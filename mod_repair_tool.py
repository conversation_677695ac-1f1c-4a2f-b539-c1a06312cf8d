# -*- coding: utf-8 -*-
import os
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog # Keep simpledialog for new extension input
import threading
import time
import json
import zipfile
from io import BytesIO
import requests
import webbrowser
from supabase import create_client, Client
# from supabase.lib.client_options import StorageException # For specific Supabase storage errors -> Removed due to import issues
import re
import shutil
from urllib.parse import urlparse
from PIL import Image, ImageTk

# --- NEW: Gemini Import ---
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("Warning: google-generativeai library not found. AI repair features will be disabled.")
    print("Install it using: pip install google-generativeai")

try:
    import pyperclip
    PYPERCLIP_AVAILABLE = True
except ImportError:
    PYPERCLIP_AVAILABLE = False
    print("Warning: pyperclip library not found. Copy log button will be disabled.")
    print("Install it using: pip install pyperclip")


# --- Supabase Configurations ---
STORAGE_URL = "https://ytqxxodyecdeosnqoure.supabase.co"
STORAGE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4"
MOD_BUCKET = "my_new_mods_bucket"
APP_DB_URL = 'https://ytqxxodyecdeosnqoure.supabase.co'
APP_DB_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4"
MODS_TABLE_NAME = 'mods'

# --- Initialize Supabase Clients ---
storage_client: Client | None = None
app_db_client: Client | None = None
STORAGE_CLIENT_OK = False
APP_DB_CLIENT_OK = False

try:
    if STORAGE_URL and STORAGE_KEY:
        storage_client = create_client(STORAGE_URL, STORAGE_KEY)
        print("✅ Supabase storage client initialized for repair tool.")
        STORAGE_CLIENT_OK = True
    else:
        print("❌ Missing STORAGE_URL or STORAGE_KEY configuration.")
except Exception as e:
    print(f"❌ Error initializing Supabase storage client for repair tool: {e}")
    print(f"   URL: {STORAGE_URL}")
    print(f"   Key length: {len(STORAGE_KEY)} characters")

try:
    if APP_DB_URL and APP_DB_KEY:
        app_db_client = create_client(APP_DB_URL, APP_DB_KEY)
        print("✅ Supabase App DB client initialized for repair tool.")
        APP_DB_CLIENT_OK = True
    else:
        print("❌ Missing APP_DB_URL or APP_DB_KEY configuration.")
except Exception as e:
    print(f"❌ Error initializing Supabase App DB client for repair tool: {e}")
    print(f"   URL: {APP_DB_URL}")
    print(f"   Key length: {len(APP_DB_KEY)} characters")

# --- Enhanced Gemini Configuration for Repair Tool ---
REPAIR_GEMINI_API_KEYS = [
    os.environ.get("GEMINI_API_KEY", ""),  # المفتاح الافتراضي
    "AIzaSyD7E2B9iYXV9_2houLLHDWXUA-K53kUGq0",
    "AIzaSyBY58cZFvFzRQpzzYJ7m1VjwvS7af-vHhM",
    "AIzaSyBSbEdSARy5ims96kxF1om2725VZxwl6nU",
    "AIzaSyBx2D9UdlvuSCH4Z7jaT16S0MREkNIuVNc",
    "AIzaSyAuI06np4vmdKkWN1JucexLW1mO0ESEyts",
    "AIzaSyC4_fX42vOZYfuF56i_lNSJjiEs02vX3Uo",
    "AIzaSyDZfiLXBVs8yBk0CDb4hvLZ_l8P6tKy6og",
    "AIzaSyC_3oOs766IXUlWFdwUSNTVAhg_GLFfb1E",
    "AIzaSyA9sCmQsOZIPKRKX14aMJsC8Mt7IFPsYE8",
    "AIzaSyD_YyLNsLeKeFYUX7KvTw5fVJyneWMNjl8",
    "AIzaSyD1c3SzOGL4M8O1qZ6AejUy7jm567Nbf5w",
    "AIzaSyBSeMPla9eDR2VCmgS0fub-EujNsLO7EDk",
    "AIzaSyCxWJz_Cybce02eEXZrOnvZcEaD9yiFIQI",
    "AIzaSyAq6w_rvoIiZLxX191-UZDrhibbA6adDAw",
    "AIzaSyCzpJ4I958670qvycQgL9oay1Mpjp1q3zA",
    "AIzaSyCJajG5U6RH9KPMaZYXAcMGNldpOunf4nU",
    "AIzaSyDtfQhkANpIM-cHRy-UdMhHfROzSjBX5SY",
    "AIzaSyD-461tFFQsQcOxadaAQVWG1VN5ZvCxAYU",
    "AIzaSyAGoM8irNjhKPZcU8hBv1rpLpIhhy5eZ0E",
    "AIzaSyCyjFbianBAZ3eYOqIH2Yf0J70FUgWh5wg",
    "AIzaSyASQK3r61z8OKrW6WIru6DZt3T0NFwkPCI",
    "AIzaSyCxhQrKeoPrgZSQshH_Ij4sqLNxbjVuIDE",
    "AIzaSyCO9ok1iCqfFbtVyDJdVpK_JoLraNS6aHU",
    "AIzaSyAV3uTUtTKlSHgU3cge20vnQbusNeM-Wxk",
    "AIzaSyAnnYx15P89izzx_rJy9en4kaLVJ_Nuk40",
    "AIzaSyAhpl7qAaouwRb6niHi9UMNnOxQVzTZXeo",
    "AIzaSyCeSyykhVy6BXB_Do-V934k--VxwvdWAok",
    "AIzaSyDLlAe9XxHdbJ8affKNqWKD5509E0roQ2E",
    "AIzaSyC2Q_XqKuefQ0badweq13D5mjLVMhmA6BU",
    "AIzaSyA5W4wz0mpnajLAzHj3w_X_U9ACNr0Web4",
    "AIzaSyChdkhUNWaXlDDyGN7Zy-DEHOTHw79guH8",
    "AIzaSyB8ZRBRJ-ouTxBHVzOXZaCjD_z2PfZzYFE",
    "AIzaSyAMbRJtbmTa8xjGJuIX3wUcvIwSFuMB5gM",
    "AIzaSyAjHe3qCwNjcaEEie9ZC84gMIr2dy5NR28",
    "AIzaSyClKt_GqxphM-5aFYUMLzT925OgTtxxIhY",
    "AIzaSyBDASshsMMS7K17y9j6I0A7amdnOEJQ78Q",
    "AIzaSyC5F-PHaWU9yb4_mv_K7E6RbrhOJyYS5-U",
    "AIzaSyAERiSzXO9roZ49OLyd9FvhezU5u193c2g",
    "AIzaSyAQuKvRKMP48MB2RePpur8Wp6f5iYBpkaA",
    "AIzaSyBnxTW0uaIjIacKMHShqsjL_EMzk6NribQ",
    "AIzaSyDDK44eqIOF7rfz2RTscDRoAbk7IPwaKt8",
    "AIzaSyBIlkoOdvE7oCP8oJlXUrE2YcbFCp5Ioyo",
    "AIzaSyBELBROWW0EBVVmuTNvt32sD6hbj0Tq6MU",
    "AIzaSyBGYJPjJ5aycTP5i--CXPKXe53wuA1jHgE",
    "AIzaSyD9XM4KEqt_VmzBbLqCROtaydXICu7-ymw",
    "AIzaSyCulDVPsLQXChqpbYVERipHvdOGohhpMP0",
    "AIzaSyA5gcq8vv2jtVx1Ew9HWXGh4rtnDh7dXhU",
    "AIzaSyBOPeEmj0j2svyoAyqMwpFYr4Iy2qYliEk",
    "AIzaSyAybDJWXToHHzg9VUERbNwnGc7c6l6GNqE",
    "AIzaSyBstNKoy12AYAGy7rxfNK2x-wK-gvaVFEA"
]
REPAIR_GEMINI_MODEL_NAME = "models/gemini-2.0-flash-exp" # أو "models/gemini-pro"
repair_gemini_model = None
REPAIR_GEMINI_CLIENT_OK = False
current_repair_gemini_key_index = 0
CONFIG_FILE = "repair_tool_config.json"

# --- Enhanced API Key Management ---
api_key_status = {}  # تتبع حالة كل مفتاح API
api_key_usage_count = {}  # عدد الاستخدامات لكل مفتاح
api_key_last_error = {}  # آخر خطأ لكل مفتاح
api_key_cooldown = {}  # وقت التبريد لكل مفتاح
MAX_RETRIES_PER_KEY = 3  # عدد المحاولات القصوى لكل مفتاح
COOLDOWN_DURATION = 300  # مدة التبريد بالثواني (5 دقائق)
MAX_DAILY_USAGE_PER_KEY = 1000  # الحد الأقصى للاستخدام اليومي لكل مفتاح

def load_repair_config():
    """تحميل إعدادات أداة الإصلاح"""
    global REPAIR_GEMINI_API_KEYS, api_key_status, api_key_usage_count, api_key_last_error, api_key_cooldown
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                REPAIR_GEMINI_API_KEYS = config.get('gemini_api_keys', REPAIR_GEMINI_API_KEYS)
                api_key_status = config.get('api_key_status', {})
                api_key_usage_count = config.get('api_key_usage_count', {})
                api_key_last_error = config.get('api_key_last_error', {})
                api_key_cooldown = config.get('api_key_cooldown', {})

                # تنظيف البيانات القديمة (أكثر من يوم واحد)
                cleanup_old_api_data()
    except Exception as e:
        print(f"خطأ في تحميل إعدادات أداة الإصلاح: {e}")

def save_repair_config():
    """حفظ إعدادات أداة الإصلاح"""
    try:
        config = {
            'gemini_api_keys': REPAIR_GEMINI_API_KEYS,
            'api_key_status': api_key_status,
            'api_key_usage_count': api_key_usage_count,
            'api_key_last_error': api_key_last_error,
            'api_key_cooldown': api_key_cooldown
        }
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"خطأ في حفظ إعدادات أداة الإصلاح: {e}")

def cleanup_old_api_data():
    """تنظيف البيانات القديمة للمفاتيح"""
    global api_key_usage_count, api_key_cooldown, api_key_last_error

    current_time = time.time()
    current_date = time.strftime("%Y-%m-%d")

    # تنظيف عدادات الاستخدام اليومية
    keys_to_remove = []
    for key_id, usage_data in api_key_usage_count.items():
        if isinstance(usage_data, dict) and usage_data.get('date') != current_date:
            keys_to_remove.append(key_id)

    for key_id in keys_to_remove:
        api_key_usage_count[key_id] = {'date': current_date, 'count': 0}

    # تنظيف أوقات التبريد المنتهية
    cooldown_keys_to_remove = []
    for key_id, cooldown_time in api_key_cooldown.items():
        if current_time > cooldown_time:
            cooldown_keys_to_remove.append(key_id)

    for key_id in cooldown_keys_to_remove:
        del api_key_cooldown[key_id]

def get_api_key_id(api_key):
    """الحصول على معرف فريد للمفتاح"""
    return api_key[-8:] if len(api_key) > 8 else api_key

def is_api_key_available(api_key):
    """فحص ما إذا كان المفتاح متاحاً للاستخدام"""
    key_id = get_api_key_id(api_key)
    current_time = time.time()
    current_date = time.strftime("%Y-%m-%d")

    # فحص وقت التبريد
    if key_id in api_key_cooldown and current_time < api_key_cooldown[key_id]:
        return False

    # فحص الحد اليومي
    if key_id in api_key_usage_count:
        usage_data = api_key_usage_count[key_id]
        if isinstance(usage_data, dict):
            if usage_data.get('date') == current_date and usage_data.get('count', 0) >= MAX_DAILY_USAGE_PER_KEY:
                return False
        elif usage_data >= MAX_DAILY_USAGE_PER_KEY:  # للتوافق مع البيانات القديمة
            return False

    return True

def increment_api_key_usage(api_key):
    """زيادة عداد استخدام المفتاح"""
    key_id = get_api_key_id(api_key)
    current_date = time.strftime("%Y-%m-%d")

    if key_id not in api_key_usage_count:
        api_key_usage_count[key_id] = {'date': current_date, 'count': 0}

    usage_data = api_key_usage_count[key_id]
    if isinstance(usage_data, dict):
        if usage_data.get('date') != current_date:
            usage_data = {'date': current_date, 'count': 0}
        usage_data['count'] += 1
        api_key_usage_count[key_id] = usage_data
    else:  # للتوافق مع البيانات القديمة
        api_key_usage_count[key_id] = {'date': current_date, 'count': 1}

def set_api_key_cooldown(api_key, error_message=""):
    """وضع المفتاح في وقت تبريد"""
    key_id = get_api_key_id(api_key)
    current_time = time.time()

    api_key_cooldown[key_id] = current_time + COOLDOWN_DURATION
    api_key_last_error[key_id] = {
        'time': current_time,
        'error': error_message,
        'date': time.strftime("%Y-%m-%d %H:%M:%S")
    }

    print(f"🕒 تم وضع المفتاح {key_id} في وقت تبريد لمدة {COOLDOWN_DURATION//60} دقائق")

def get_best_available_api_key():
    """الحصول على أفضل مفتاح متاح للاستخدام"""
    available_keys = []

    for i, api_key in enumerate(REPAIR_GEMINI_API_KEYS):
        if is_api_key_available(api_key):
            key_id = get_api_key_id(api_key)
            usage_count = 0

            if key_id in api_key_usage_count:
                usage_data = api_key_usage_count[key_id]
                if isinstance(usage_data, dict):
                    usage_count = usage_data.get('count', 0)
                else:
                    usage_count = usage_data

            available_keys.append((i, api_key, usage_count))

    if not available_keys:
        return None

    # ترتيب المفاتيح حسب الاستخدام (الأقل استخداماً أولاً)
    available_keys.sort(key=lambda x: x[2])
    return available_keys[0][0]  # إرجاع فهرس أفضل مفتاح

def configure_repair_gemini_client(key_index=None):
    """تكوين عميل Gemini للإصلاح مع النظام الذكي لإدارة المفاتيح"""
    global repair_gemini_model, REPAIR_GEMINI_CLIENT_OK, current_repair_gemini_key_index

    if not GEMINI_AVAILABLE:
        REPAIR_GEMINI_CLIENT_OK = False
        return False

    if not REPAIR_GEMINI_API_KEYS:
        update_status("!!! تحذير: لا توجد مفاتيح Gemini API. ميزات الإصلاح الذكي معطلة.")
        REPAIR_GEMINI_CLIENT_OK = False
        return False

    # إذا لم يتم تحديد فهرس، استخدم أفضل مفتاح متاح
    if key_index is None:
        key_index = get_best_available_api_key()
        if key_index is None:
            print("!!! جميع مفاتيح Gemini API غير متاحة حالياً (في وقت تبريد أو وصلت للحد اليومي).")
            REPAIR_GEMINI_CLIENT_OK = False
            return False

    if key_index >= len(REPAIR_GEMINI_API_KEYS):
        print("!!! فهرس المفتاح غير صالح.")
        REPAIR_GEMINI_CLIENT_OK = False
        return False

    api_key = REPAIR_GEMINI_API_KEYS[key_index]
    key_id = get_api_key_id(api_key)

    # فحص ما إذا كان المفتاح متاحاً
    if not is_api_key_available(api_key):
        print(f"⏳ المفتاح {key_id} غير متاح حالياً (في وقت تبريد أو وصل للحد اليومي)")
        # جرب المفتاح التالي المتاح
        next_key_index = get_best_available_api_key()
        if next_key_index is not None and next_key_index != key_index:
            return configure_repair_gemini_client(next_key_index)
        else:
            REPAIR_GEMINI_CLIENT_OK = False
            return False

    try:
        genai.configure(api_key=api_key)
        repair_gemini_model = genai.GenerativeModel(REPAIR_GEMINI_MODEL_NAME)

        # اختبار المفتاح
        test_response = repair_gemini_model.generate_content("Test")
        if test_response and test_response.text:
            print(f"✅ تم تكوين Gemini للإصلاح بنجاح باستخدام المفتاح {key_id}")
            REPAIR_GEMINI_CLIENT_OK = True
            current_repair_gemini_key_index = key_index

            # تحديث حالة المفتاح كنشط
            api_key_status[key_id] = 'active'
            save_repair_config()
            return True
        else:
            print(f"❌ فشل اختبار المفتاح {key_id}")
            set_api_key_cooldown(api_key, "فشل في اختبار المفتاح")
            REPAIR_GEMINI_CLIENT_OK = False

            # جرب المفتاح التالي المتاح
            next_key_index = get_best_available_api_key()
            if next_key_index is not None:
                return configure_repair_gemini_client(next_key_index)
            return False

    except Exception as e:
        error_msg = str(e)
        print(f"❌ خطأ في تكوين Gemini بالمفتاح {key_id}: {error_msg}")

        # تحليل نوع الخطأ
        if any(keyword in error_msg.upper() for keyword in ["QUOTA", "RATE_LIMIT", "RESOURCE_EXHAUSTED", "429"]):
            set_api_key_cooldown(api_key, f"حد الاستخدام: {error_msg}")
        elif any(keyword in error_msg.upper() for keyword in ["API_KEY", "INVALID", "PERMISSION", "401", "403"]):
            set_api_key_cooldown(api_key, f"مفتاح غير صالح: {error_msg}")
        else:
            set_api_key_cooldown(api_key, f"خطأ عام: {error_msg}")

        REPAIR_GEMINI_CLIENT_OK = False
        save_repair_config()

        # جرب المفتاح التالي المتاح
        next_key_index = get_best_available_api_key()
        if next_key_index is not None:
            return configure_repair_gemini_client(next_key_index)
        return False

def smart_repair_gemini_request(prompt, max_retries=3):
    """طلب ذكي لـ Gemini مع إعادة المحاولة والتبديل التلقائي للمفاتيح"""
    global repair_gemini_model, REPAIR_GEMINI_CLIENT_OK, current_repair_gemini_key_index

    if not REPAIR_GEMINI_CLIENT_OK or not repair_gemini_model:
        # محاولة إعادة التكوين
        if not configure_repair_gemini_client():
            return None

    current_api_key = REPAIR_GEMINI_API_KEYS[current_repair_gemini_key_index] if current_repair_gemini_key_index < len(REPAIR_GEMINI_API_KEYS) else None

    for attempt in range(max_retries):
        try:
            # زيادة عداد الاستخدام قبل الطلب
            if current_api_key:
                increment_api_key_usage(current_api_key)
                save_repair_config()

            response = repair_gemini_model.generate_content(prompt)
            if response and response.text:
                return response.text.strip() if response.text else ""
            else:
                print(f"⚠️ استجابة فارغة من Gemini (المحاولة {attempt + 1})")

        except Exception as e:
            error_msg = str(e)
            print(f"❌ خطأ في Gemini (المحاولة {attempt + 1}): {error_msg}")

            # تحليل نوع الخطأ وتطبيق الإجراء المناسب
            is_quota_error = any(keyword in error_msg.upper() for keyword in ["QUOTA", "RATE_LIMIT", "RESOURCE_EXHAUSTED", "429"])
            is_auth_error = any(keyword in error_msg.upper() for keyword in ["API_KEY", "INVALID", "PERMISSION", "401", "403"])
            is_length_error = any(keyword in error_msg.upper() for keyword in ["TOO_LONG", "LENGTH", "TOKENS"])

            if is_quota_error or is_auth_error:
                # وضع المفتاح الحالي في وقت تبريد
                if current_api_key:
                    set_api_key_cooldown(current_api_key, error_msg)
                    save_repair_config()

                # محاولة التبديل إلى مفتاح آخر
                if try_next_repair_gemini_key():
                    current_api_key = REPAIR_GEMINI_API_KEYS[current_repair_gemini_key_index]
                    print(f"🔄 تم التبديل إلى مفتاح جديد: {get_api_key_id(current_api_key)}")
                    continue  # أعد المحاولة بالمفتاح الجديد
                else:
                    print("❌ لا توجد مفاتيح أخرى متاحة")
                    break  # لا توجد مفاتيح أخرى صالحة

            elif is_length_error:
                print("⚠️ النص طويل جداً، محاولة التبديل إلى مفتاح آخر...")
                # وضع المفتاح الحالي في وقت تبريد
                if current_api_key:
                    set_api_key_cooldown(current_api_key, error_msg)
                    save_repair_config()

                # محاولة التبديل إلى مفتاح آخر
                if try_next_repair_gemini_key():
                    current_api_key = REPAIR_GEMINI_API_KEYS[current_repair_gemini_key_index]
                    print(f"🔄 تم التبديل إلى مفتاح جديد: {get_api_key_id(current_api_key)}")
                    continue  # أعد المحاولة بالمفتاح الجديد
                else:
                    print("❌ لا توجد مفاتيح أخرى متاحة")
                    break  # لا توجد مفاتيح أخرى صالحة

            else:
                # للأخطاء الأخرى، انتظر قليلاً قبل إعادة المحاولة
                if attempt < max_retries - 1:
                    print(f"⏳ انتظار {2 * (attempt + 1)} ثانية قبل إعادة المحاولة...")
                    time.sleep(2 * (attempt + 1))

    return None

def try_next_repair_gemini_key():
    """محاولة التبديل إلى أفضل مفتاح متاح عند فشل المفتاح الحالي"""
    global current_repair_gemini_key_index, REPAIR_GEMINI_API_KEYS

    if not REPAIR_GEMINI_API_KEYS or len(REPAIR_GEMINI_API_KEYS) <= 1:
        return False

    # الحصول على أفضل مفتاح متاح (غير المفتاح الحالي)
    best_key_index = get_best_available_api_key()

    if best_key_index is None:
        print("❌ لا توجد مفاتيح متاحة للتبديل")
        return False

    if best_key_index == current_repair_gemini_key_index:
        # إذا كان أفضل مفتاح هو نفس المفتاح الحالي، ابحث عن البديل التالي
        available_keys = []
        for i, api_key in enumerate(REPAIR_GEMINI_API_KEYS):
            if i != current_repair_gemini_key_index and is_api_key_available(api_key):
                key_id = get_api_key_id(api_key)
                usage_count = 0

                if key_id in api_key_usage_count:
                    usage_data = api_key_usage_count[key_id]
                    if isinstance(usage_data, dict):
                        usage_count = usage_data.get('count', 0)
                    else:
                        usage_count = usage_data

                available_keys.append((i, api_key, usage_count))

        if not available_keys:
            print("❌ لا توجد مفاتيح بديلة متاحة")
            return False

        # ترتيب المفاتيح حسب الاستخدام (الأقل استخداماً أولاً)
        available_keys.sort(key=lambda x: x[2])
        best_key_index = available_keys[0][0]

    # محاولة التكوين بالمفتاح الجديد
    if configure_repair_gemini_client(best_key_index):
        key_id = get_api_key_id(REPAIR_GEMINI_API_KEYS[best_key_index])
        print(f"✅ تم التبديل إلى مفتاح Gemini {key_id} بنجاح")
        update_status(f"تم التبديل إلى مفتاح Gemini {key_id} بنجاح")
        return True
    else:
        print(f"❌ فشل في التبديل إلى المفتاح {best_key_index + 1}")
        return False

# --- Custom Description Generation Prompt ---
CUSTOM_DESCRIPTION_PROMPT = """
أنشئ وصفًا لهذا المود في ماين كرافت باللغتين العربية والإنجليزية، بحيث تكون الإنجليزية باللهجة البريطانية والعربية باللهجة السعودية.

معلومات المود:
اسم المود: {mod_name}
الفئة: {mod_category}
الوصف الإنجليزي الحالي (إذا وجد): {existing_english_description}

تعرف على اذا كان شادر او تكستر باك او سكن او مود باك او ماب وألخ وقم بإنشاء وصف بالمتطلبات التالية:
1. أن تكون الأوصاف طبيعية وكأن بشري مراهق يحب ماين كرافت قام بكتابتها
2. كل وصف يجب أن يكون ما بين 300 إلى 400 حرفًا
3. تضمين 2 إلى 3 رموز تعبيرية مناسبة في كل وصف
4. التركيز على الميزات والفوائد الأساسية لمود او شادر او تكستر باك او ماب
5. استخدام لغة غير رسمية تشبه أسلوب البشر (بلهجة بريطانية في الإنجليزية، ولهجة سعودية في العربية)
6. تجنب اللغة الرسمية أو العبارات التي تبدو كأنها من الذكاء الاصطناعي
7. تجنب هاذه الكلمات (تخيل) (تجربة لعب) (اضافة) (عش تجربة) (عش مغامرة)
8. استعمل كلمات بلهجة مصرية، سعودية، عراقية أحياناً، وبريطانية أمريكية لوصف إنجليزي.
9. اذكر في بداية هاذا مود او شادر او تكتسر باك او ماب او دا اليوم جبتلكم.. او اليوم راح اقدم لكم... جبتلكم... او حبيت اقدم لكم... وكثير وكثير من عبارات اخرى متنوعة
10. لا تستعمل عبارات مثل مرحبا شباب، اليوم سوق اقدم لكم،
11. يجب ان يكون وصف بطابع بشري قدر الإمكان

نسّق ردك على شكل كائن JSON صالح بالهيكل التالي تمامًا:
{{
  "ar": "الوصف العربي هنا مع الإيموجيات",
  "en": "الوصف الإنجليزي هنا مع الإيموجيات"
}}

أعد فقط كائن الـ JSON، ولا تضف أي شيء آخر.
"""

# --- NEW: Telegram-Specific Description Prompt ---
TELEGRAM_CUSTOM_DESCRIPTION_PROMPT = """
أنشئ وصفًا مباشرًا لهذا المود في ماين كرافت باللغتين العربية والإنجليزية.

معلومات المود:
اسم المود: {mod_name}
الفئة: {mod_category}
الوصف الإنجليزي الحالي: {existing_english_description}

متطلبات الوصف:
1. وصف مباشر بدون تعليمات أو خيارات متعددة
2. طول الوصف: 300-400 حرف لكل لغة
3. استخدام 2-3 إيموجيات مناسبة
4. لهجة طبيعية وبشرية (سعودية للعربية، بريطانية للإنجليزية)
5. تجنب الكلمات: "تخيل"، "تجربة لعب"، "إضافة"، "عش تجربة"، "عش مغامرة"
6. بداية متنوعة مثل: "جبتلكم"، "حبيت أقدم لكم"، "اليوم راح أعرض عليكم"
7. تجنب: "مرحبا شباب"، "اليوم سوف أقدم لكم"
7. استخدم اسلوب مراهق يحب ماين كرافت

أعد فقط JSON بالشكل التالي:
{{
"ar": "الوصف العربي المباشر",
"en": "الوصف الإنجليزي المباشر"
}}
"""

# --- Global Variables ---
selected_mods_for_repair = [] # Changed from selected_mod_for_repair = None
loaded_mods_data_list = [] # This will store ALL mods fetched from Supabase
currently_visible_mod_data = [] # This will store mods currently visible in the listbox after filtering
current_category_filter = "All" # Default filter
LOCAL_REPAIRED_MODS_DIR = "repaired_mods_output"

# ملفات تتبع المودات المعيبة
PROBLEMATIC_MODS_FILE = "problematic_mods_tracking.json"
FIXED_MODS_FILE = "fixed_mods_history.json"

# --- GUI Variables ---
upload_after_repair_var = None # tk.BooleanVar() for the checkbox
select_all_button = None # Added for global access
deselect_all_button = None # Added for global access
repair_mod_button = None # Added for global access
change_format_button = None # Added for global access
replace_mod_button = None # Added for replacing mod files
generate_description_button = None # Added for description generation
manage_api_keys_button = None # Added for API key management

# --- Description Management Functions ---
def find_mods_missing_descriptions():
    """العثور على المودات التي تفتقر للأوصاف الأساسية وأوصاف التيليجرام"""
    global loaded_mods_data_list

    if not loaded_mods_data_list:
        messagebox.showwarning("لا توجد بيانات", "الرجاء تحميل قائمة المودات أولاً.")
        return

    # الأوصاف الأساسية
    missing_basic_english = []
    missing_basic_arabic = []
    missing_basic_both = []

    # أوصاف التيليجرام
    missing_telegram_english = []
    missing_telegram_arabic = []
    missing_telegram_both = []

    for mod in loaded_mods_data_list:
        # فحص الأوصاف الأساسية
        description_en = mod.get('description', '')
        description_ar = mod.get('description_ar', '')
        has_basic_english = bool(description_en and description_en.strip() and len(description_en.strip()) > 10)
        has_basic_arabic = bool(description_ar and description_ar.strip() and len(description_ar.strip()) > 10)

        # فحص أوصاف التيليجرام
        telegram_description_en = mod.get('telegram_description_en', '')
        telegram_description_ar = mod.get('telegram_description_ar', '')
        has_telegram_english = bool(telegram_description_en and telegram_description_en.strip() and len(telegram_description_en.strip()) > 10)
        has_telegram_arabic = bool(telegram_description_ar and telegram_description_ar.strip() and len(telegram_description_ar.strip()) > 10)

        # تصنيف الأوصاف الأساسية
        if not has_basic_english and not has_basic_arabic:
            missing_basic_both.append(mod)
        elif not has_basic_english:
            missing_basic_english.append(mod)
        elif not has_basic_arabic:
            missing_basic_arabic.append(mod)

        # تصنيف أوصاف التيليجرام
        if not has_telegram_english and not has_telegram_arabic:
            missing_telegram_both.append(mod)
        elif not has_telegram_english:
            missing_telegram_english.append(mod)
        elif not has_telegram_arabic:
            missing_telegram_arabic.append(mod)

    # إنشاء نافذة لعرض النتائج
    results_window = tk.Toplevel()
    results_window.title("إدارة أوصاف المودات")
    results_window.geometry("1000x700")
    results_window.resizable(True, True)

    # إنشاء إطار رئيسي مع تبويبات
    notebook = ttk.Notebook(results_window)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # === تبويبات الأوصاف الأساسية ===
    # تبويب للمودات التي تفتقر لكلا الوصفين الأساسيين
    basic_both_frame = ttk.Frame(notebook)
    notebook.add(basic_both_frame, text=f"بدون أي وصف أساسي ({len(missing_basic_both)})")
    create_descriptions_list_tab(basic_both_frame, missing_basic_both, "لا يوجد وصف أساسي إنجليزي أو عربي", "basic")

    # تبويب للمودات التي تفتقر للوصف الأساسي الإنجليزي
    basic_english_frame = ttk.Frame(notebook)
    notebook.add(basic_english_frame, text=f"بدون وصف أساسي إنجليزي ({len(missing_basic_english)})")
    create_descriptions_list_tab(basic_english_frame, missing_basic_english, "يوجد وصف أساسي عربي فقط", "basic")

    # تبويب للمودات التي تفتقر للوصف الأساسي العربي
    basic_arabic_frame = ttk.Frame(notebook)
    notebook.add(basic_arabic_frame, text=f"بدون وصف أساسي عربي ({len(missing_basic_arabic)})")
    create_descriptions_list_tab(basic_arabic_frame, missing_basic_arabic, "يوجد وصف أساسي إنجليزي فقط", "basic")

    # === تبويبات أوصاف التيليجرام ===
    # تبويب للمودات التي تفتقر لكلا وصفي التيليجرام
    telegram_both_frame = ttk.Frame(notebook)
    notebook.add(telegram_both_frame, text=f"بدون أي وصف تيليجرام ({len(missing_telegram_both)})")
    create_descriptions_list_tab(telegram_both_frame, missing_telegram_both, "لا يوجد وصف تيليجرام إنجليزي أو عربي", "telegram")

    # تبويب للمودات التي تفتقر لوصف التيليجرام الإنجليزي
    telegram_english_frame = ttk.Frame(notebook)
    notebook.add(telegram_english_frame, text=f"بدون وصف تيليجرام إنجليزي ({len(missing_telegram_english)})")
    create_descriptions_list_tab(telegram_english_frame, missing_telegram_english, "يوجد وصف تيليجرام عربي فقط", "telegram")

    # تبويب للمودات التي تفتقر لوصف التيليجرام العربي
    telegram_arabic_frame = ttk.Frame(notebook)
    notebook.add(telegram_arabic_frame, text=f"بدون وصف تيليجرام عربي ({len(missing_telegram_arabic)})")
    create_descriptions_list_tab(telegram_arabic_frame, missing_telegram_arabic, "يوجد وصف تيليجرام إنجليزي فقط", "telegram")

    # إضافة معلومات إجمالية
    info_frame = ttk.Frame(results_window)
    info_frame.pack(fill=tk.X, padx=10, pady=5)

    total_mods = len(loaded_mods_data_list)
    total_basic_issues = len(missing_basic_both) + len(missing_basic_english) + len(missing_basic_arabic)
    total_telegram_issues = len(missing_telegram_both) + len(missing_telegram_english) + len(missing_telegram_arabic)

    info_text = f"إجمالي المودات: {total_mods} | مشاكل الأوصاف الأساسية: {total_basic_issues} | مشاكل أوصاف التيليجرام: {total_telegram_issues}"
    ttk.Label(info_frame, text=info_text, font=("Arial", 10, "bold")).pack()

def create_descriptions_list_tab(parent_frame, mods_list, description, description_type):
    """إنشاء تبويب لعرض قائمة المودات مع دعم الأوصاف الأساسية وأوصاف التيليجرام"""

    # إطار للوصف
    desc_frame = ttk.Frame(parent_frame)
    desc_frame.pack(fill=tk.X, padx=5, pady=5)
    ttk.Label(desc_frame, text=description, font=("Arial", 10)).pack()

    # إطار للقائمة
    list_frame = ttk.Frame(parent_frame)
    list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # إنشاء قائمة مع شريط تمرير
    listbox_frame = ttk.Frame(list_frame)
    listbox_frame.pack(fill=tk.BOTH, expand=True)

    scrollbar = ttk.Scrollbar(listbox_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set, selectmode=tk.EXTENDED)
    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.config(command=listbox.yview)

    # ملء القائمة
    for mod in mods_list:
        mod_name = mod.get('name', 'بدون اسم')
        mod_id = mod.get('id', 'N/A')
        category = mod.get('category', 'غير مصنف')

        # إنشاء حالة الأوصاف حسب النوع
        if description_type == "basic":
            # فحص الأوصاف الأساسية
            description_en = mod.get('description', '')
            description_ar = mod.get('description_ar', '')
            has_english = bool(description_en and description_en.strip() and len(description_en.strip()) > 10)
            has_arabic = bool(description_ar and description_ar.strip() and len(description_ar.strip()) > 10)

            status = ""
            if has_english and has_arabic:
                status = "📝 (EN+AR)"
            elif has_english:
                status = "📝 (EN)"
            elif has_arabic:
                status = "📝 (AR)"
            else:
                status = "❌"

        elif description_type == "telegram":
            # فحص أوصاف التيليجرام
            telegram_description_en = mod.get('telegram_description_en', '')
            telegram_description_ar = mod.get('telegram_description_ar', '')
            has_telegram_english = bool(telegram_description_en and telegram_description_en.strip() and len(telegram_description_en.strip()) > 10)
            has_telegram_arabic = bool(telegram_description_ar and telegram_description_ar.strip() and len(telegram_description_ar.strip()) > 10)

            status = ""
            if has_telegram_english and has_telegram_arabic:
                status = "📱 (EN+AR)"
            elif has_telegram_english:
                status = "📱 (EN)"
            elif has_telegram_arabic:
                status = "📱 (AR)"
            else:
                status = "❌"

        display_text = f"ID: {mod_id} - {mod_name} ({category}) {status}"
        listbox.insert(tk.END, display_text)

    # إطار للأزرار
    buttons_frame = ttk.Frame(parent_frame)
    buttons_frame.pack(fill=tk.X, padx=5, pady=5)

    def select_all_in_tab():
        listbox.select_set(0, tk.END)

    def generate_descriptions_for_selected():
        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("لا يوجد تحديد", "الرجاء تحديد مود واحد على الأقل.")
            return

        selected_mods = [mods_list[i] for i in selected_indices]

        if description_type == "basic":
            handle_batch_description_generation(selected_mods, description_type="normal")
        elif description_type == "telegram":
            handle_batch_description_generation(selected_mods, description_type="telegram")

    def view_selected_mod():
        selected_indices = listbox.curselection()
        if len(selected_indices) != 1:
            messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط للعرض.")
            return

        mod = mods_list[selected_indices[0]]
        if description_type == "basic":
            show_mod_details(mod)
        elif description_type == "telegram":
            show_mod_telegram_details(mod)

    ttk.Button(buttons_frame, text="تحديد الكل", command=select_all_in_tab).pack(side=tk.LEFT, padx=5)

    if description_type == "basic":
        ttk.Button(buttons_frame, text="إنشاء أوصاف أساسية للمحدد", command=generate_descriptions_for_selected).pack(side=tk.LEFT, padx=5)
    elif description_type == "telegram":
        ttk.Button(buttons_frame, text="إنشاء أوصاف تيليجرام للمحدد", command=generate_descriptions_for_selected).pack(side=tk.LEFT, padx=5)

    ttk.Button(buttons_frame, text="عرض تفاصيل المود", command=view_selected_mod).pack(side=tk.LEFT, padx=5)

def show_mod_telegram_details(mod):
    """عرض تفاصيل أوصاف التيليجرام للمود مع إمكانية التعديل"""

    details_window = tk.Toplevel()
    details_window.title(f"أوصاف التيليجرام: {mod.get('name', 'بدون اسم')}")
    details_window.geometry("800x700")
    details_window.resizable(True, True)

    # إطار رئيسي مع تمرير
    main_frame = ttk.Frame(details_window)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # معلومات أساسية
    info_frame = ttk.LabelFrame(main_frame, text="معلومات أساسية", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 10))

    ttk.Label(info_frame, text=f"ID: {mod.get('id', 'N/A')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الاسم: {mod.get('name', 'بدون اسم')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الفئة: {mod.get('category', 'غير مصنف')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الإصدار: {mod.get('version', 'غير محدد')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"التحميلات: {mod.get('downloads', 0)}").pack(anchor="w")

    # الوصف التيليجرام الإنجليزي
    english_frame = ttk.LabelFrame(main_frame, text="وصف التيليجرام الإنجليزي", padding=10)
    english_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    english_text = scrolledtext.ScrolledText(english_frame, height=8, wrap=tk.WORD)
    english_text.pack(fill=tk.BOTH, expand=True)

    current_english = mod.get('telegram_description_en', '')
    english_text.insert(tk.END, current_english)

    # الوصف التيليجرام العربي
    arabic_frame = ttk.LabelFrame(main_frame, text="وصف التيليجرام العربي", padding=10)
    arabic_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    arabic_text = scrolledtext.ScrolledText(arabic_frame, height=8, wrap=tk.WORD)
    arabic_text.pack(fill=tk.BOTH, expand=True)

    current_arabic = mod.get('telegram_description_ar', '')
    arabic_text.insert(tk.END, current_arabic)

    # أزرار العمليات
    actions_frame = ttk.Frame(main_frame)
    actions_frame.pack(fill=tk.X, pady=(10, 0))

    def generate_telegram_english_from_arabic():
        arabic_desc = arabic_text.get("1.0", tk.END)
        arabic_desc = arabic_desc.strip() if arabic_desc else ""
        if not arabic_desc:
            messagebox.showwarning("لا يوجد محتوى", "لا يوجد وصف تيليجرام عربي لترجمته.")
            return

        generate_telegram_description_from_existing(mod, arabic_desc, "arabic_to_english", english_text)

    def generate_telegram_arabic_from_english():
        english_desc = english_text.get("1.0", tk.END)
        english_desc = english_desc.strip() if english_desc else ""
        if not english_desc:
            messagebox.showwarning("لا يوجد محتوى", "لا يوجد وصف تيليجرام إنجليزي لترجمته.")
            return

        generate_telegram_description_from_existing(mod, english_desc, "english_to_arabic", arabic_text)

    def generate_new_telegram_descriptions():
        """إنشاء أوصاف تيليجرام جديدة"""
        update_status(f"جاري إنشاء أوصاف تيليجرام جديدة للمود: {mod.get('name', '')}")

        def generate_task():
            try:
                telegram_descriptions = generate_telegram_custom_description(mod)
                if telegram_descriptions:
                    new_english = telegram_descriptions.get('en', '')
                    new_arabic = telegram_descriptions.get('ar', '')

                    # تحديث النصوص في النافذة
                    english_text.delete("1.0", tk.END)
                    english_text.insert(tk.END, new_english)

                    arabic_text.delete("1.0", tk.END)
                    arabic_text.insert(tk.END, new_arabic)

                    update_status("✅ تم إنشاء أوصاف التيليجرام بنجاح!")
                    messagebox.showinfo("نجح الإنشاء", "تم إنشاء أوصاف التيليجرام. يمكنك الآن حفظ التغييرات.")
                else:
                    update_status("❌ فشل في إنشاء أوصاف التيليجرام")
                    messagebox.showerror("خطأ", "فشل في إنشاء أوصاف التيليجرام.")
            except Exception as e:
                update_status(f"❌ خطأ في إنشاء أوصاف التيليجرام: {e}")
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

        # تشغيل المهمة في خيط منفصل
        import threading
        thread = threading.Thread(target=generate_task)
        thread.daemon = True
        thread.start()

    def save_telegram_descriptions():
        new_english = english_text.get("1.0", tk.END)
        new_english = new_english.strip() if new_english else ""
        new_arabic = arabic_text.get("1.0", tk.END)
        new_arabic = new_arabic.strip() if new_arabic else ""

        if update_mod_telegram_descriptions(mod['id'], new_english, new_arabic):
            messagebox.showinfo("تم الحفظ", "تم حفظ أوصاف التيليجرام بنجاح!")
            details_window.destroy()
        else:
            messagebox.showerror("خطأ", "فشل في حفظ أوصاف التيليجرام.")

    ttk.Button(actions_frame, text="إنشاء إنجليزي من العربي", command=generate_telegram_english_from_arabic).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="إنشاء عربي من الإنجليزي", command=generate_telegram_arabic_from_english).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="إنشاء أوصاف جديدة", command=generate_new_telegram_descriptions).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="حفظ التغييرات", command=save_telegram_descriptions).pack(side=tk.RIGHT, padx=5)
    ttk.Button(actions_frame, text="إغلاق", command=details_window.destroy).pack(side=tk.RIGHT, padx=5)

def generate_telegram_description_from_existing(mod, existing_description, conversion_type, target_text_widget):
    """إنشاء وصف تيليجرام جديد بناءً على وصف موجود"""

    if not REPAIR_GEMINI_CLIENT_OK:
        messagebox.showerror("خطأ", "Gemini غير متوفر. الرجاء التحقق من إعدادات API.")
        return

    # تحديد نوع التحويل والرسالة
    if conversion_type == "arabic_to_english":
        prompt = f"""
        قم بترجمة وتحسين هذا الوصف العربي لمود ماين كرافت إلى وصف تيليجرام إنجليزي.
        اجعل الترجمة طبيعية ومتدفقة وجذابة للاعبين الناطقين بالإنجليزية.
        يجب أن يكون الوصف مناسب لقناة تيليجرام (300-400 حرف).

        اسم المود: {mod.get('name', '')}
        الفئة: {mod.get('category', '')}

        الوصف العربي:
        {existing_description}

        اكتب وصف تيليجرام إنجليزي جذاب:
        """
        status_msg = "جاري إنشاء وصف التيليجرام الإنجليزي..."

    elif conversion_type == "english_to_arabic":
        prompt = f"""
        قم بترجمة وتحسين هذا الوصف الإنجليزي لمود ماين كرافت إلى وصف تيليجرام عربي.
        اجعل الترجمة طبيعية ومتدفقة وجذابة للاعبين العرب.
        يجب أن يكون الوصف مناسب لقناة تيليجرام (300-400 حرف).

        اسم المود: {mod.get('name', '')}
        الفئة: {mod.get('category', '')}

        الوصف الإنجليزي:
        {existing_description}

        اكتب وصف تيليجرام عربي جذاب:
        """
        status_msg = "جاري إنشاء وصف التيليجرام العربي..."

    else:
        messagebox.showerror("خطأ", "نوع تحويل غير صالح.")
        return

    # عرض رسالة التحميل
    update_status(status_msg)

    # تشغيل الطلب في خيط منفصل
    def generate_task():
        try:
            generated_description = smart_repair_gemini_request(prompt)

            if generated_description:
                # تحديث النص في الواجهة الرئيسية
                target_text_widget.delete("1.0", tk.END)
                target_text_widget.insert(tk.END, generated_description)
                update_status("✅ تم إنشاء وصف التيليجرام بنجاح!")
            else:
                update_status("❌ فشل في إنشاء وصف التيليجرام")
                messagebox.showerror("خطأ", "فشل في إنشاء وصف التيليجرام. الرجاء المحاولة مرة أخرى.")

        except Exception as e:
            update_status(f"❌ خطأ في إنشاء وصف التيليجرام: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    # تشغيل المهمة في خيط منفصل
    import threading
    thread = threading.Thread(target=generate_task)
    thread.daemon = True
    thread.start()

def find_mods_missing_telegram_descriptions():
    """العثور على المودات التي تفتقر لأوصاف تيليجرام أو تحتوي على أوصاف معيبة"""
    global loaded_mods_data_list

    if not loaded_mods_data_list:
        messagebox.showwarning("لا توجد بيانات", "الرجاء تحميل قائمة المودات أولاً.")
        return

    # إنشاء نافذة التحميل
    progress_window = tk.Toplevel()
    progress_window.title("فحص أوصاف تيليجرام")
    progress_window.geometry("500x200")
    progress_window.resizable(False, False)
    progress_window.grab_set()  # جعل النافذة modal

    # إطار التقدم
    progress_frame = ttk.Frame(progress_window, padding=20)
    progress_frame.pack(fill=tk.BOTH, expand=True)

    ttk.Label(progress_frame, text="جاري فحص أوصاف تيليجرام...", font=("Arial", 12, "bold")).pack(pady=10)

    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100)
    progress_bar.pack(fill=tk.X, pady=10)

    status_label = ttk.Label(progress_frame, text="بدء الفحص...")
    status_label.pack(pady=5)

    # متغيرات النتائج
    missing_telegram_en = []
    missing_telegram_ar = []
    missing_telegram_both = []
    broken_descriptions = []

    def analyze_mods():
        """تحليل المودات في خيط منفصل"""
        total_mods = len(loaded_mods_data_list)

        for i, mod in enumerate(loaded_mods_data_list):
            # تحديث شريط التقدم
            progress_percent = (i / total_mods) * 100
            progress_var.set(progress_percent)
            status_label.config(text=f"فحص المود: {mod.get('name', 'بدون اسم')[:30]}...")
            progress_window.update()

            telegram_description_en = mod.get('telegram_description_en', '')
            telegram_description_ar = mod.get('telegram_description_ar', '')

            # فحص أساسي للأوصاف
            has_telegram_english = bool(telegram_description_en and telegram_description_en.strip() and len(telegram_description_en.strip()) > 10)
            has_telegram_arabic = bool(telegram_description_ar and telegram_description_ar.strip() and len(telegram_description_ar.strip()) > 10)

            # فحص الأوصاف المعيبة باستخدام الكلمات المفتاحية
            is_broken = False
            broken_reasons = []

            if has_telegram_english or has_telegram_arabic:
                # فحص الأوصاف الموجودة للتأكد من أنها ليست معيبة
                broken_indicators = [
                    "📋 الوصف:", "هناك عدة خيارات", "الخيار الأول", "الخيار الثاني",
                    "حسب الجمهور المستهدف", "بسيط ومباشر", "أكثر تفصيلاً", "يمكنك اختيار",
                    "Description options:", "Option 1:", "Option 2:", "Choose from:",
                    "Here are", "several options", "different versions"
                ]

                # فحص الوصف العربي
                if has_telegram_arabic:
                    for indicator in broken_indicators:
                        if indicator in telegram_description_ar:
                            is_broken = True
                            broken_reasons.append(f"العربي: يحتوي على '{indicator}'")
                            has_telegram_arabic = False  # اعتبره غير موجود
                            break

                # فحص الوصف الإنجليزي
                if has_telegram_english:
                    for indicator in broken_indicators:
                        if indicator in telegram_description_en:
                            is_broken = True
                            broken_reasons.append(f"الإنجليزي: يحتوي على '{indicator}'")
                            has_telegram_english = False  # اعتبره غير موجود
                            break

            # تصنيف المودات
            if is_broken:
                mod['broken_reasons'] = broken_reasons
                broken_descriptions.append(mod)

            # إعادة فحص الأوصاف المفقودة بعد استبعاد المعيبة
            if not has_telegram_english and not has_telegram_arabic:
                missing_telegram_both.append(mod)
            elif not has_telegram_english:
                missing_telegram_en.append(mod)
            elif not has_telegram_arabic:
                missing_telegram_ar.append(mod)

        # إغلاق نافذة التقدم
        progress_window.destroy()

        # عرض النتائج
        show_telegram_analysis_results(missing_telegram_both, missing_telegram_en, missing_telegram_ar, broken_descriptions)

    # تشغيل التحليل في خيط منفصل
    import threading
    analysis_thread = threading.Thread(target=analyze_mods)
    analysis_thread.daemon = True
    analysis_thread.start()

def show_telegram_analysis_results(missing_both, missing_en, missing_ar, broken_descriptions):
    """عرض نتائج تحليل أوصاف تيليجرام"""

    # إنشاء نافذة لعرض النتائج
    results_window = tk.Toplevel()
    results_window.title("تحليل أوصاف تيليجرام")
    results_window.geometry("1000x700")
    results_window.resizable(True, True)

    # إنشاء إطار رئيسي مع تبويبات
    notebook = ttk.Notebook(results_window)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # تبويب للمودات التي تفتقر لكلا الوصفين
    both_frame = ttk.Frame(notebook)
    notebook.add(both_frame, text=f"بدون أي وصف تيليجرام ({len(missing_both)})")
    create_telegram_mods_list_tab(both_frame, missing_both, "لا يوجد وصف تيليجرام إنجليزي أو عربي")

    # تبويب للمودات التي تفتقر للوصف الإنجليزي
    english_frame = ttk.Frame(notebook)
    notebook.add(english_frame, text=f"بدون وصف تيليجرام إنجليزي ({len(missing_en)})")
    create_telegram_mods_list_tab(english_frame, missing_en, "يوجد وصف تيليجرام عربي فقط")

    # تبويب للمودات التي تفتقر للوصف العربي
    arabic_frame = ttk.Frame(notebook)
    notebook.add(arabic_frame, text=f"بدون وصف تيليجرام عربي ({len(missing_ar)})")
    create_telegram_mods_list_tab(arabic_frame, missing_ar, "يوجد وصف تيليجرام إنجليزي فقط")

    # تبويب للمودات ذات الأوصاف المعيبة
    broken_frame = ttk.Frame(notebook)
    notebook.add(broken_frame, text=f"أوصاف تيليجرام معيبة ({len(broken_descriptions)})")
    create_broken_telegram_mods_list_tab(broken_frame, broken_descriptions, "مودات تحتوي على أوصاف تيليجرام معيبة")

    # إضافة معلومات إجمالية
    info_frame = ttk.Frame(results_window)
    info_frame.pack(fill=tk.X, padx=10, pady=5)

    total_mods = len(loaded_mods_data_list)
    total_issues = len(missing_both) + len(missing_en) + len(missing_ar) + len(broken_descriptions)
    complete_telegram_mods = total_mods - total_issues

    info_text = f"إجمالي المودات: {total_mods} | مكتملة أوصاف تيليجرام: {complete_telegram_mods} | تحتاج إصلاح: {total_issues}"
    ttk.Label(info_frame, text=info_text, font=("Arial", 10, "bold")).pack()

    # إضافة أزرار عامة
    buttons_frame = ttk.Frame(results_window)
    buttons_frame.pack(fill=tk.X, padx=10, pady=5)

    def run_gemini_smart_check():
        """تشغيل فحص Gemini الذكي الشامل لجميع المودات"""
        if not REPAIR_GEMINI_CLIENT_OK:
            messagebox.showerror("خطأ", "Gemini غير متوفر. الرجاء التحقق من إعدادات API.")
            return

        # فحص جميع المودات (ليس فقط التي تحتوي على أوصاف)
        mods_to_check = loaded_mods_data_list.copy()

        if not mods_to_check:
            messagebox.showinfo("لا توجد مودات", "لا توجد مودات للفحص.")
            return

        # تأكيد من المستخدم
        confirm = messagebox.askyesno(
            "تأكيد الفحص الذكي",
            f"هل تريد فحص جميع المودات ({len(mods_to_check)} مود) باستخدام Gemini؟\n\n"
            "سيتم فحص الأوصاف الأساسية وأوصاف التيليجرام وكشف المشاكل."
        )

        if confirm:
            run_gemini_smart_analysis(mods_to_check)

    ttk.Button(buttons_frame, text="🤖 فحص ذكي بـ Gemini", command=run_gemini_smart_check).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="إغلاق", command=results_window.destroy).pack(side=tk.RIGHT, padx=5)

def create_telegram_mods_list_tab(parent_frame, mods_list, description):
    """إنشاء تبويب لعرض قائمة المودات التي تفتقر لأوصاف تيليجرام"""

    # إطار للوصف
    desc_frame = ttk.Frame(parent_frame)
    desc_frame.pack(fill=tk.X, padx=5, pady=5)
    ttk.Label(desc_frame, text=description, font=("Arial", 10)).pack()

    # إطار للقائمة
    list_frame = ttk.Frame(parent_frame)
    list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # إنشاء قائمة مع شريط تمرير
    listbox_frame = ttk.Frame(list_frame)
    listbox_frame.pack(fill=tk.BOTH, expand=True)

    scrollbar = ttk.Scrollbar(listbox_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set, selectmode=tk.EXTENDED)
    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.config(command=listbox.yview)

    # ملء القائمة
    for mod in mods_list:
        mod_name = mod.get('name', 'بدون اسم')
        mod_id = mod.get('id', 'N/A')
        category = mod.get('category', 'غير مصنف')

        telegram_description_en = mod.get('telegram_description_en', '')
        telegram_description_ar = mod.get('telegram_description_ar', '')
        has_telegram_en = bool(telegram_description_en and telegram_description_en.strip() and len(telegram_description_en.strip()) > 10)
        has_telegram_ar = bool(telegram_description_ar and telegram_description_ar.strip() and len(telegram_description_ar.strip()) > 10)

        telegram_status = ""
        if has_telegram_en and has_telegram_ar:
            telegram_status = "📱 (EN+AR)"
        elif has_telegram_en:
            telegram_status = "📱 (EN)"
        elif has_telegram_ar:
            telegram_status = "📱 (AR)"
        else:
            telegram_status = "❌"

        display_text = f"ID: {mod_id} - {mod_name} ({category}) {telegram_status}"
        listbox.insert(tk.END, display_text)

    # إطار للأزرار
    buttons_frame = ttk.Frame(parent_frame)
    buttons_frame.pack(fill=tk.X, padx=5, pady=5)

    def select_all_in_tab():
        listbox.select_set(0, tk.END)

    def generate_telegram_descriptions_for_selected():
        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("لا يوجد تحديد", "الرجاء تحديد مود واحد على الأقل.")
            return

        selected_mods = [mods_list[i] for i in selected_indices]
        handle_batch_description_generation(selected_mods, description_type="telegram")

    def view_selected_mod():
        selected_indices = listbox.curselection()
        if len(selected_indices) != 1:
            messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط للعرض.")
            return

        mod = mods_list[selected_indices[0]]
        show_mod_details(mod)

    ttk.Button(buttons_frame, text="تحديد الكل", command=select_all_in_tab).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="إنشاء أوصاف تيليجرام للمحدد", command=generate_telegram_descriptions_for_selected).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="عرض تفاصيل المود", command=view_selected_mod).pack(side=tk.LEFT, padx=5)

def create_broken_telegram_mods_list_tab(parent_frame, mods_list, description):
    """إنشاء تبويب لعرض قائمة المودات ذات الأوصاف المعيبة"""

    # إطار للوصف
    desc_frame = ttk.Frame(parent_frame)
    desc_frame.pack(fill=tk.X, padx=5, pady=5)
    ttk.Label(desc_frame, text=description, font=("Arial", 10)).pack()

    # إطار للقائمة
    list_frame = ttk.Frame(parent_frame)
    list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # إنشاء قائمة مع شريط تمرير
    listbox_frame = ttk.Frame(list_frame)
    listbox_frame.pack(fill=tk.BOTH, expand=True)

    scrollbar = ttk.Scrollbar(listbox_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set, selectmode=tk.EXTENDED)
    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.config(command=listbox.yview)

    # ملء القائمة
    for mod in mods_list:
        mod_name = mod.get('name', 'بدون اسم')
        mod_id = mod.get('id', 'N/A')
        category = mod.get('category', 'غير مصنف')
        reasons = " | ".join(mod.get('broken_reasons', []))

        display_text = f"ID: {mod_id} - {mod_name} ({category}) - أسباب المشكلة: {reasons}"
        listbox.insert(tk.END, display_text)

    # إطار للأزرار
    buttons_frame = ttk.Frame(parent_frame)
    buttons_frame.pack(fill=tk.X, padx=5, pady=5)

    def select_all_broken():
        listbox.select_set(0, tk.END)

    def fix_selected_descriptions():
        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("لا يوجد تحديد", "الرجاء تحديد مود واحد على الأقل.")
            return

        selected_mods = [mods_list[i] for i in selected_indices]
        handle_batch_description_generation(selected_mods, description_type="telegram")

    def view_broken_mod():
        selected_indices = listbox.curselection()
        if len(selected_indices) != 1:
            messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط للعرض.")
            return

        mod = mods_list[selected_indices[0]]
        show_broken_mod_details(mod)

    ttk.Button(buttons_frame, text="تحديد الكل", command=select_all_broken).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="إصلاح المحدد", command=fix_selected_descriptions).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="عرض تفاصيل المود", command=view_broken_mod).pack(side=tk.LEFT, padx=5)

def run_gemini_smart_analysis(mods_to_check):
    """تشغيل تحليل Gemini الذكي الشامل للأوصاف"""

    # إنشاء نافذة التحليل
    analysis_window = tk.Toplevel()
    analysis_window.title("🤖 فحص Gemini الذكي الشامل")
    analysis_window.geometry("800x600")
    analysis_window.resizable(True, True)
    analysis_window.grab_set()

    # إطار التقدم
    progress_frame = ttk.Frame(analysis_window, padding=20)
    progress_frame.pack(fill=tk.BOTH, expand=True)

    ttk.Label(progress_frame, text="🤖 فحص Gemini الذكي الشامل", font=("Arial", 14, "bold")).pack(pady=10)
    ttk.Label(progress_frame, text="جاري فحص جميع الأوصاف باستخدام الذكاء الاصطناعي...", font=("Arial", 10)).pack(pady=5)

    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100)
    progress_bar.pack(fill=tk.X, pady=10)

    status_label = ttk.Label(progress_frame, text="بدء الفحص الذكي الشامل...")
    status_label.pack(pady=5)

    # منطقة عرض النتائج
    results_frame = ttk.LabelFrame(progress_frame, text="نتائج الفحص المباشرة", padding=10)
    results_frame.pack(fill=tk.BOTH, expand=True, pady=10)

    results_text = scrolledtext.ScrolledText(results_frame, height=15, wrap=tk.WORD)
    results_text.pack(fill=tk.BOTH, expand=True)

    # متغيرات النتائج المحسنة
    analysis_results = {
        'problematic_mods': [],
        'missing_basic_descriptions': [],
        'missing_telegram_descriptions': [],
        'broken_descriptions': [],
        'multiple_versions_mentioned': [],
        'instruction_based_descriptions': [],
        'good_mods': []
    }

    def analyze_with_gemini():
        """تحليل شامل للأوصاف باستخدام Gemini"""
        total_mods = len(mods_to_check)

        for i, mod in enumerate(mods_to_check):
            # تحديث شريط التقدم
            progress_percent = (i / total_mods) * 100
            progress_var.set(progress_percent)
            mod_name = mod.get('name', 'بدون اسم')
            status_label.config(text=f"فحص المود: {mod_name[:30]}...")
            analysis_window.update()

            # جمع جميع الأوصاف
            basic_description_en = mod.get('description', '')
            basic_description_ar = mod.get('description_ar', '')
            telegram_description_en = mod.get('telegram_description_en', '')
            telegram_description_ar = mod.get('telegram_description_ar', '')

            # إنشاء prompt شامل للفحص
            prompt = f"""
            أنت خبير في فحص أوصاف مودات ماين كرافت. قم بتحليل شامل لجميع أوصاف هذا المود وحدد:

            اسم المود: {mod_name}
            الفئة: {mod.get('category', '')}

            الأوصاف الأساسية:
            - الوصف الأساسي الإنجليزي: {basic_description_en}
            - الوصف الأساسي العربي: {basic_description_ar}

            أوصاف التيليجرام:
            - وصف التيليجرام الإنجليزي: {telegram_description_en}
            - وصف التيليجرام العربي: {telegram_description_ar}

            قم بفحص وتحديد:
            1. هل يحتوي على أوصاف أساسية؟
            2. هل يحتوي على أوصاف تيليجرام؟
            3. هل توجد مشاكل في الأوصاف مثل:
               - تعليمات بدلاً من أوصاف مباشرة
               - خيارات متعددة ("الخيار الأول"، "الخيار الثاني")
               - ذكر عدة إصدارات للمود
               - عبارات مثل "هناك عدة خيارات" أو "حسب الجمهور المستهدف"
               - أوصاف غير مكتملة أو مقطوعة
               - أوصاف تحتوي على رموز غريبة أو تنسيق خاطئ

            أجب بـ JSON فقط:
            {{
                "has_basic_descriptions": {{
                    "english": true/false,
                    "arabic": true/false
                }},
                "has_telegram_descriptions": {{
                    "english": true/false,
                    "arabic": true/false
                }},
                "problems_found": {{
                    "has_problems": true/false,
                    "instruction_based": true/false,
                    "multiple_options": true/false,
                    "multiple_versions": true/false,
                    "incomplete_descriptions": true/false,
                    "formatting_issues": true/false
                }},
                "detailed_issues": [
                    "قائمة بالمشاكل المحددة الموجودة"
                ],
                "severity": "low/medium/high",
                "recommendation": "توصية للإصلاح"
            }}
            """

            try:
                response = smart_repair_gemini_request(prompt)
                if response:
                    # تنظيف الاستجابة
                    response_clean = response.strip()
                    if response_clean.startswith('```json'):
                        response_clean = response_clean[7:]
                    if response_clean.endswith('```'):
                        response_clean = response_clean[:-3]
                    response_clean = response_clean.strip()

                    result = json.loads(response_clean)

                    # تحليل النتائج وتصنيف المود
                    mod['gemini_analysis'] = result

                    # فحص حالة الأوصاف
                    has_basic = result.get('has_basic_descriptions', {})
                    has_telegram = result.get('has_telegram_descriptions', {})
                    problems = result.get('problems_found', {})

                    # تصنيف المود
                    if problems.get('has_problems', False):
                        analysis_results['problematic_mods'].append(mod)

                        if problems.get('instruction_based', False):
                            analysis_results['instruction_based_descriptions'].append(mod)
                        if problems.get('multiple_versions', False):
                            analysis_results['multiple_versions_mentioned'].append(mod)

                        analysis_results['broken_descriptions'].append(mod)

                        # عرض المشاكل
                        issues = result.get('detailed_issues', [])
                        severity = result.get('severity', 'medium')
                        severity_icon = "🔴" if severity == "high" else "🟡" if severity == "medium" else "🟢"

                        results_text.insert(tk.END, f"{severity_icon} {mod_name}: مشاكل موجودة\n")
                        for issue in issues:
                            results_text.insert(tk.END, f"   • {issue}\n")
                        results_text.insert(tk.END, f"   📋 التوصية: {result.get('recommendation', 'يحتاج إصلاح')}\n\n")
                    else:
                        analysis_results['good_mods'].append(mod)
                        results_text.insert(tk.END, f"✅ {mod_name}: أوصاف جيدة\n")

                    # فحص الأوصاف المفقودة
                    if not (has_basic.get('english', False) and has_basic.get('arabic', False)):
                        analysis_results['missing_basic_descriptions'].append(mod)

                    if not (has_telegram.get('english', False) and has_telegram.get('arabic', False)):
                        analysis_results['missing_telegram_descriptions'].append(mod)

                    results_text.see(tk.END)
                    analysis_window.update()

            except Exception as e:
                results_text.insert(tk.END, f"⚠️ {mod_name}: خطأ في الفحص - {str(e)}\n")
                results_text.see(tk.END)
                analysis_window.update()

        # إكمال الفحص وحفظ النتائج
        progress_var.set(100)
        total_problems = len(analysis_results['problematic_mods'])
        status_label.config(text=f"اكتمل الفحص! تم العثور على {total_problems} مود يحتاج إصلاح")

        # حفظ النتائج في ملف
        save_analysis_results_to_file(analysis_results)

        # حفظ المودات المعيبة في ملف التتبع
        if analysis_results['problematic_mods']:
            save_problematic_mods_to_file(analysis_results['problematic_mods'])
            results_text.insert(tk.END, f"\n💾 تم حفظ {len(analysis_results['problematic_mods'])} مود معيب في ملف التتبع\n")
            results_text.see(tk.END)
            analysis_window.update()

        # إضافة أزرار للنتائج
        buttons_frame = ttk.Frame(progress_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        if analysis_results['problematic_mods']:
            def show_comprehensive_results():
                analysis_window.destroy()
                show_comprehensive_analysis_results(analysis_results)

            ttk.Button(buttons_frame, text="عرض النتائج الشاملة", command=show_comprehensive_results).pack(side=tk.LEFT, padx=5)

            def auto_fix_all_problems():
                analysis_window.destroy()
                handle_auto_fix_problematic_mods(analysis_results['problematic_mods'])

            ttk.Button(buttons_frame, text="🤖 إصلاح تلقائي للكل", command=auto_fix_all_problems).pack(side=tk.LEFT, padx=5)

    # تشغيل التحليل في خيط منفصل
    import threading
    analysis_thread = threading.Thread(target=analyze_with_gemini)
    analysis_thread.daemon = True
    analysis_thread.start()

def save_analysis_results_to_file(analysis_results):
    """حفظ نتائج التحليل في ملف JSON"""
    try:
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"gemini_analysis_results_{timestamp}.json"

        # تحضير البيانات للحفظ
        save_data = {
            'analysis_timestamp': timestamp,
            'total_mods_analyzed': len(analysis_results['problematic_mods']) + len(analysis_results['good_mods']),
            'summary': {
                'problematic_mods_count': len(analysis_results['problematic_mods']),
                'missing_basic_descriptions_count': len(analysis_results['missing_basic_descriptions']),
                'missing_telegram_descriptions_count': len(analysis_results['missing_telegram_descriptions']),
                'broken_descriptions_count': len(analysis_results['broken_descriptions']),
                'multiple_versions_mentioned_count': len(analysis_results['multiple_versions_mentioned']),
                'instruction_based_descriptions_count': len(analysis_results['instruction_based_descriptions']),
                'good_mods_count': len(analysis_results['good_mods'])
            },
            'detailed_results': {}
        }

        # حفظ تفاصيل كل فئة
        for category, mods in analysis_results.items():
            save_data['detailed_results'][category] = []
            for mod in mods:
                mod_data = {
                    'id': mod.get('id'),
                    'name': mod.get('name'),
                    'category': mod.get('category'),
                    'gemini_analysis': mod.get('gemini_analysis', {})
                }
                save_data['detailed_results'][category].append(mod_data)

        # حفظ الملف
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)

        print(f"✅ تم حفظ نتائج التحليل في الملف: {filename}")
        update_status(f"تم حفظ نتائج التحليل في: {filename}")

    except Exception as e:
        print(f"❌ خطأ في حفظ نتائج التحليل: {e}")
        update_status(f"خطأ في حفظ النتائج: {e}")

def show_comprehensive_analysis_results(analysis_results):
    """عرض النتائج الشاملة لتحليل Gemini"""

    results_window = tk.Toplevel()
    results_window.title("🤖 نتائج تحليل Gemini الشامل")
    results_window.geometry("1200x800")
    results_window.resizable(True, True)

    # إطار رئيسي
    main_frame = ttk.Frame(results_window, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # معلومات إجمالية
    summary_frame = ttk.LabelFrame(main_frame, text="ملخص التحليل", padding=10)
    summary_frame.pack(fill=tk.X, pady=(0, 10))

    total_analyzed = len(analysis_results['problematic_mods']) + len(analysis_results['good_mods'])
    problematic_count = len(analysis_results['problematic_mods'])

    summary_text = f"""📊 إجمالي المودات المحللة: {total_analyzed}
🔴 مودات تحتاج إصلاح: {problematic_count}
✅ مودات جيدة: {len(analysis_results['good_mods'])}
📝 مودات بدون أوصاف أساسية: {len(analysis_results['missing_basic_descriptions'])}
📱 مودات بدون أوصاف تيليجرام: {len(analysis_results['missing_telegram_descriptions'])}
📋 مودات تحتوي على تعليمات: {len(analysis_results['instruction_based_descriptions'])}
🔢 مودات تذكر عدة إصدارات: {len(analysis_results['multiple_versions_mentioned'])}"""

    ttk.Label(summary_frame, text=summary_text, font=("Arial", 10)).pack(anchor="w")

    # إنشاء تبويبات للنتائج
    notebook = ttk.Notebook(main_frame)
    notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    # تبويب المودات التي تحتاج إصلاح
    problematic_frame = ttk.Frame(notebook)
    notebook.add(problematic_frame, text=f"🔴 مودات تحتاج إصلاح ({problematic_count})")
    create_analysis_results_tab(problematic_frame, analysis_results['problematic_mods'], "مودات تحتوي على مشاكل في الأوصاف")

    # تبويب المودات بدون أوصاف أساسية
    missing_basic_frame = ttk.Frame(notebook)
    notebook.add(missing_basic_frame, text=f"📝 بدون أوصاف أساسية ({len(analysis_results['missing_basic_descriptions'])})")
    create_analysis_results_tab(missing_basic_frame, analysis_results['missing_basic_descriptions'], "مودات تفتقر للأوصاف الأساسية")

    # تبويب المودات بدون أوصاف تيليجرام
    missing_telegram_frame = ttk.Frame(notebook)
    notebook.add(missing_telegram_frame, text=f"📱 بدون أوصاف تيليجرام ({len(analysis_results['missing_telegram_descriptions'])})")
    create_analysis_results_tab(missing_telegram_frame, analysis_results['missing_telegram_descriptions'], "مودات تفتقر لأوصاف التيليجرام")

    # تبويب المودات التي تحتوي على تعليمات
    instruction_frame = ttk.Frame(notebook)
    notebook.add(instruction_frame, text=f"📋 أوصاف بتعليمات ({len(analysis_results['instruction_based_descriptions'])})")
    create_analysis_results_tab(instruction_frame, analysis_results['instruction_based_descriptions'], "مودات تحتوي على تعليمات بدلاً من أوصاف")

    # تبويب المودات التي تذكر عدة إصدارات
    versions_frame = ttk.Frame(notebook)
    notebook.add(versions_frame, text=f"🔢 تذكر عدة إصدارات ({len(analysis_results['multiple_versions_mentioned'])})")
    create_analysis_results_tab(versions_frame, analysis_results['multiple_versions_mentioned'], "مودات تذكر عدة إصدارات في الوصف")

    # تبويب المودات الجيدة
    good_frame = ttk.Frame(notebook)
    notebook.add(good_frame, text=f"✅ مودات جيدة ({len(analysis_results['good_mods'])})")
    create_analysis_results_tab(good_frame, analysis_results['good_mods'], "مودات بأوصاف جيدة")

    # أزرار العمليات الشاملة
    actions_frame = ttk.Frame(main_frame)
    actions_frame.pack(fill=tk.X, pady=(10, 0))

    def fix_all_problematic():
        handle_auto_fix_problematic_mods(analysis_results['problematic_mods'])

    def export_results():
        export_analysis_results(analysis_results)

    ttk.Button(actions_frame, text="🤖 إصلاح تلقائي لجميع المشاكل", command=fix_all_problematic).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="📄 تصدير النتائج", command=export_results).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="إغلاق", command=results_window.destroy).pack(side=tk.RIGHT, padx=5)

def save_analysis_results_to_file(analysis_results):
    """حفظ نتائج التحليل في ملف JSON"""
    try:
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"gemini_analysis_results_{timestamp}.json"

        # تحضير البيانات للحفظ
        save_data = {
            'analysis_timestamp': timestamp,
            'total_mods_analyzed': len(analysis_results['problematic_mods']) + len(analysis_results['good_mods']),
            'summary': {
                'problematic_mods_count': len(analysis_results['problematic_mods']),
                'missing_basic_descriptions_count': len(analysis_results['missing_basic_descriptions']),
                'missing_telegram_descriptions_count': len(analysis_results['missing_telegram_descriptions']),
                'broken_descriptions_count': len(analysis_results['broken_descriptions']),
                'multiple_versions_mentioned_count': len(analysis_results['multiple_versions_mentioned']),
                'instruction_based_descriptions_count': len(analysis_results['instruction_based_descriptions']),
                'good_mods_count': len(analysis_results['good_mods'])
            },
            'detailed_results': {}
        }

        # حفظ تفاصيل كل فئة
        for category, mods in analysis_results.items():
            save_data['detailed_results'][category] = []
            for mod in mods:
                mod_data = {
                    'id': mod.get('id'),
                    'name': mod.get('name'),
                    'category': mod.get('category'),
                    'gemini_analysis': mod.get('gemini_analysis', {})
                }
                save_data['detailed_results'][category].append(mod_data)

        # حفظ الملف
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)

        print(f"✅ تم حفظ نتائج التحليل في الملف: {filename}")
        update_status(f"تم حفظ نتائج التحليل في: {filename}")

    except Exception as e:
        print(f"❌ خطأ في حفظ نتائج التحليل: {e}")
        update_status(f"خطأ في حفظ النتائج: {e}")

def show_comprehensive_analysis_results(analysis_results):
    """عرض النتائج الشاملة لتحليل Gemini"""

    results_window = tk.Toplevel()
    results_window.title("🤖 نتائج تحليل Gemini الشامل")
    results_window.geometry("1200x800")
    results_window.resizable(True, True)

    # إطار رئيسي
    main_frame = ttk.Frame(results_window, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # معلومات إجمالية
    summary_frame = ttk.LabelFrame(main_frame, text="ملخص التحليل", padding=10)
    summary_frame.pack(fill=tk.X, pady=(0, 10))

    total_analyzed = len(analysis_results['problematic_mods']) + len(analysis_results['good_mods'])
    problematic_count = len(analysis_results['problematic_mods'])

    summary_text = f"""
📊 إجمالي المودات المحللة: {total_analyzed}
🔴 مودات تحتاج إصلاح: {problematic_count}
✅ مودات جيدة: {len(analysis_results['good_mods'])}
📝 مودات بدون أوصاف أساسية: {len(analysis_results['missing_basic_descriptions'])}
📱 مودات بدون أوصاف تيليجرام: {len(analysis_results['missing_telegram_descriptions'])}
📋 مودات تحتوي على تعليمات: {len(analysis_results['instruction_based_descriptions'])}
🔢 مودات تذكر عدة إصدارات: {len(analysis_results['multiple_versions_mentioned'])}
    """

    ttk.Label(summary_frame, text=summary_text, font=("Arial", 10)).pack(anchor="w")

    # إنشاء تبويبات للنتائج
    notebook = ttk.Notebook(main_frame)
    notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    # تبويب المودات التي تحتاج إصلاح
    problematic_frame = ttk.Frame(notebook)
    notebook.add(problematic_frame, text=f"🔴 مودات تحتاج إصلاح ({problematic_count})")
    create_analysis_results_tab(problematic_frame, analysis_results['problematic_mods'], "مودات تحتوي على مشاكل في الأوصاف")

    # تبويب المودات بدون أوصاف أساسية
    missing_basic_frame = ttk.Frame(notebook)
    notebook.add(missing_basic_frame, text=f"📝 بدون أوصاف أساسية ({len(analysis_results['missing_basic_descriptions'])})")
    create_analysis_results_tab(missing_basic_frame, analysis_results['missing_basic_descriptions'], "مودات تفتقر للأوصاف الأساسية")

    # تبويب المودات بدون أوصاف تيليجرام
    missing_telegram_frame = ttk.Frame(notebook)
    notebook.add(missing_telegram_frame, text=f"📱 بدون أوصاف تيليجرام ({len(analysis_results['missing_telegram_descriptions'])})")
    create_analysis_results_tab(missing_telegram_frame, analysis_results['missing_telegram_descriptions'], "مودات تفتقر لأوصاف التيليجرام")

    # تبويب المودات التي تحتوي على تعليمات
    instruction_frame = ttk.Frame(notebook)
    notebook.add(instruction_frame, text=f"📋 أوصاف بتعليمات ({len(analysis_results['instruction_based_descriptions'])})")
    create_analysis_results_tab(instruction_frame, analysis_results['instruction_based_descriptions'], "مودات تحتوي على تعليمات بدلاً من أوصاف")

    # تبويب المودات التي تذكر عدة إصدارات
    versions_frame = ttk.Frame(notebook)
    notebook.add(versions_frame, text=f"🔢 تذكر عدة إصدارات ({len(analysis_results['multiple_versions_mentioned'])})")
    create_analysis_results_tab(versions_frame, analysis_results['multiple_versions_mentioned'], "مودات تذكر عدة إصدارات في الوصف")

    # تبويب المودات الجيدة
    good_frame = ttk.Frame(notebook)
    notebook.add(good_frame, text=f"✅ مودات جيدة ({len(analysis_results['good_mods'])})")
    create_analysis_results_tab(good_frame, analysis_results['good_mods'], "مودات بأوصاف جيدة")

    # أزرار العمليات الشاملة
    actions_frame = ttk.Frame(main_frame)
    actions_frame.pack(fill=tk.X, pady=(10, 0))

    def fix_all_problematic():
        handle_auto_fix_problematic_mods(analysis_results['problematic_mods'])

    def export_results():
        export_analysis_results(analysis_results)

    ttk.Button(actions_frame, text="🤖 إصلاح تلقائي لجميع المشاكل", command=fix_all_problematic).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="📄 تصدير النتائج", command=export_results).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="إغلاق", command=results_window.destroy).pack(side=tk.RIGHT, padx=5)

def create_analysis_results_tab(parent_frame, mods_list, description):
    """إنشاء تبويب لعرض نتائج تحليل Gemini"""

    # إطار للوصف
    desc_frame = ttk.Frame(parent_frame)
    desc_frame.pack(fill=tk.X, padx=5, pady=5)
    ttk.Label(desc_frame, text=description, font=("Arial", 10)).pack()

    # إطار للقائمة
    list_frame = ttk.Frame(parent_frame)
    list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # إنشاء قائمة مع شريط تمرير
    listbox_frame = ttk.Frame(list_frame)
    listbox_frame.pack(fill=tk.BOTH, expand=True)

    scrollbar = ttk.Scrollbar(listbox_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set, selectmode=tk.EXTENDED)
    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.config(command=listbox.yview)

    # ملء القائمة
    for mod in mods_list:
        mod_name = mod.get('name', 'بدون اسم')
        mod_id = mod.get('id', 'N/A')
        category = mod.get('category', 'غير مصنف')

        # عرض تفاصيل التحليل إذا وجدت
        analysis = mod.get('gemini_analysis', {})
        if analysis:
            problems = analysis.get('problems_found', {})
            severity = analysis.get('severity', 'medium')
            severity_icon = "🔴" if severity == "high" else "🟡" if severity == "medium" else "🟢"

            # عرض حالة الأوصاف
            has_basic = analysis.get('has_basic_descriptions', {})
            has_telegram = analysis.get('has_telegram_descriptions', {})

            basic_status = "📝" if (has_basic.get('english') and has_basic.get('arabic')) else "❌"
            telegram_status = "📱" if (has_telegram.get('english') and has_telegram.get('arabic')) else "❌"

            display_text = f"{severity_icon} ID: {mod_id} - {mod_name} ({category}) {basic_status} {telegram_status}"
        else:
            display_text = f"ID: {mod_id} - {mod_name} ({category})"

        listbox.insert(tk.END, display_text)

    # إطار للأزرار
    buttons_frame = ttk.Frame(parent_frame)
    buttons_frame.pack(fill=tk.X, padx=5, pady=5)

    def select_all_in_analysis_tab():
        listbox.select_set(0, tk.END)

    def view_detailed_analysis():
        selected_indices = listbox.curselection()
        if len(selected_indices) != 1:
            messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط للعرض.")
            return

        mod = mods_list[selected_indices[0]]
        show_detailed_mod_analysis(mod)

    def fix_selected_mods():
        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("لا يوجد تحديد", "الرجاء تحديد مود واحد على الأقل.")
            return

        selected_mods = [mods_list[i] for i in selected_indices]
        handle_auto_fix_problematic_mods(selected_mods)

    ttk.Button(buttons_frame, text="تحديد الكل", command=select_all_in_analysis_tab).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="عرض التحليل التفصيلي", command=view_detailed_analysis).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="🤖 إصلاح المحدد", command=fix_selected_mods).pack(side=tk.LEFT, padx=5)

def show_detailed_mod_analysis(mod):
    """عرض التحليل التفصيلي لمود واحد"""

    analysis_window = tk.Toplevel()
    analysis_window.title(f"تحليل تفصيلي: {mod.get('name', 'بدون اسم')}")
    analysis_window.geometry("800x700")
    analysis_window.resizable(True, True)

    # إطار رئيسي
    main_frame = ttk.Frame(analysis_window, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # معلومات أساسية
    info_frame = ttk.LabelFrame(main_frame, text="معلومات المود", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 10))

    ttk.Label(info_frame, text=f"ID: {mod.get('id', 'N/A')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الاسم: {mod.get('name', 'بدون اسم')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الفئة: {mod.get('category', 'غير مصنف')}").pack(anchor="w")

    # نتائج التحليل
    analysis = mod.get('gemini_analysis', {})
    if analysis:
        # حالة الأوصاف
        status_frame = ttk.LabelFrame(main_frame, text="حالة الأوصاف", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        has_basic = analysis.get('has_basic_descriptions', {})
        has_telegram = analysis.get('has_telegram_descriptions', {})

        basic_en_status = "✅" if has_basic.get('english') else "❌"
        basic_ar_status = "✅" if has_basic.get('arabic') else "❌"
        telegram_en_status = "✅" if has_telegram.get('english') else "❌"
        telegram_ar_status = "✅" if has_telegram.get('arabic') else "❌"

        ttk.Label(status_frame, text=f"الوصف الأساسي الإنجليزي: {basic_en_status}").pack(anchor="w")
        ttk.Label(status_frame, text=f"الوصف الأساسي العربي: {basic_ar_status}").pack(anchor="w")
        ttk.Label(status_frame, text=f"وصف التيليجرام الإنجليزي: {telegram_en_status}").pack(anchor="w")
        ttk.Label(status_frame, text=f"وصف التيليجرام العربي: {telegram_ar_status}").pack(anchor="w")

        # المشاكل المكتشفة
        problems = analysis.get('problems_found', {})
        if problems.get('has_problems', False):
            problems_frame = ttk.LabelFrame(main_frame, text="المشاكل المكتشفة", padding=10)
            problems_frame.pack(fill=tk.X, pady=(0, 10))

            severity = analysis.get('severity', 'medium')
            severity_text = "عالية" if severity == "high" else "متوسطة" if severity == "medium" else "منخفضة"
            severity_color = "red" if severity == "high" else "orange" if severity == "medium" else "green"

            ttk.Label(problems_frame, text=f"مستوى الخطورة: {severity_text}", foreground=severity_color).pack(anchor="w")

            if problems.get('instruction_based'):
                ttk.Label(problems_frame, text="❌ يحتوي على تعليمات بدلاً من وصف مباشر").pack(anchor="w")
            if problems.get('multiple_options'):
                ttk.Label(problems_frame, text="❌ يحتوي على خيارات متعددة").pack(anchor="w")
            if problems.get('multiple_versions'):
                ttk.Label(problems_frame, text="❌ يذكر عدة إصدارات للمود").pack(anchor="w")
            if problems.get('incomplete_descriptions'):
                ttk.Label(problems_frame, text="❌ أوصاف غير مكتملة").pack(anchor="w")
            if problems.get('formatting_issues'):
                ttk.Label(problems_frame, text="❌ مشاكل في التنسيق").pack(anchor="w")

        # المشاكل التفصيلية
        detailed_issues = analysis.get('detailed_issues', [])
        if detailed_issues:
            issues_frame = ttk.LabelFrame(main_frame, text="تفاصيل المشاكل", padding=10)
            issues_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            issues_text = scrolledtext.ScrolledText(issues_frame, height=8, wrap=tk.WORD)
            issues_text.pack(fill=tk.BOTH, expand=True)

            for issue in detailed_issues:
                issues_text.insert(tk.END, f"• {issue}\n")

        # التوصية
        recommendation = analysis.get('recommendation', '')
        if recommendation:
            rec_frame = ttk.LabelFrame(main_frame, text="التوصية", padding=10)
            rec_frame.pack(fill=tk.X, pady=(0, 10))
            ttk.Label(rec_frame, text=recommendation, wraplength=750).pack(anchor="w")

    # أزرار العمليات
    actions_frame = ttk.Frame(main_frame)
    actions_frame.pack(fill=tk.X, pady=(10, 0))

    def fix_this_mod():
        handle_auto_fix_problematic_mods([mod])

    ttk.Button(actions_frame, text="🤖 إصلاح هذا المود", command=fix_this_mod).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="إغلاق", command=analysis_window.destroy).pack(side=tk.RIGHT, padx=5)

def handle_auto_fix_problematic_mods(problematic_mods):
    """معالجة الإصلاح التلقائي للمودات التي تحتوي على مشاكل"""

    if not problematic_mods:
        messagebox.showinfo("لا توجد مودات", "لا توجد مودات تحتاج إصلاح.")
        return

    # نافذة تأكيد
    confirm = messagebox.askyesno(
        "تأكيد الإصلاح التلقائي",
        f"هل تريد إصلاح {len(problematic_mods)} مود تلقائياً باستخدام Gemini؟\n\n"
        "سيتم إنشاء أوصاف جديدة لجميع المودات المحددة."
    )

    if not confirm:
        return

    # إنشاء نافذة التقدم
    progress_window = tk.Toplevel()
    progress_window.title("🤖 الإصلاح التلقائي")
    progress_window.geometry("600x400")
    progress_window.resizable(False, False)
    progress_window.grab_set()

    # إطار التقدم
    progress_frame = ttk.Frame(progress_window, padding=20)
    progress_frame.pack(fill=tk.BOTH, expand=True)

    ttk.Label(progress_frame, text="🤖 الإصلاح التلقائي للمودات", font=("Arial", 14, "bold")).pack(pady=10)

    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100)
    progress_bar.pack(fill=tk.X, pady=10)

    status_label = ttk.Label(progress_frame, text="بدء الإصلاح التلقائي...")
    status_label.pack(pady=5)

    # منطقة عرض النتائج
    results_frame = ttk.LabelFrame(progress_frame, text="نتائج الإصلاح", padding=10)
    results_frame.pack(fill=tk.BOTH, expand=True, pady=10)

    results_text = scrolledtext.ScrolledText(results_frame, height=12, wrap=tk.WORD)
    results_text.pack(fill=tk.BOTH, expand=True)

    # متغيرات النتائج
    success_count = 0
    fail_count = 0

    def auto_fix_task():
        """مهمة الإصلاح التلقائي"""
        nonlocal success_count, fail_count

        total_mods = len(problematic_mods)

        for i, mod in enumerate(problematic_mods):
            # تحديث شريط التقدم
            progress_percent = (i / total_mods) * 100
            progress_var.set(progress_percent)
            mod_name = mod.get('name', 'بدون اسم')
            status_label.config(text=f"إصلاح المود: {mod_name[:30]}...")
            progress_window.update()

            try:
                # تحديد نوع الإصلاح المطلوب
                analysis = mod.get('gemini_analysis', {})
                has_basic = analysis.get('has_basic_descriptions', {})
                has_telegram = analysis.get('has_telegram_descriptions', {})

                results_text.insert(tk.END, f"🔧 إصلاح المود: {mod_name}\n")

                # إصلاح الأوصاف الأساسية إذا لزم الأمر
                if not (has_basic.get('english') and has_basic.get('arabic')):
                    results_text.insert(tk.END, "   📝 إنشاء أوصاف أساسية...\n")
                    basic_descriptions = generate_custom_description(mod)
                    if basic_descriptions:
                        en_desc = basic_descriptions.get('en', '')
                        ar_desc = basic_descriptions.get('ar', '')
                        if update_mod_descriptions(mod['id'], en_desc, ar_desc):
                            results_text.insert(tk.END, "   ✅ تم إنشاء الأوصاف الأساسية\n")
                        else:
                            results_text.insert(tk.END, "   ❌ فشل في حفظ الأوصاف الأساسية\n")
                    else:
                        results_text.insert(tk.END, "   ❌ فشل في إنشاء الأوصاف الأساسية\n")

                # إصلاح أوصاف التيليجرام إذا لزم الأمر
                if not (has_telegram.get('english') and has_telegram.get('arabic')):
                    results_text.insert(tk.END, "   📱 إنشاء أوصاف تيليجرام...\n")
                    telegram_descriptions = generate_telegram_custom_description(mod)
                    if telegram_descriptions:
                        en_desc = telegram_descriptions.get('en', '')
                        ar_desc = telegram_descriptions.get('ar', '')
                        if update_mod_telegram_descriptions(mod['id'], en_desc, ar_desc):
                            results_text.insert(tk.END, "   ✅ تم إنشاء أوصاف التيليجرام\n")
                        else:
                            results_text.insert(tk.END, "   ❌ فشل في حفظ أوصاف التيليجرام\n")
                    else:
                        results_text.insert(tk.END, "   ❌ فشل في إنشاء أوصاف التيليجرام\n")

                results_text.insert(tk.END, f"✅ اكتمل إصلاح المود: {mod_name}\n\n")
                success_count += 1

                # تمييز المود كمُصلح في ملف التتبع
                mark_mod_as_fixed(mod['id'])

            except Exception as e:
                results_text.insert(tk.END, f"❌ خطأ في إصلاح المود {mod_name}: {str(e)}\n\n")
                fail_count += 1

                # زيادة عدد محاولات الإصلاح
                increment_fix_attempts(mod['id'])

            results_text.see(tk.END)
            progress_window.update()

        # إكمال الإصلاح
        progress_var.set(100)
        status_label.config(text=f"اكتمل الإصلاح! نجح: {success_count} | فشل: {fail_count}")

        # إضافة زر الإغلاق
        ttk.Button(progress_frame, text="إغلاق", command=progress_window.destroy).pack(pady=10)

    # تشغيل مهمة الإصلاح في خيط منفصل
    import threading
    fix_thread = threading.Thread(target=auto_fix_task)
    fix_thread.daemon = True
    fix_thread.start()

def export_analysis_results(analysis_results):
    """تصدير نتائج التحليل إلى ملف Excel"""
    try:
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"gemini_analysis_export_{timestamp}.csv"

        # تحضير البيانات للتصدير
        export_data = []

        # إضافة جميع المودات مع تفاصيل التحليل
        all_mods = (analysis_results['problematic_mods'] +
                   analysis_results['good_mods'] +
                   analysis_results['missing_basic_descriptions'] +
                   analysis_results['missing_telegram_descriptions'])

        for mod in all_mods:
            analysis = mod.get('gemini_analysis', {})
            has_basic = analysis.get('has_basic_descriptions', {})
            has_telegram = analysis.get('has_telegram_descriptions', {})
            problems = analysis.get('problems_found', {})

            row = {
                'ID': mod.get('id', ''),
                'اسم المود': mod.get('name', ''),
                'الفئة': mod.get('category', ''),
                'وصف أساسي إنجليزي': 'نعم' if has_basic.get('english') else 'لا',
                'وصف أساسي عربي': 'نعم' if has_basic.get('arabic') else 'لا',
                'وصف تيليجرام إنجليزي': 'نعم' if has_telegram.get('english') else 'لا',
                'وصف تيليجرام عربي': 'نعم' if has_telegram.get('arabic') else 'لا',
                'يحتوي على مشاكل': 'نعم' if problems.get('has_problems') else 'لا',
                'تعليمات بدلاً من وصف': 'نعم' if problems.get('instruction_based') else 'لا',
                'خيارات متعددة': 'نعم' if problems.get('multiple_options') else 'لا',
                'عدة إصدارات': 'نعم' if problems.get('multiple_versions') else 'لا',
                'مستوى الخطورة': analysis.get('severity', ''),
                'التوصية': analysis.get('recommendation', ''),
                'المشاكل التفصيلية': ' | '.join(analysis.get('detailed_issues', []))
            }
            export_data.append(row)

        # كتابة ملف CSV
        import csv
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            if export_data:
                fieldnames = export_data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(export_data)

        messagebox.showinfo("تم التصدير", f"تم تصدير النتائج إلى الملف:\n{filename}")
        update_status(f"تم تصدير نتائج التحليل إلى: {filename}")

    except Exception as e:
        messagebox.showerror("خطأ في التصدير", f"فشل في تصدير النتائج:\n{str(e)}")
        update_status(f"خطأ في تصدير النتائج: {e}")

# --- دوال إدارة ملف المودات المعيبة ---

def save_problematic_mods_to_file(problematic_mods):
    """حفظ المودات المعيبة في ملف للتتبع"""
    try:
        import datetime

        # قراءة الملف الحالي إذا وجد
        existing_data = load_problematic_mods_from_file()

        # إضافة المودات الجديدة
        timestamp = datetime.datetime.now().isoformat()

        for mod in problematic_mods:
            mod_id = mod.get('id')
            if mod_id:
                # تحديث أو إضافة المود
                existing_data[str(mod_id)] = {
                    'id': mod_id,
                    'name': mod.get('name', ''),
                    'category': mod.get('category', ''),
                    'problems_detected': mod.get('gemini_analysis', {}).get('detailed_issues', []),
                    'severity': mod.get('gemini_analysis', {}).get('severity', 'medium'),
                    'recommendation': mod.get('gemini_analysis', {}).get('recommendation', ''),
                    'first_detected': existing_data.get(str(mod_id), {}).get('first_detected', timestamp),
                    'last_updated': timestamp,
                    'status': 'pending_fix',
                    'fix_attempts': existing_data.get(str(mod_id), {}).get('fix_attempts', 0)
                }

        # حفظ الملف
        with open(PROBLEMATIC_MODS_FILE, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, indent=2, ensure_ascii=False)

        update_status(f"✅ تم حفظ {len(problematic_mods)} مود معيب في ملف التتبع")
        return True

    except Exception as e:
        update_status(f"❌ خطأ في حفظ المودات المعيبة: {e}")
        return False

def load_problematic_mods_from_file():
    """تحميل المودات المعيبة من الملف"""
    try:
        if os.path.exists(PROBLEMATIC_MODS_FILE):
            with open(PROBLEMATIC_MODS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        update_status(f"❌ خطأ في تحميل ملف المودات المعيبة: {e}")
        return {}

def mark_mod_as_fixed(mod_id):
    """تمييز مود كمُصلح"""
    try:
        import datetime

        # قراءة المودات المعيبة
        problematic_data = load_problematic_mods_from_file()

        if str(mod_id) in problematic_data:
            mod_data = problematic_data[str(mod_id)]

            # نقل إلى ملف المودات المُصلحة
            fixed_data = load_fixed_mods_from_file()
            fixed_data[str(mod_id)] = {
                **mod_data,
                'fixed_timestamp': datetime.datetime.now().isoformat(),
                'status': 'fixed'
            }

            # حفظ ملف المودات المُصلحة
            with open(FIXED_MODS_FILE, 'w', encoding='utf-8') as f:
                json.dump(fixed_data, f, indent=2, ensure_ascii=False)

            # حذف من ملف المودات المعيبة
            del problematic_data[str(mod_id)]

            # حفظ ملف المودات المعيبة المحدث
            with open(PROBLEMATIC_MODS_FILE, 'w', encoding='utf-8') as f:
                json.dump(problematic_data, f, indent=2, ensure_ascii=False)

            update_status(f"✅ تم تمييز المود {mod_id} كمُصلح")
            return True

    except Exception as e:
        update_status(f"❌ خطأ في تمييز المود كمُصلح: {e}")
        return False

def load_fixed_mods_from_file():
    """تحميل المودات المُصلحة من الملف"""
    try:
        if os.path.exists(FIXED_MODS_FILE):
            with open(FIXED_MODS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        update_status(f"❌ خطأ في تحميل ملف المودات المُصلحة: {e}")
        return {}

def increment_fix_attempts(mod_id):
    """زيادة عدد محاولات الإصلاح للمود"""
    try:
        problematic_data = load_problematic_mods_from_file()

        if str(mod_id) in problematic_data:
            problematic_data[str(mod_id)]['fix_attempts'] += 1
            problematic_data[str(mod_id)]['last_updated'] = datetime.datetime.now().isoformat()

            # حفظ الملف
            with open(PROBLEMATIC_MODS_FILE, 'w', encoding='utf-8') as f:
                json.dump(problematic_data, f, indent=2, ensure_ascii=False)

            return True

    except Exception as e:
        update_status(f"❌ خطأ في تحديث محاولات الإصلاح: {e}")
        return False

def show_tracked_problematic_mods():
    """عرض المودات المعيبة المحفوظة في الملف"""

    # تحميل البيانات
    problematic_data = load_problematic_mods_from_file()
    fixed_data = load_fixed_mods_from_file()

    if not problematic_data and not fixed_data:
        messagebox.showinfo("لا توجد بيانات", "لا توجد مودات معيبة محفوظة للعرض.")
        return

    # إنشاء نافذة العرض
    tracking_window = tk.Toplevel()
    tracking_window.title("📋 تتبع المودات المعيبة")
    tracking_window.geometry("1200x800")
    tracking_window.resizable(True, True)

    # إطار رئيسي
    main_frame = ttk.Frame(tracking_window, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # معلومات إجمالية
    summary_frame = ttk.LabelFrame(main_frame, text="ملخص التتبع", padding=10)
    summary_frame.pack(fill=tk.X, pady=(0, 10))

    pending_count = len(problematic_data)
    fixed_count = len(fixed_data)

    summary_text = f"""📊 إجمالي المودات المتتبعة: {pending_count + fixed_count}
🔴 مودات تحتاج إصلاح: {pending_count}
✅ مودات تم إصلاحها: {fixed_count}"""

    ttk.Label(summary_frame, text=summary_text, font=("Arial", 11, "bold")).pack(anchor="w")

    # إنشاء تبويبات
    notebook = ttk.Notebook(main_frame)
    notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    # تبويب المودات التي تحتاج إصلاح
    pending_frame = ttk.Frame(notebook)
    notebook.add(pending_frame, text=f"🔴 تحتاج إصلاح ({pending_count})")
    create_tracked_mods_tab(pending_frame, problematic_data, "pending")

    # تبويب المودات المُصلحة
    fixed_frame = ttk.Frame(notebook)
    notebook.add(fixed_frame, text=f"✅ تم إصلاحها ({fixed_count})")
    create_tracked_mods_tab(fixed_frame, fixed_data, "fixed")

    # أزرار العمليات الرئيسية
    actions_frame = ttk.Frame(main_frame)
    actions_frame.pack(fill=tk.X, pady=(10, 0))

    def fix_all_pending():
        """إصلاح جميع المودات المعلقة"""
        if not problematic_data:
            messagebox.showinfo("لا توجد مودات", "لا توجد مودات تحتاج إصلاح.")
            return

        # تحويل البيانات إلى تنسيق مناسب للإصلاح
        mods_to_fix = []
        for mod_id, mod_data in problematic_data.items():
            # البحث عن المود في البيانات المحملة
            for loaded_mod in loaded_mods_data_list:
                if str(loaded_mod.get('id')) == str(mod_id):
                    mods_to_fix.append(loaded_mod)
                    break

        if mods_to_fix:
            handle_auto_fix_problematic_mods(mods_to_fix)
        else:
            messagebox.showwarning("خطأ", "لم يتم العثور على المودات في البيانات المحملة.")

    def clear_fixed_history():
        """مسح تاريخ المودات المُصلحة"""
        confirm = messagebox.askyesno("تأكيد المسح", "هل تريد مسح تاريخ جميع المودات المُصلحة؟")
        if confirm:
            try:
                if os.path.exists(FIXED_MODS_FILE):
                    os.remove(FIXED_MODS_FILE)
                update_status("✅ تم مسح تاريخ المودات المُصلحة")
                tracking_window.destroy()
                show_tracked_problematic_mods()  # إعادة فتح النافذة
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في مسح التاريخ: {e}")

    def export_tracking_data():
        """تصدير بيانات التتبع"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"problematic_mods_tracking_export_{timestamp}.csv"

            # تحضير البيانات للتصدير
            export_data = []

            # إضافة المودات المعلقة
            for mod_id, mod_data in problematic_data.items():
                row = {
                    'ID': mod_id,
                    'اسم المود': mod_data.get('name', ''),
                    'الفئة': mod_data.get('category', ''),
                    'الحالة': 'تحتاج إصلاح',
                    'مستوى الخطورة': mod_data.get('severity', ''),
                    'المشاكل': ' | '.join(mod_data.get('problems_detected', [])),
                    'التوصية': mod_data.get('recommendation', ''),
                    'تاريخ الاكتشاف': mod_data.get('first_detected', ''),
                    'آخر تحديث': mod_data.get('last_updated', ''),
                    'محاولات الإصلاح': mod_data.get('fix_attempts', 0)
                }
                export_data.append(row)

            # إضافة المودات المُصلحة
            for mod_id, mod_data in fixed_data.items():
                row = {
                    'ID': mod_id,
                    'اسم المود': mod_data.get('name', ''),
                    'الفئة': mod_data.get('category', ''),
                    'الحالة': 'تم الإصلاح',
                    'مستوى الخطورة': mod_data.get('severity', ''),
                    'المشاكل': ' | '.join(mod_data.get('problems_detected', [])),
                    'التوصية': mod_data.get('recommendation', ''),
                    'تاريخ الاكتشاف': mod_data.get('first_detected', ''),
                    'تاريخ الإصلاح': mod_data.get('fixed_timestamp', ''),
                    'محاولات الإصلاح': mod_data.get('fix_attempts', 0)
                }
                export_data.append(row)

            # كتابة ملف CSV
            import csv
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                if export_data:
                    fieldnames = export_data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(export_data)

            messagebox.showinfo("تم التصدير", f"تم تصدير بيانات التتبع إلى:\n{filename}")
            update_status(f"تم تصدير بيانات التتبع إلى: {filename}")

        except Exception as e:
            messagebox.showerror("خطأ في التصدير", f"فشل في تصدير بيانات التتبع:\n{str(e)}")

    ttk.Button(actions_frame, text="🤖 إصلاح جميع المودات المعلقة", command=fix_all_pending).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="📄 تصدير بيانات التتبع", command=export_tracking_data).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="🗑️ مسح تاريخ المُصلحة", command=clear_fixed_history).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="إغلاق", command=tracking_window.destroy).pack(side=tk.RIGHT, padx=5)

def create_tracked_mods_tab(parent_frame, mods_data, tab_type):
    """إنشاء تبويب لعرض المودات المتتبعة"""

    # إطار للقائمة
    list_frame = ttk.Frame(parent_frame)
    list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # إنشاء قائمة مع شريط تمرير
    listbox_frame = ttk.Frame(list_frame)
    listbox_frame.pack(fill=tk.BOTH, expand=True)

    scrollbar = ttk.Scrollbar(listbox_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set, selectmode=tk.EXTENDED)
    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.config(command=listbox.yview)

    # ملء القائمة
    for mod_id, mod_data in mods_data.items():
        mod_name = mod_data.get('name', 'بدون اسم')
        category = mod_data.get('category', 'غير مصنف')
        severity = mod_data.get('severity', 'medium')
        fix_attempts = mod_data.get('fix_attempts', 0)

        # أيقونة حسب مستوى الخطورة
        severity_icon = "🔴" if severity == "high" else "🟡" if severity == "medium" else "🟢"

        if tab_type == "pending":
            display_text = f"{severity_icon} ID: {mod_id} - {mod_name} ({category}) - محاولات: {fix_attempts}"
        else:  # fixed
            fixed_date = mod_data.get('fixed_timestamp', '')[:10] if mod_data.get('fixed_timestamp') else ''
            display_text = f"✅ ID: {mod_id} - {mod_name} ({category}) - تم الإصلاح: {fixed_date}"

        listbox.insert(tk.END, display_text)

    # إطار للأزرار
    buttons_frame = ttk.Frame(parent_frame)
    buttons_frame.pack(fill=tk.X, padx=5, pady=5)

    def select_all_tracked():
        listbox.select_set(0, tk.END)

    def view_tracked_mod_details():
        selected_indices = listbox.curselection()
        if len(selected_indices) != 1:
            messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط للعرض.")
            return

        mod_ids = list(mods_data.keys())
        selected_mod_id = mod_ids[selected_indices[0]]
        mod_data = mods_data[selected_mod_id]

        show_tracked_mod_details(selected_mod_id, mod_data, tab_type)

    def fix_selected_tracked_mods():
        if tab_type != "pending":
            messagebox.showinfo("غير متاح", "هذه الميزة متاحة فقط للمودات التي تحتاج إصلاح.")
            return

        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("لا يوجد تحديد", "الرجاء تحديد مود واحد على الأقل.")
            return

        # جمع المودات المحددة
        mod_ids = list(mods_data.keys())
        selected_mod_ids = [mod_ids[i] for i in selected_indices]

        # البحث عن المودات في البيانات المحملة
        mods_to_fix = []
        for mod_id in selected_mod_ids:
            for loaded_mod in loaded_mods_data_list:
                if str(loaded_mod.get('id')) == str(mod_id):
                    mods_to_fix.append(loaded_mod)
                    break

        if mods_to_fix:
            handle_auto_fix_problematic_mods(mods_to_fix)
        else:
            messagebox.showwarning("خطأ", "لم يتم العثور على المودات في البيانات المحملة.")

    def remove_from_tracking():
        if tab_type != "pending":
            messagebox.showinfo("غير متاح", "لا يمكن حذف المودات المُصلحة من التتبع.")
            return

        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("لا يوجد تحديد", "الرجاء تحديد مود واحد على الأقل.")
            return

        confirm = messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف {len(selected_indices)} مود من التتبع؟")
        if not confirm:
            return

        try:
            # حذف المودات المحددة
            mod_ids = list(mods_data.keys())
            selected_mod_ids = [mod_ids[i] for i in selected_indices]

            problematic_data = load_problematic_mods_from_file()
            for mod_id in selected_mod_ids:
                if str(mod_id) in problematic_data:
                    del problematic_data[str(mod_id)]

            # حفظ الملف المحدث
            with open(PROBLEMATIC_MODS_FILE, 'w', encoding='utf-8') as f:
                json.dump(problematic_data, f, indent=2, ensure_ascii=False)

            update_status(f"✅ تم حذف {len(selected_mod_ids)} مود من التتبع")
            messagebox.showinfo("تم الحذف", f"تم حذف {len(selected_mod_ids)} مود من التتبع.")

            # إعادة تحميل النافذة
            parent_frame.master.master.destroy()  # إغلاق النافذة الحالية
            show_tracked_problematic_mods()  # إعادة فتحها

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المودات: {e}")

    ttk.Button(buttons_frame, text="تحديد الكل", command=select_all_tracked).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="عرض التفاصيل", command=view_tracked_mod_details).pack(side=tk.LEFT, padx=5)

    if tab_type == "pending":
        ttk.Button(buttons_frame, text="🤖 إصلاح المحدد", command=fix_selected_tracked_mods).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="🗑️ حذف من التتبع", command=remove_from_tracking).pack(side=tk.LEFT, padx=5)

def show_tracked_mod_details(mod_id, mod_data, tab_type):
    """عرض تفاصيل المود المتتبع"""

    details_window = tk.Toplevel()
    details_window.title(f"تفاصيل المود المتتبع: {mod_data.get('name', 'بدون اسم')}")
    details_window.geometry("800x700")
    details_window.resizable(True, True)

    # إطار رئيسي
    main_frame = ttk.Frame(details_window, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # معلومات أساسية
    info_frame = ttk.LabelFrame(main_frame, text="معلومات المود", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 10))

    ttk.Label(info_frame, text=f"ID: {mod_id}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الاسم: {mod_data.get('name', 'بدون اسم')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الفئة: {mod_data.get('category', 'غير مصنف')}").pack(anchor="w")

    # معلومات التتبع
    tracking_frame = ttk.LabelFrame(main_frame, text="معلومات التتبع", padding=10)
    tracking_frame.pack(fill=tk.X, pady=(0, 10))

    severity = mod_data.get('severity', 'medium')
    severity_text = "عالية" if severity == "high" else "متوسطة" if severity == "medium" else "منخفضة"
    severity_color = "red" if severity == "high" else "orange" if severity == "medium" else "green"

    ttk.Label(tracking_frame, text=f"مستوى الخطورة: {severity_text}", foreground=severity_color).pack(anchor="w")
    ttk.Label(tracking_frame, text=f"تاريخ الاكتشاف: {mod_data.get('first_detected', '')[:19]}").pack(anchor="w")
    ttk.Label(tracking_frame, text=f"آخر تحديث: {mod_data.get('last_updated', '')[:19]}").pack(anchor="w")
    ttk.Label(tracking_frame, text=f"محاولات الإصلاح: {mod_data.get('fix_attempts', 0)}").pack(anchor="w")

    if tab_type == "fixed":
        ttk.Label(tracking_frame, text=f"تاريخ الإصلاح: {mod_data.get('fixed_timestamp', '')[:19]}", foreground="green").pack(anchor="w")

    # المشاكل المكتشفة
    problems = mod_data.get('problems_detected', [])
    if problems:
        problems_frame = ttk.LabelFrame(main_frame, text="المشاكل المكتشفة", padding=10)
        problems_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        problems_text = scrolledtext.ScrolledText(problems_frame, height=8, wrap=tk.WORD)
        problems_text.pack(fill=tk.BOTH, expand=True)

        for i, problem in enumerate(problems, 1):
            problems_text.insert(tk.END, f"{i}. {problem}\n")

    # التوصية
    recommendation = mod_data.get('recommendation', '')
    if recommendation:
        rec_frame = ttk.LabelFrame(main_frame, text="التوصية", padding=10)
        rec_frame.pack(fill=tk.X, pady=(0, 10))

        rec_text = scrolledtext.ScrolledText(rec_frame, height=4, wrap=tk.WORD)
        rec_text.pack(fill=tk.X)
        rec_text.insert(tk.END, recommendation)

    # أزرار العمليات
    actions_frame = ttk.Frame(main_frame)
    actions_frame.pack(fill=tk.X, pady=(10, 0))

    def fix_this_tracked_mod():
        if tab_type != "pending":
            messagebox.showinfo("غير متاح", "هذا المود تم إصلاحه بالفعل.")
            return

        # البحث عن المود في البيانات المحملة
        for loaded_mod in loaded_mods_data_list:
            if str(loaded_mod.get('id')) == str(mod_id):
                handle_auto_fix_problematic_mods([loaded_mod])
                details_window.destroy()
                return

        messagebox.showwarning("خطأ", "لم يتم العثور على المود في البيانات المحملة.")

    def mark_as_manually_fixed():
        if tab_type != "pending":
            messagebox.showinfo("غير متاح", "هذا المود تم إصلاحه بالفعل.")
            return

        confirm = messagebox.askyesno("تأكيد الإصلاح", "هل تريد تمييز هذا المود كمُصلح يدوياً؟")
        if confirm:
            if mark_mod_as_fixed(mod_id):
                messagebox.showinfo("تم التمييز", "تم تمييز المود كمُصلح.")
                details_window.destroy()

    def remove_from_tracking_single():
        if tab_type != "pending":
            messagebox.showinfo("غير متاح", "لا يمكن حذف المودات المُصلحة من التتبع.")
            return

        confirm = messagebox.askyesno("تأكيد الحذف", "هل تريد حذف هذا المود من التتبع؟")
        if confirm:
            try:
                problematic_data = load_problematic_mods_from_file()
                if str(mod_id) in problematic_data:
                    del problematic_data[str(mod_id)]

                    with open(PROBLEMATIC_MODS_FILE, 'w', encoding='utf-8') as f:
                        json.dump(problematic_data, f, indent=2, ensure_ascii=False)

                    update_status(f"✅ تم حذف المود {mod_id} من التتبع")
                    messagebox.showinfo("تم الحذف", "تم حذف المود من التتبع.")
                    details_window.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المود: {e}")

    if tab_type == "pending":
        ttk.Button(actions_frame, text="🤖 إصلاح هذا المود", command=fix_this_tracked_mod).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="✅ تمييز كمُصلح يدوياً", command=mark_as_manually_fixed).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="🗑️ حذف من التتبع", command=remove_from_tracking_single).pack(side=tk.LEFT, padx=5)

    ttk.Button(actions_frame, text="إغلاق", command=details_window.destroy).pack(side=tk.RIGHT, padx=5)

def show_gemini_broken_mods_results(broken_mods):
    """عرض نتائج فحص Gemini للمودات المعيبة"""

    results_window = tk.Toplevel()
    results_window.title("🤖 نتائج فحص Gemini")
    results_window.geometry("900x600")
    results_window.resizable(True, True)

    # إطار رئيسي
    main_frame = ttk.Frame(results_window, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # معلومات إجمالية
    info_frame = ttk.LabelFrame(main_frame, text="نتائج فحص Gemini الذكي", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 10))

    info_text = f"تم العثور على {len(broken_mods)} مود يحتوي على أوصاف تيليجرام معيبة"
    ttk.Label(info_frame, text=info_text, font=("Arial", 12, "bold"), foreground="red").pack()

    # قائمة المودات المعيبة
    list_frame = ttk.LabelFrame(main_frame, text="المودات المعيبة", padding=10)
    list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    # إنشاء قائمة مع شريط تمرير
    listbox_frame = ttk.Frame(list_frame)
    listbox_frame.pack(fill=tk.BOTH, expand=True)

    scrollbar = ttk.Scrollbar(listbox_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set, selectmode=tk.EXTENDED)
    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.config(command=listbox.yview)

    # ملء القائمة
    for mod in broken_mods:
        mod_name = mod.get('name', 'بدون اسم')
        mod_id = mod.get('id', 'N/A')
        category = mod.get('category', 'غير مصنف')
        reason = mod.get('gemini_broken_reason', 'وصف معيب')

        display_text = f"ID: {mod_id} - {mod_name} ({category}) - السبب: {reason}"
        listbox.insert(tk.END, display_text)

    # أزرار العمليات
    buttons_frame = ttk.Frame(main_frame)
    buttons_frame.pack(fill=tk.X, pady=(10, 0))

    def select_all_gemini():
        listbox.select_set(0, tk.END)

    def fix_selected_gemini():
        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("لا يوجد تحديد", "الرجاء تحديد مود واحد على الأقل.")
            return

        selected_mods = [broken_mods[i] for i in selected_indices]
        handle_batch_description_generation(selected_mods, description_type="telegram")

    ttk.Button(buttons_frame, text="تحديد الكل", command=select_all_gemini).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="إصلاح المحدد", command=fix_selected_gemini).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="إغلاق", command=results_window.destroy).pack(side=tk.RIGHT, padx=5)

    # إنشاء نافذة لعرض النتائج
    results_window = tk.Toplevel()
    results_window.title("المودات التي تفتقر لأوصاف تيليجرام")
    results_window.geometry("900x600")
    results_window.resizable(True, True)

    # إنشاء إطار رئيسي مع تبويبات
    notebook = ttk.Notebook(results_window)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # تبويب للمودات التي تفتقر لكلا الوصفين
    both_frame = ttk.Frame(notebook)
    notebook.add(both_frame, text=f"بدون أي وصف تيليجرام ({len(missing_telegram_both)})")
    create_mods_list_tab(both_frame, missing_telegram_both, "لا يوجد وصف تيليجرام إنجليزي أو عربي")

    # تبويب للمودات التي تفتقر للوصف الإنجليزي
    english_frame = ttk.Frame(notebook)
    notebook.add(english_frame, text=f"بدون وصف تيليجرام إنجليزي ({len(missing_telegram_en)})")
    create_mods_list_tab(english_frame, missing_telegram_en, "يوجد وصف تيليجرام عربي فقط")

    # تبويب للمودات التي تفتقر للوصف العربي
    arabic_frame = ttk.Frame(notebook)
    notebook.add(arabic_frame, text=f"بدون وصف تيليجرام عربي ({len(missing_telegram_ar)})")
    create_mods_list_tab(arabic_frame, missing_telegram_ar, "يوجد وصف تيليجرام إنجليزي فقط")

    # إضافة معلومات إجمالية
    info_frame = ttk.Frame(results_window)
    info_frame.pack(fill=tk.X, padx=10, pady=5)

    total_mods = len(loaded_mods_data_list)
    complete_telegram_mods = total_mods - len(missing_telegram_both) - len(missing_telegram_en) - len(missing_telegram_ar)

    info_text = f"إجمالي المودات: {total_mods} | مكتملة أوصاف تيليجرام: {complete_telegram_mods} | تحتاج إصلاح: {total_mods - complete_telegram_mods}"
    ttk.Label(info_frame, text=info_text, font=("Arial", 10, "bold")).pack()




def show_broken_mod_details(mod):
    """عرض تفاصيل المود المعيب مع إمكانية الإصلاح"""

    details_window = tk.Toplevel()
    details_window.title(f"تفاصيل المود المعيب: {mod.get('name', 'بدون اسم')}")
    details_window.geometry("800x700")
    details_window.resizable(True, True)

    # إطار رئيسي مع تمرير
    main_frame = ttk.Frame(details_window)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # معلومات أساسية
    info_frame = ttk.LabelFrame(main_frame, text="معلومات أساسية", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 10))

    ttk.Label(info_frame, text=f"ID: {mod.get('id', 'N/A')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الاسم: {mod.get('name', 'بدون اسم')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الفئة: {mod.get('category', 'غير مصنف')}").pack(anchor="w")

    # أسباب المشكلة
    reasons_text = " | ".join(mod.get('broken_reasons', []))
    ttk.Label(info_frame, text=f"أسباب المشكلة: {reasons_text}", foreground="red").pack(anchor="w")

    # الوصف التيليجرام الإنجليزي المعيب
    english_frame = ttk.LabelFrame(main_frame, text="الوصف التيليجرام الإنجليزي المعيب", padding=10)
    english_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    english_text = scrolledtext.ScrolledText(english_frame, height=6, wrap=tk.WORD)
    english_text.pack(fill=tk.BOTH, expand=True)

    current_english = mod.get('telegram_description_en', '')
    english_text.insert(tk.END, current_english)

    # الوصف التيليجرام العربي المعيب
    arabic_frame = ttk.LabelFrame(main_frame, text="الوصف التيليجرام العربي المعيب", padding=10)
    arabic_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    arabic_text = scrolledtext.ScrolledText(arabic_frame, height=6, wrap=tk.WORD)
    arabic_text.pack(fill=tk.BOTH, expand=True)

    current_arabic = mod.get('telegram_description_ar', '')
    arabic_text.insert(tk.END, current_arabic)

    # أزرار العمليات
    actions_frame = ttk.Frame(main_frame)
    actions_frame.pack(fill=tk.X, pady=(10, 0))

    def fix_telegram_descriptions():
        """إصلاح أوصاف التيليجرام للمود"""
        update_status(f"جاري إصلاح أوصاف تيليجرام للمود: {mod.get('name', '')}")

        def fix_task():
            try:
                telegram_descriptions = generate_telegram_custom_description(mod)
                if telegram_descriptions:
                    new_english = telegram_descriptions.get('en', '')
                    new_arabic = telegram_descriptions.get('ar', '')

                    # تحديث النصوص في النافذة
                    english_text.delete("1.0", tk.END)
                    english_text.insert(tk.END, new_english)

                    arabic_text.delete("1.0", tk.END)
                    arabic_text.insert(tk.END, new_arabic)

                    update_status("✅ تم إصلاح أوصاف التيليجرام بنجاح!")
                    messagebox.showinfo("نجح الإصلاح", "تم إصلاح أوصاف التيليجرام. يمكنك الآن حفظ التغييرات.")
                else:
                    update_status("❌ فشل في إصلاح أوصاف التيليجرام")
                    messagebox.showerror("خطأ", "فشل في إصلاح أوصاف التيليجرام.")
            except Exception as e:
                update_status(f"❌ خطأ في إصلاح أوصاف التيليجرام: {e}")
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

        # تشغيل المهمة في خيط منفصل
        import threading
        thread = threading.Thread(target=fix_task)
        thread.daemon = True
        thread.start()

    def save_fixed_descriptions():
        new_english = english_text.get("1.0", tk.END)
        new_english = new_english.strip() if new_english else ""
        new_arabic = arabic_text.get("1.0", tk.END)
        new_arabic = new_arabic.strip() if new_arabic else ""

        if update_mod_telegram_descriptions(mod['id'], new_english, new_arabic):
            messagebox.showinfo("تم الحفظ", "تم حفظ أوصاف التيليجرام المُصلحة بنجاح!")
            details_window.destroy()
        else:
            messagebox.showerror("خطأ", "فشل في حفظ أوصاف التيليجرام.")

    ttk.Button(actions_frame, text="إصلاح أوصاف التيليجرام", command=fix_telegram_descriptions).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="حفظ التغييرات", command=save_fixed_descriptions).pack(side=tk.RIGHT, padx=5)
    ttk.Button(actions_frame, text="إغلاق", command=details_window.destroy).pack(side=tk.RIGHT, padx=5)

def create_mods_list_tab(parent_frame, mods_list, description):
    """إنشاء تبويب لعرض قائمة المودات"""

    # إطار للوصف
    desc_frame = ttk.Frame(parent_frame)
    desc_frame.pack(fill=tk.X, padx=5, pady=5)
    ttk.Label(desc_frame, text=description, font=("Arial", 10)).pack()

    # إطار للقائمة
    list_frame = ttk.Frame(parent_frame)
    list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # إنشاء قائمة مع شريط تمرير
    listbox_frame = ttk.Frame(list_frame)
    listbox_frame.pack(fill=tk.BOTH, expand=True)

    scrollbar = ttk.Scrollbar(listbox_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set, selectmode=tk.EXTENDED)
    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.config(command=listbox.yview)

    # ملء القائمة
    for mod in mods_list:
        mod_name = mod.get('name', 'بدون اسم')
        mod_id = mod.get('id', 'N/A')
        category = mod.get('category', 'غير مصنف')
        
        # Check if this is a Telegram description tab
        if "تيليجرام" in description:
            telegram_description_en = mod.get('telegram_description_en', '')
            telegram_description_ar = mod.get('telegram_description_ar', '')
            has_telegram_en = bool(telegram_description_en and telegram_description_en.strip())
            has_telegram_ar = bool(telegram_description_ar and telegram_description_ar.strip())
            
            telegram_status = ""
            if has_telegram_en and has_telegram_ar:
                telegram_status = "📱 (EN+AR)"
            elif has_telegram_en:
                telegram_status = "📱 (EN)"
            elif has_telegram_ar:
                telegram_status = "📱 (AR)"
            else:
                telegram_status = "❌"
                
            display_text = f"ID: {mod_id} - {mod_name} ({category}) {telegram_status}"
        else:
            display_text = f"ID: {mod_id} - {mod_name} ({category})"
        listbox.insert(tk.END, display_text)

    # إطار للأزرار
    buttons_frame = ttk.Frame(parent_frame)
    buttons_frame.pack(fill=tk.X, padx=5, pady=5)

    def select_all_in_tab():
        listbox.select_set(0, tk.END)

    def generate_descriptions_for_selected():
        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("لا يوجد تحديد", "الرجاء تحديد مود واحد على الأقل.")
            return

        selected_mods = [mods_list[i] for i in selected_indices]
        handle_batch_description_generation(selected_mods)

    def view_selected_mod():
        selected_indices = listbox.curselection()
        if len(selected_indices) != 1:
            messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط للعرض.")
            return

        mod = mods_list[selected_indices[0]]
        show_mod_details(mod)

    ttk.Button(buttons_frame, text="تحديد الكل", command=select_all_in_tab).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="إنشاء أوصاف للمحدد", command=lambda: handle_batch_description_generation([mods_list[i] for i in listbox.curselection()])).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="إنشاء أوصاف تيليجرام للمحدد", command=lambda: handle_batch_description_generation([mods_list[i] for i in listbox.curselection()], description_type="telegram")).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="عرض تفاصيل المود", command=view_selected_mod).pack(side=tk.LEFT, padx=5)

def show_mod_details(mod):
    """عرض تفاصيل المود مع إمكانية تعديل الأوصاف"""

    details_window = tk.Toplevel()
    details_window.title(f"تفاصيل المود: {mod.get('name', 'بدون اسم')}")
    details_window.geometry("800x700")
    details_window.resizable(True, True)

    # إطار رئيسي مع تمرير
    main_frame = ttk.Frame(details_window)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # معلومات أساسية
    info_frame = ttk.LabelFrame(main_frame, text="معلومات أساسية", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 10))

    ttk.Label(info_frame, text=f"ID: {mod.get('id', 'N/A')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الاسم: {mod.get('name', 'بدون اسم')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الفئة: {mod.get('category', 'غير مصنف')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"الإصدار: {mod.get('version', 'غير محدد')}").pack(anchor="w")
    ttk.Label(info_frame, text=f"التحميلات: {mod.get('downloads', 0)}").pack(anchor="w")

    # الوصف الإنجليزي
    english_frame = ttk.LabelFrame(main_frame, text="الوصف الإنجليزي", padding=10)
    english_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    english_text = scrolledtext.ScrolledText(english_frame, height=8, wrap=tk.WORD)
    english_text.pack(fill=tk.BOTH, expand=True)

    current_english = mod.get('description', '')
    english_text.insert(tk.END, current_english)

    # الوصف العربي
    arabic_frame = ttk.LabelFrame(main_frame, text="الوصف العربي", padding=10)
    arabic_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    arabic_text = scrolledtext.ScrolledText(arabic_frame, height=8, wrap=tk.WORD)
    arabic_text.pack(fill=tk.BOTH, expand=True)

    current_arabic = mod.get('description_ar', '')
    arabic_text.insert(tk.END, current_arabic)

    # أزرار العمليات
    actions_frame = ttk.Frame(main_frame)
    actions_frame.pack(fill=tk.X, pady=(10, 0))

    def generate_english_from_arabic():
        arabic_desc = arabic_text.get("1.0", tk.END)
        arabic_desc = arabic_desc.strip() if arabic_desc else ""
        if not arabic_desc:
            messagebox.showwarning("لا يوجد محتوى", "لا يوجد وصف عربي لترجمته.")
            return

        generate_description_from_existing(mod, arabic_desc, "arabic_to_english", english_text)

    def generate_arabic_from_english():
        english_desc = english_text.get("1.0", tk.END)
        english_desc = english_desc.strip() if english_desc else ""
        if not english_desc:
            messagebox.showwarning("لا يوجد محتوى", "لا يوجد وصف إنجليزي لترجمته.")
            return

        generate_description_from_existing(mod, english_desc, "english_to_arabic", arabic_text)

    def save_descriptions():
        new_english = english_text.get("1.0", tk.END)
        new_english = new_english.strip() if new_english else ""
        new_arabic = arabic_text.get("1.0", tk.END)
        new_arabic = new_arabic.strip() if new_arabic else ""

        update_mod_descriptions(mod['id'], new_english, new_arabic)
        details_window.destroy()

    ttk.Button(actions_frame, text="إنشاء إنجليزي من العربي", command=generate_english_from_arabic).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="إنشاء عربي من الإنجليزي", command=generate_arabic_from_english).pack(side=tk.LEFT, padx=5)
    ttk.Button(actions_frame, text="حفظ التغييرات", command=save_descriptions).pack(side=tk.RIGHT, padx=5)
    ttk.Button(actions_frame, text="إغلاق", command=details_window.destroy).pack(side=tk.RIGHT, padx=5)

def generate_description_from_existing(mod, existing_description, conversion_type, target_text_widget):
    """إنشاء وصف جديد بناءً على وصف موجود"""

    if not REPAIR_GEMINI_CLIENT_OK:
        messagebox.showerror("خطأ", "Gemini غير متوفر. الرجاء التحقق من إعدادات API.")
        return

    # تحديد نوع التحويل والرسالة
    if conversion_type == "arabic_to_english":
        prompt = f"""
        قم بترجمة وتحسين هذا الوصف العربي لمود ماين كرافت إلى اللغة الإنجليزية.
        اجعل الترجمة طبيعية ومتدفقة وجذابة للاعبين الناطقين بالإنجليزية.

        اسم المود: {mod.get('name', '')}
        الفئة: {mod.get('category', '')}

        الوصف العربي:
        {existing_description}

        اكتب وصفاً إنجليزياً جذاباً ومفصلاً:
        """
        status_msg = "جاري إنشاء الوصف الإنجليزي..."

    elif conversion_type == "english_to_arabic":
        prompt = f"""
        قم بترجمة وتحسين هذا الوصف الإنجليزي لمود ماين كرافت إلى اللغة العربية.
        اجعل الترجمة طبيعية ومتدفقة وجذابة للاعبين العرب.

        اسم المود: {mod.get('name', '')}
        الفئة: {mod.get('category', '')}

        الوصف الإنجليزي:
        {existing_description}

        اكتب وصفاً عربياً جذاباً ومفصلاً:
        """
        status_msg = "جاري إنشاء الوصف العربي..."

    else:
        messagebox.showerror("خطأ", "نوع تحويل غير صالح.")
        return

    # عرض رسالة التحميل
    update_status(status_msg)

    # تشغيل الطلب في خيط منفصل
    def generate_task():
        try:
            generated_description = smart_repair_gemini_request(prompt)

            if generated_description:
                # تحديث النص في الواجهة الرئيسية
                target_text_widget.delete("1.0", tk.END)
                target_text_widget.insert(tk.END, generated_description)
                update_status("✅ تم إنشاء الوصف بنجاح!")
            else:
                update_status("❌ فشل في إنشاء الوصف")
                messagebox.showerror("خطأ", "فشل في إنشاء الوصف. الرجاء المحاولة مرة أخرى.")

        except Exception as e:
            update_status(f"❌ خطأ في إنشاء الوصف: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    # تشغيل المهمة في خيط منفصل
    import threading
    thread = threading.Thread(target=generate_task)
    thread.daemon = True
    thread.start()

def handle_batch_description_generation(selected_mods, description_type="normal"): # Added description_type parameter
    """معالجة إنشاء أوصاف لعدة مودات"""

    if not REPAIR_GEMINI_CLIENT_OK:
        messagebox.showerror("خطأ", "Gemini غير متوفر. الرجاء التحقق من إعدادات API.")
        return

    if not selected_mods:
        messagebox.showwarning("لا يوجد تحديد", "لا توجد مودات محددة.")
        return

    # نافذة خيارات المعالجة المجمعة
    batch_window = tk.Toplevel()
    batch_window.title("إنشاء أوصاف مجمعة")
    batch_window.geometry("500x400")
    batch_window.resizable(False, False)

    # إطار رئيسي
    main_frame = ttk.Frame(batch_window, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # معلومات المودات المحددة
    info_frame = ttk.LabelFrame(main_frame, text="المودات المحددة", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 10))

    ttk.Label(info_frame, text=f"عدد المودات: {len(selected_mods)}", font=("Arial", 10, "bold")).pack(anchor="w")

    # خيارات الإنشاء
    options_frame = ttk.LabelFrame(main_frame, text="خيارات الإنشاء", padding=10)
    options_frame.pack(fill=tk.X, pady=(0, 10))

    generation_type = tk.StringVar(value="missing_only")

    ttk.Radiobutton(options_frame, text="إنشاء الأوصاف المفقودة فقط",
                   variable=generation_type, value="missing_only").pack(anchor="w", pady=2)
    ttk.Radiobutton(options_frame, text="إعادة إنشاء جميع الأوصاف",
                   variable=generation_type, value="regenerate_all").pack(anchor="w", pady=2)
    ttk.Radiobutton(options_frame, text="إنشاء الأوصاف العربية من الإنجليزية",
                   variable=generation_type, value="english_to_arabic").pack(anchor="w", pady=2)
    ttk.Radiobutton(options_frame, text="إنشاء الأوصاف الإنجليزية من العربية",
                   variable=generation_type, value="arabic_to_english").pack(anchor="w", pady=2)
    
    # NEW: Option for Telegram descriptions
    if description_type == "telegram":
        ttk.Radiobutton(options_frame, text="إنشاء أوصاف تيليجرام المفقودة فقط",
                       variable=generation_type, value="telegram_missing_only").pack(anchor="w", pady=2)
        ttk.Radiobutton(options_frame, text="إعادة إنشاء جميع أوصاف تيليجرام",
                       variable=generation_type, value="telegram_regenerate_all").pack(anchor="w", pady=2)


    # شريط التقدم
    progress_frame = ttk.LabelFrame(main_frame, text="التقدم", padding=10)
    progress_frame.pack(fill=tk.X, pady=(0, 10))

    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100)
    progress_bar.pack(fill=tk.X, pady=(0, 5))

    progress_label = ttk.Label(progress_frame, text="جاهز للبدء...")
    progress_label.pack()

    # أزرار التحكم
    buttons_frame = ttk.Frame(main_frame)
    buttons_frame.pack(fill=tk.X, pady=(10, 0))

    def start_batch_generation():
        """بدء عملية الإنشاء المجمعة"""

        def batch_task():
            try:
                total_mods = len(selected_mods)
                processed = 0
                successful = 0
                skipped_count = 0 # Added for skipping already existing descriptions

                for i, mod in enumerate(selected_mods):
                    mod_name = mod.get('name', 'بدون اسم')
                    progress_label.config(text=f"معالجة: {mod_name}")

                    # تحديد ما يجب إنشاؤه
                    should_generate_english = False
                    should_generate_arabic = False
                    should_update_telegram = False # New flag for Telegram descriptions

                    current_english = mod.get('description', '')
                    current_english = current_english.strip() if current_english else ""
                    current_arabic = mod.get('description_ar', '')
                    current_arabic = current_arabic.strip() if current_arabic else ""
                    
                    # NEW: Get current Telegram descriptions
                    current_telegram_english = mod.get('telegram_description_en', '')
                    current_telegram_english = current_telegram_english.strip() if current_telegram_english else ""
                    current_telegram_arabic = mod.get('telegram_description_ar', '')
                    current_telegram_arabic = current_telegram_arabic.strip() if current_telegram_arabic else ""


                    gen_type = generation_type.get()

                    if gen_type == "missing_only":
                        should_generate_english = not current_english
                        should_generate_arabic = not current_arabic
                    elif gen_type == "regenerate_all":
                        should_generate_english = True
                        should_generate_arabic = True
                    elif gen_type == "english_to_arabic":
                        should_generate_arabic = bool(current_english)
                    elif gen_type == "arabic_to_english":
                        should_generate_english = bool(current_arabic)
                    elif gen_type == "telegram_missing_only": # NEW: Telegram missing only
                        if current_telegram_english and current_telegram_arabic:
                            progress_label.config(text=f"تخطي: {mod_name} (يحتوي على وصف تيليجرام)")
                            skipped_count += 1
                            processed += 1
                            progress_percent = (processed / total_mods) * 100
                            progress_var.set(progress_percent)
                            batch_window.update()
                            continue # Skip to next mod
                        should_update_telegram = True
                    elif gen_type == "telegram_regenerate_all": # NEW: Telegram regenerate all
                        should_update_telegram = True

                    # إنشاء الأوصاف
                    new_english = current_english
                    new_arabic = current_arabic
                    new_telegram_english = current_telegram_english # Initialize with existing
                    new_telegram_arabic = current_telegram_arabic # Initialize with existing

                    if should_generate_english:
                        if gen_type == "arabic_to_english" and current_arabic:
                            # ترجمة من العربي للإنجليزي
                            prompt = f"""
                            قم بترجمة هذا الوصف العربي لمود ماين كرافت إلى اللغة الإنجليزية:

                            اسم المود: {mod_name}
                            الوصف العربي: {current_arabic}

                            اكتب وصفاً إنجليزياً جذاباً:
                            """
                            new_english = smart_repair_gemini_request(prompt)
                        else:
                            # إنشاء وصف إنجليزي جديد
                            new_english = generate_smart_description(mod, "english")

                    if should_generate_arabic:
                        if gen_type == "english_to_arabic" and current_english:
                            # ترجمة من الإنجليزي للعربي
                            prompt = f"""
                            قم بترجمة هذا الوصف الإنجليزي لمود ماين كرافت إلى اللغة العربية:

                            اسم المود: {mod_name}
                            الوصف الإنجليزي: {current_english}

                            اكتب وصفاً عربياً جذاباً:
                            """
                            new_arabic = smart_repair_gemini_request(prompt)
                        else:
                            # إنشاء وصف عربي جديد
                            new_arabic = generate_smart_description(mod, "arabic")
                    
                    if should_update_telegram: # NEW: Handle Telegram description generation
                        telegram_descriptions = generate_telegram_custom_description(mod)
                        if telegram_descriptions:
                            new_telegram_english = telegram_descriptions.get('en', '')
                            new_telegram_arabic = telegram_descriptions.get('ar', '')

                    # حفظ التحديثات
                    if should_update_telegram: # NEW: Update Telegram descriptions
                        if update_mod_telegram_descriptions(mod['id'], new_telegram_english, new_telegram_arabic):
                            successful += 1
                    elif new_english or new_arabic:
                        if update_mod_descriptions(mod['id'], new_english or current_english, new_arabic or current_arabic):
                            successful += 1

                    processed += 1
                    progress_percent = (processed / total_mods) * 100
                    progress_var.set(progress_percent)

                    # تحديث النافذة
                    batch_window.update()
                    time.sleep(1) # Add a small delay to avoid rate limiting and ensure sequential processing

                progress_label.config(text=f"اكتمل! تم معالجة {successful} من {total_mods} مود بنجاح. تم تخطي {skipped_count} مود.")
                messagebox.showinfo("اكتمل", f"تم إنشاء أوصاف لـ {successful} مود من أصل {total_mods}. تم تخطي {skipped_count} مود.")

            except Exception as e:
                progress_label.config(text=f"خطأ: {e}")
                messagebox.showerror("خطأ", f"حدث خطأ أثناء المعالجة: {e}")

        # تشغيل المهمة في خيط منفصل
        import threading
        thread = threading.Thread(target=batch_task)
        thread.daemon = True
        thread.start()

    ttk.Button(buttons_frame, text="بدء الإنشاء", command=start_batch_generation).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="إغلاق", command=batch_window.destroy).pack(side=tk.RIGHT, padx=5)

def generate_smart_description(mod, language):
    """إنشاء وصف ذكي لمود"""

    mod_name = mod.get('name', '')
    mod_category = mod.get('category', '')

    if language == "english":
        prompt = f"""
        Create an engaging English description for this Minecraft mod:

        Name: {mod_name}
        Category: {mod_category}

        Write a compelling description that explains what the mod does and why players would want to use it.
        Make it natural and flowing, around 100-200 words.
        """
    else:  # arabic
        prompt = f"""
        أنشئ وصفاً عربياً جذاباً لهذا المود في ماين كرافت:

        الاسم: {mod_name}
        الفئة: {mod_category}

        اكتب وصفاً مقنعاً يشرح ما يفعله المود ولماذا يرغب اللاعبون في استخدامه.
        اجعله طبيعياً ومتدفقاً، حوالي 100-200 كلمة.
        """

    return smart_repair_gemini_request(prompt)

def update_mod_descriptions(mod_id, english_description, arabic_description):
    """تحديث أوصاف المود في قاعدة البيانات"""

    if not APP_DB_CLIENT_OK:
        messagebox.showerror("خطأ", "قاعدة البيانات غير متوفرة.")
        return False

    try:
        update_data = {}

        if english_description:
            update_data['description'] = english_description

        if arabic_description:
            update_data['description_ar'] = arabic_description

        if not update_data:
            return False

        response = app_db_client.table('mods').update(update_data).eq('id', mod_id).execute()

        if response.data:
            update_status(f"✅ تم تحديث أوصاف المود {mod_id}")

            # تحديث البيانات المحلية
            for mod in loaded_mods_data_list:
                if mod['id'] == mod_id:
                    if english_description:
                        mod['description'] = english_description
                    if arabic_description:
                        mod['description_ar'] = arabic_description
                    break

            return True
        else:
            update_status(f"❌ فشل في تحديث أوصاف المود {mod_id}")
            return False

    except Exception as e:
        error_message = str(e)

        # التعامل مع خطأ العمود المفقود 'updated_at'
        if ("updated_at" in error_message and ("PGRST204" in error_message or "42703" in error_message)) or \
           ('record "new" has no field "updated_at"' in error_message):
            update_status(f"⚠️ تحذير: العمود 'updated_at' غير موجود في قاعدة البيانات. سيتم تجاهل هذا الخطأ.")

            # محاولة التحديث مرة أخرى بدون تحديث الطابع الزمني
            try:
                # إعادة المحاولة مع تجاهل خطأ updated_at
                response = app_db_client.table('mods').update(update_data).eq('id', mod_id).execute()

                if response.data:
                    update_status(f"✅ تم تحديث أوصاف المود {mod_id} (تم تجاهل خطأ updated_at)")

                    # تحديث البيانات المحلية
                    for mod in loaded_mods_data_list:
                        if mod['id'] == mod_id:
                            if english_description:
                                mod['description'] = english_description
                            if arabic_description:
                                mod['description_ar'] = arabic_description
                            break

                    return True
                else:
                    update_status(f"❌ فشل في تحديث أوصاف المود {mod_id} حتى بعد تجاهل خطأ updated_at")
                    return False

            except Exception as retry_e:
                update_status(f"❌ خطأ في إعادة محاولة تحديث أوصاف المود {mod_id}: {retry_e}")
                return False
        else:
            # خطأ آخر غير متعلق بـ updated_at
            update_status(f"❌ خطأ في تحديث أوصاف المود {mod_id}: {e}")
            messagebox.showerror("خطأ", f"فشل في تحديث الأوصاف: {e}")
            return False

def show_database_fix_instructions():
    """عرض تعليمات إصلاح قاعدة البيانات لحل مشكلة updated_at"""

    instructions_window = tk.Toplevel()
    instructions_window.title("إصلاح قاعدة البيانات - مشكلة updated_at")
    instructions_window.geometry("800x600")
    instructions_window.resizable(True, True)

    # إطار رئيسي
    main_frame = ttk.Frame(instructions_window, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # عنوان
    title_label = ttk.Label(main_frame, text="إصلاح مشكلة العمود المفقود 'updated_at'",
                           font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 10))

    # شرح المشكلة
    problem_frame = ttk.LabelFrame(main_frame, text="وصف المشكلة", padding=10)
    problem_frame.pack(fill=tk.X, pady=(0, 10))

    problem_text = """
المشكلة: عند محاولة تحديث أوصاف المودات، يظهر خطأ:
"Could not find the 'updated_at' column of 'mods' in the schema cache"

السبب: قاعدة البيانات تحتوي على عمود 'created_at' فقط ولا تحتوي على عمود 'updated_at'
الذي يحاول Supabase تحديثه تلقائياً عند تعديل أي سجل.
    """

    ttk.Label(problem_frame, text=problem_text, justify=tk.LEFT, wraplength=750).pack(anchor="w")

    # الحل المؤقت
    temp_solution_frame = ttk.LabelFrame(main_frame, text="الحل المؤقت (مطبق حالياً)", padding=10)
    temp_solution_frame.pack(fill=tk.X, pady=(0, 10))

    temp_text = """
✅ تم تطبيق حل مؤقت في الكود:
- الأداة تتعامل مع خطأ 'updated_at' تلقائياً
- تتجاهل الخطأ وتكمل عملية التحديث
- تعرض رسالة تحذيرية بدلاً من إيقاف العملية
    """

    ttk.Label(temp_solution_frame, text=temp_text, justify=tk.LEFT, wraplength=750).pack(anchor="w")

    # الحل الدائم
    permanent_solution_frame = ttk.LabelFrame(main_frame, text="الحل الدائم (اختياري)", padding=10)
    permanent_solution_frame.pack(fill=tk.X, pady=(0, 10))

    permanent_text = """
لحل المشكلة نهائياً، يمكنك إضافة العمود المفقود إلى قاعدة البيانات:

1. افتح لوحة تحكم Supabase
2. اذهب إلى SQL Editor
3. نفذ الكود SQL التالي:
    """

    ttk.Label(permanent_solution_frame, text=permanent_text, justify=tk.LEFT, wraplength=750).pack(anchor="w")

    # منطقة الكود SQL
    sql_frame = ttk.Frame(permanent_solution_frame)
    sql_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

    sql_text = scrolledtext.ScrolledText(sql_frame, height=12, wrap=tk.WORD, font=("Consolas", 10))
    sql_text.pack(fill=tk.BOTH, expand=True)

    sql_code = """-- إضافة العمود المفقود updated_at
ALTER TABLE mods
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء trigger لتحديث updated_at عند أي تعديل
DROP TRIGGER IF EXISTS update_mods_updated_at ON mods;
CREATE TRIGGER update_mods_updated_at
    BEFORE UPDATE ON mods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- تحديث السجلات الموجودة
UPDATE mods
SET updated_at = created_at
WHERE updated_at IS NULL;"""

    sql_text.insert(tk.END, sql_code)
    sql_text.config(state=tk.DISABLED)

    # أزرار
    buttons_frame = ttk.Frame(main_frame)
    buttons_frame.pack(fill=tk.X, pady=(10, 0))

    def copy_sql_code():
        if PYPERCLIP_AVAILABLE:
            pyperclip.copy(sql_code)
            messagebox.showinfo("تم النسخ", "تم نسخ كود SQL إلى الحافظة!", parent=instructions_window)
        else:
            messagebox.showwarning("خطأ", "مكتبة pyperclip غير متوفرة لنسخ النص.", parent=instructions_window)

    def open_sql_file():
        try:
            import os
            import subprocess
            sql_file_path = "add_updated_at_column.sql"
            if os.path.exists(sql_file_path):
                subprocess.Popen(['notepad.exe', sql_file_path])
            else:
                messagebox.showwarning("ملف غير موجود", f"الملف {sql_file_path} غير موجود.", parent=instructions_window)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح الملف: {e}", parent=instructions_window)

    ttk.Button(buttons_frame, text="نسخ كود SQL", command=copy_sql_code).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="فتح ملف SQL", command=open_sql_file).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="إغلاق", command=instructions_window.destroy).pack(side=tk.RIGHT, padx=5)

def open_repair_gemini_management_dialog():
    """فتح نافذة إدارة مفاتيح Gemini API للإصلاح"""
    global REPAIR_GEMINI_API_KEYS, current_repair_gemini_key_index, REPAIR_GEMINI_CLIENT_OK

    # إنشاء نافذة جديدة
    gemini_window = tk.Toplevel()
    gemini_window.title("إدارة مفاتيح Gemini API - أداة الإصلاح")
    gemini_window.geometry("700x500")
    gemini_window.resizable(True, True)

    # جعل النافذة في المقدمة
    gemini_window.transient()
    gemini_window.grab_set()

    # إطار رئيسي مع تمرير
    main_frame = ttk.Frame(gemini_window)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # عنوان
    title_label = ttk.Label(main_frame, text="إدارة مفاتيح Gemini API - أداة الإصلاح", font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 10))

    # معلومات الحالة الحالية
    status_frame = ttk.LabelFrame(main_frame, text="الحالة الحالية", padding=10)
    status_frame.pack(fill=tk.X, pady=(0, 10))

    current_status_label = ttk.Label(status_frame, text="", font=("Arial", 10))
    current_status_label.pack(anchor=tk.W)

    def update_status_display():
        if REPAIR_GEMINI_CLIENT_OK and REPAIR_GEMINI_API_KEYS:
            status_text = f"✅ متصل - المفتاح النشط: {current_repair_gemini_key_index + 1} من {len(REPAIR_GEMINI_API_KEYS)}"
            if len(REPAIR_GEMINI_API_KEYS) > 0:
                active_key = REPAIR_GEMINI_API_KEYS[current_repair_gemini_key_index]
                masked_key = active_key[:8] + "..." + active_key[-8:] if len(active_key) > 16 else active_key
                status_text += f"\nالمفتاح: {masked_key}"
        elif REPAIR_GEMINI_API_KEYS:
            status_text = f"⚠️ غير متصل - {len(REPAIR_GEMINI_API_KEYS)} مفتاح محفوظ"
        else:
            status_text = "❌ لا توجد مفاتيح محفوظة"
        current_status_label.config(text=status_text)

    update_status_display()

    # قائمة المفاتيح
    keys_frame = ttk.LabelFrame(main_frame, text="المفاتيح المحفوظة", padding=10)
    keys_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

    # إطار للقائمة مع شريط تمرير
    list_frame = ttk.Frame(keys_frame)
    list_frame.pack(fill=tk.BOTH, expand=True)

    # قائمة المفاتيح
    keys_listbox = tk.Listbox(list_frame, font=("Consolas", 10))
    keys_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=keys_listbox.yview)
    keys_listbox.configure(yscrollcommand=keys_scrollbar.set)

    keys_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    keys_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def refresh_keys_list():
        keys_listbox.delete(0, tk.END)
        for i, key in enumerate(REPAIR_GEMINI_API_KEYS):
            masked_key = key[:12] + "..." + key[-12:] if len(key) > 24 else key
            key_id = get_api_key_id(key)

            # تحديد حالة المفتاح
            status_indicator = "⚪"
            status_text = ""

            if i == current_repair_gemini_key_index and REPAIR_GEMINI_CLIENT_OK:
                status_indicator = "🟢"
                status_text = " (نشط)"
            elif not is_api_key_available(key):
                # فحص سبب عدم التوفر
                current_time = time.time()
                current_date = time.strftime("%Y-%m-%d")

                if key_id in api_key_cooldown and current_time < api_key_cooldown[key_id]:
                    remaining_time = int((api_key_cooldown[key_id] - current_time) / 60)
                    status_indicator = "🔴"
                    status_text = f" (تبريد: {remaining_time}د)"
                elif key_id in api_key_usage_count:
                    usage_data = api_key_usage_count[key_id]
                    if isinstance(usage_data, dict):
                        if usage_data.get('date') == current_date and usage_data.get('count', 0) >= MAX_DAILY_USAGE_PER_KEY:
                            status_indicator = "🟡"
                            status_text = f" (حد يومي: {usage_data.get('count', 0)}/{MAX_DAILY_USAGE_PER_KEY})"
                    elif usage_data >= MAX_DAILY_USAGE_PER_KEY:
                        status_indicator = "🟡"
                        status_text = f" (حد يومي: {usage_data}/{MAX_DAILY_USAGE_PER_KEY})"
            else:
                # المفتاح متاح
                usage_count = 0
                if key_id in api_key_usage_count:
                    usage_data = api_key_usage_count[key_id]
                    if isinstance(usage_data, dict):
                        usage_count = usage_data.get('count', 0)
                    else:
                        usage_count = usage_data

                if usage_count > 0:
                    status_text = f" (استخدام: {usage_count}/{MAX_DAILY_USAGE_PER_KEY})"

            keys_listbox.insert(tk.END, f"{status_indicator} {i+1}. {masked_key}{status_text}")

    refresh_keys_list()

    # أزرار إدارة المفاتيح
    buttons_frame = ttk.Frame(keys_frame)
    buttons_frame.pack(fill=tk.X, pady=(10, 0))

    def add_new_key(key_value):
        if key_value:
            new_key_clean = key_value.strip()
            if new_key_clean:
                REPAIR_GEMINI_API_KEYS.append(new_key_clean)
                save_repair_config()
                refresh_keys_list()
                update_status_display()
                update_status("تم إضافة مفتاح API جديد")
                new_key_entry.delete(0, tk.END) # Clear the entry after adding

    def paste_from_clipboard():
        if PYPERCLIP_AVAILABLE:
            try:
                clipboard_content = pyperclip.paste()
                new_key_entry.delete(0, tk.END)
                new_key_entry.insert(0, clipboard_content)
            except Exception as e:
                messagebox.showerror("خطأ في اللصق", f"فشل في اللصق من الحافظة: {e}", parent=gemini_window)
        else:
            messagebox.showwarning("تحذير", "مكتبة pyperclip غير متاحة. الرجاء تثبيتها (pip install pyperclip).", parent=gemini_window)

    def test_selected_key():
        selection = keys_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "الرجاء اختيار مفتاح للاختبار", parent=gemini_window)
            return

        key_index = selection[0]
        if configure_repair_gemini_client(key_index):
            messagebox.showinfo("نجح الاختبار", f"المفتاح {key_index + 1} يعمل بشكل صحيح!", parent=gemini_window)
            refresh_keys_list()
            update_status_display()
        else:
            messagebox.showerror("فشل الاختبار", f"المفتاح {key_index + 1} لا يعمل أو غير صالح", parent=gemini_window)

    def delete_selected_key():
        selection = keys_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "الرجاء اختيار مفتاح للحذف", parent=gemini_window)
            return

        key_index = selection[0]
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المفتاح {key_index + 1}؟", parent=gemini_window):
            try:
                if key_index < len(REPAIR_GEMINI_API_KEYS):
                    REPAIR_GEMINI_API_KEYS.pop(key_index)
                    save_repair_config()

                    # إعادة تكوين العميل إذا تم حذف المفتاح النشط
                    global current_repair_gemini_key_index, REPAIR_GEMINI_CLIENT_OK
                    if key_index == current_repair_gemini_key_index:
                        if REPAIR_GEMINI_API_KEYS:
                            configure_repair_gemini_client(0)
                        else:
                            REPAIR_GEMINI_CLIENT_OK = False
                    elif key_index < current_repair_gemini_key_index:
                        current_repair_gemini_key_index -= 1

                    refresh_keys_list()
                    update_status_display()
                    messagebox.showinfo("تم الحذف", "تم حذف المفتاح بنجاح", parent=gemini_window)
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المفتاح: {e}", parent=gemini_window)

    def set_as_active():
        selection = keys_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "الرجاء اختيار مفتاح لتفعيله", parent=gemini_window)
            return

        key_index = selection[0]
        if configure_repair_gemini_client(key_index):
            messagebox.showinfo("تم التفعيل", f"تم تفعيل المفتاح {key_index + 1} بنجاح!", parent=gemini_window)
            refresh_keys_list()
            update_status_display()
        else:
            messagebox.showerror("فشل التفعيل", f"فشل في تفعيل المفتاح {key_index + 1}", parent=gemini_window)

    def reset_key_cooldown():
        """إعادة تعيين وقت التبريد للمفتاح المحدد"""
        selection = keys_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "الرجاء اختيار مفتاح لإعادة تعيين وقت التبريد", parent=gemini_window)
            return

        key_index = selection[0]
        if key_index < len(REPAIR_GEMINI_API_KEYS):
            api_key = REPAIR_GEMINI_API_KEYS[key_index]
            key_id = get_api_key_id(api_key)

            if key_id in api_key_cooldown:
                del api_key_cooldown[key_id]
                save_repair_config()
                refresh_keys_list()
                messagebox.showinfo("تم الإعادة", f"تم إعادة تعيين وقت التبريد للمفتاح {key_index + 1}", parent=gemini_window)
            else:
                messagebox.showinfo("معلومات", f"المفتاح {key_index + 1} ليس في وقت تبريد", parent=gemini_window)

    def reset_daily_usage():
        """إعادة تعيين عداد الاستخدام اليومي للمفتاح المحدد"""
        selection = keys_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "الرجاء اختيار مفتاح لإعادة تعيين عداد الاستخدام", parent=gemini_window)
            return

        key_index = selection[0]
        if key_index < len(REPAIR_GEMINI_API_KEYS):
            api_key = REPAIR_GEMINI_API_KEYS[key_index]
            key_id = get_api_key_id(api_key)
            current_date = time.strftime("%Y-%m-%d")

            api_key_usage_count[key_id] = {'date': current_date, 'count': 0}
            save_repair_config()
            refresh_keys_list()
            messagebox.showinfo("تم الإعادة", f"تم إعادة تعيين عداد الاستخدام للمفتاح {key_index + 1}", parent=gemini_window)

    def show_key_details():
        """عرض تفاصيل المفتاح المحدد"""
        selection = keys_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "الرجاء اختيار مفتاح لعرض تفاصيله", parent=gemini_window)
            return

        key_index = selection[0]
        if key_index < len(REPAIR_GEMINI_API_KEYS):
            api_key = REPAIR_GEMINI_API_KEYS[key_index]
            key_id = get_api_key_id(api_key)
            current_time = time.time()
            current_date = time.strftime("%Y-%m-%d")

            details = f"تفاصيل المفتاح {key_index + 1}:\n\n"
            details += f"المعرف: {key_id}\n"
            details += f"المفتاح: {api_key[:12]}...{api_key[-12:]}\n\n"

            # حالة التوفر
            if is_api_key_available(api_key):
                details += "الحالة: ✅ متاح\n"
            else:
                details += "الحالة: ❌ غير متاح\n"

            # وقت التبريد
            if key_id in api_key_cooldown:
                cooldown_time = api_key_cooldown[key_id]
                if current_time < cooldown_time:
                    remaining_minutes = int((cooldown_time - current_time) / 60)
                    details += f"وقت التبريد: {remaining_minutes} دقيقة متبقية\n"
                else:
                    details += "وقت التبريد: منتهي\n"
            else:
                details += "وقت التبريد: لا يوجد\n"

            # الاستخدام اليومي
            if key_id in api_key_usage_count:
                usage_data = api_key_usage_count[key_id]
                if isinstance(usage_data, dict):
                    usage_count = usage_data.get('count', 0)
                    usage_date = usage_data.get('date', 'غير محدد')
                    details += f"الاستخدام اليومي: {usage_count}/{MAX_DAILY_USAGE_PER_KEY}\n"
                    details += f"تاريخ الاستخدام: {usage_date}\n"
                else:
                    details += f"الاستخدام اليومي: {usage_data}/{MAX_DAILY_USAGE_PER_KEY}\n"
            else:
                details += "الاستخدام اليومي: 0/1000\n"

            # آخر خطأ
            if key_id in api_key_last_error:
                error_data = api_key_last_error[key_id]
                if isinstance(error_data, dict):
                    details += f"\nآخر خطأ: {error_data.get('error', 'غير محدد')}\n"
                    details += f"وقت الخطأ: {error_data.get('date', 'غير محدد')}\n"
                else:
                    details += f"\nآخر خطأ: {error_data}\n"
            else:
                details += "\nآخر خطأ: لا يوجد\n"

            messagebox.showinfo("تفاصيل المفتاح", details, parent=gemini_window)

    # إطار لإدخال المفتاح الجديد
    new_key_input_frame = ttk.Frame(buttons_frame)
    new_key_input_frame.pack(fill=tk.X, pady=(0, 5))

    new_key_entry = ttk.Entry(new_key_input_frame, width=40, font=("Consolas", 10))
    new_key_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

    ttk.Button(new_key_input_frame, text="لصق", command=paste_from_clipboard).pack(side=tk.LEFT, padx=(0, 5))
    ttk.Button(new_key_input_frame, text="إضافة", command=lambda: add_new_key(new_key_entry.get())).pack(side=tk.LEFT)

    # ترتيب الأزرار - الصف الأول
    buttons_row1 = ttk.Frame(buttons_frame)
    buttons_row1.pack(fill=tk.X, pady=(0, 5))

    ttk.Button(buttons_row1, text="اختبار المفتاح", command=test_selected_key).pack(side=tk.LEFT, padx=(0, 5))
    ttk.Button(buttons_row1, text="تفعيل المفتاح", command=set_as_active).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_row1, text="حذف المفتاح", command=delete_selected_key).pack(side=tk.LEFT, padx=5)

    # الصف الثاني - أزرار الإدارة المتقدمة
    buttons_row2 = ttk.Frame(buttons_frame)
    buttons_row2.pack(fill=tk.X)

    ttk.Button(buttons_row2, text="إعادة تعيين التبريد", command=reset_key_cooldown).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_row2, text="إعادة تعيين الاستخدام", command=reset_daily_usage).pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_row2, text="تفاصيل المفتاح", command=show_key_details).pack(side=tk.LEFT, padx=5)

    # معلومات إضافية
    info_frame = ttk.LabelFrame(main_frame, text="معلومات مفيدة", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 10))

    info_text = """💡 نصائح:
• يمكنك الحصول على مفتاح API مجاني من: https://makersuite.google.com/app/apikey
• الأداة تدعم عدة مفاتيح وتتنقل بينها تلقائياً عند الحاجة
• المفتاح النشط يظهر بالرمز 🟢
• يُنصح بإضافة عدة مفاتيح لتجنب حدود الاستخدام"""

    info_label = ttk.Label(info_frame, text=info_text, font=("Arial", 9), justify=tk.LEFT)
    info_label.pack(anchor=tk.W)

    # أزرار التحكم السفلية
    bottom_frame = ttk.Frame(main_frame)
    bottom_frame.pack(fill=tk.X)

    ttk.Button(bottom_frame, text="إغلاق", command=gemini_window.destroy).pack(side=tk.RIGHT)

# --- GUI Functions ---
def update_status(message):
    if 'status_text' in globals() and status_text.winfo_exists():
        try:
            status_text.config(state=tk.NORMAL)
            current_time = time.strftime('%H:%M:%S')
            status_text.insert(tk.END, f"{current_time} - {message}\n")
            status_text.see(tk.END)
            status_text.config(state=tk.DISABLED)
        except tk.TclError: print(f"Status Update (GUI Error): {message}")
    else: print(f"{time.strftime('%H:%M:%S')} - {message}")

# --- Image Management for Mods ---
def handle_view_mod_images():
    """Handle viewing images of a selected mod"""
    global selected_mods_for_repair

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لعرض صوره.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']

    # Create a dialog for viewing images
    image_dialog = tk.Toplevel()
    image_dialog.title(f"عرض صور المود: {mod_name}")
    image_dialog.geometry("800x600")
    image_dialog.minsize(800, 600)

    # Create frames for the dialog
    top_frame = ttk.Frame(image_dialog, padding="10")
    top_frame.pack(fill=tk.X)

    ttk.Label(top_frame, text=f"صور المود: {mod_name}", font=("Segoe UI", 12, "bold")).pack(pady=5)

    # Create a scrollable frame for images
    main_frame = ttk.Frame(image_dialog)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    # Add a canvas with scrollbar
    canvas = tk.Canvas(main_frame)
    scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Status label
    status_label = ttk.Label(image_dialog, text="جاري تحميل الصور...")
    status_label.pack(pady=5)

    # Bottom frame for action buttons
    bottom_frame = ttk.Frame(image_dialog, padding="10")
    bottom_frame.pack(fill=tk.X)

    # Add close button
    close_button = ttk.Button(bottom_frame, text="إغلاق", command=image_dialog.destroy)
    close_button.pack(side=tk.RIGHT, padx=5)

    # Function to extract and display images from the mod
    def extract_and_display_images():
        nonlocal status_label

        temp_extract_dir = f"temp_view_images_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"

        try:
            # Update status
            status_label.config(text="جاري تحميل ملف المود...")
            image_dialog.update_idletasks()

            # Download the mod file
            try:
                response = requests.get(original_mod_url, stream=True, timeout=600)
                response.raise_for_status()
            except requests.exceptions.RequestException as e:
                status_label.config(text=f"خطأ في تحميل المود: {e}")
                return

            # Save the mod file temporarily
            mod_bytes = response.content
            temp_mod_path = os.path.join(temp_extract_dir, os.path.basename(urlparse(original_mod_url).path))

            # Create temp directory
            os.makedirs(temp_extract_dir, exist_ok=True)

            with open(temp_mod_path, 'wb') as f:
                f.write(mod_bytes)

            # Extract the mod
            status_label.config(text="جاري استخراج محتويات المود...")
            image_dialog.update_idletasks()

            # Extract the mod based on its extension
            _, ext = os.path.splitext(temp_mod_path.lower())

            if ext in ['.zip', '.mcpack', '.mcaddon']:
                with zipfile.ZipFile(temp_mod_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_extract_dir)
            else:
                status_label.config(text=f"صيغة الملف غير مدعومة: {ext}")
                return

            # Find all image files in the extracted directory
            status_label.config(text="جاري البحث عن الصور...")
            image_dialog.update_idletasks()

            image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp']
            image_files = []

            for root, _, files in os.walk(temp_extract_dir):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        image_files.append(os.path.join(root, file))

            if not image_files:
                status_label.config(text="لم يتم العثور على صور في هذا المود.")
                return

            # Display the images
            status_label.config(text=f"تم العثور على {len(image_files)} صورة. جاري العرض...")
            image_dialog.update_idletasks()

            for img_path in image_files:
                try:
                    # Create a frame for this image
                    img_frame = ttk.Frame(scrollable_frame, borderwidth=1, relief="solid")
                    img_frame.pack(fill=tk.X, padx=5, pady=5)

                    # Load and display the image
                    img = Image.open(img_path)

                    # Check if it's an animated GIF
                    is_animated = False
                    if img.format == 'GIF' and getattr(img, 'is_animated', False):
                        is_animated = True

                    # Create a thumbnail for display
                    img.thumbnail((200, 200))
                    photo = ImageTk.PhotoImage(img)

                    # Display the image
                    img_label = ttk.Label(img_frame, image=photo)
                    img_label.image = photo  # Keep a reference
                    img_label.pack(side=tk.LEFT, padx=5, pady=5)

                    # Display image info
                    info_frame = ttk.Frame(img_frame)
                    info_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)

                    # Get relative path within the mod
                    rel_path = os.path.relpath(img_path, temp_extract_dir)
                    ttk.Label(info_frame, text=f"المسار: {rel_path}").pack(anchor="w")

                    # Get image dimensions and size
                    width, height = img.size
                    file_size = os.path.getsize(img_path)
                    ttk.Label(info_frame, text=f"الأبعاد: {width}x{height}").pack(anchor="w")
                    ttk.Label(info_frame, text=f"الحجم: {file_size/1024:.1f} KB").pack(anchor="w")

                    if is_animated:
                        ttk.Label(info_frame, text="نوع: GIF متحرك").pack(anchor="w")
                    else:
                        ttk.Label(info_frame, text=f"نوع: {img.format}").pack(anchor="w")

                except Exception as e:
                    ttk.Label(img_frame, text=f"خطأ في عرض الصورة: {os.path.basename(img_path)}").pack(side=tk.LEFT, padx=5)
                    ttk.Label(img_frame, text=f"الخطأ: {e}").pack(side=tk.LEFT, padx=5)

            status_label.config(text=f"تم عرض {len(image_files)} صورة من المود.")

        except Exception as e:
            status_label.config(text=f"حدث خطأ: {e}")
        finally:
            # Schedule cleanup of temp directory
            image_dialog.after(60000, lambda: shutil.rmtree(temp_extract_dir, ignore_errors=True))

    # Start extraction in a separate thread
    threading.Thread(target=extract_and_display_images, daemon=True).start()

def handle_add_images_to_mod():
    """Handle adding images to a selected mod"""
    global selected_mods_for_repair

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لإضافة الصور إليه.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']

    # Ask if user wants to add new images or replace existing ones
    action = messagebox.askquestion("اختيار العملية",
                                   "هل تريد استبدال الصور الموجودة في المود؟\n\n"
                                   "نعم: استبدال الصور الموجودة\n"
                                   "لا: إضافة صور جديدة فقط",
                                   icon='question')

    replace_mode = (action == 'yes')

    if replace_mode:
        # Redirect to view images function for now
        handle_view_mod_images()
        return

    # Create a dialog for image management
    image_dialog = tk.Toplevel()
    image_dialog.title(f"إضافة وتعديل الصور للمود: {mod_name}")
    image_dialog.geometry("800x600")
    image_dialog.minsize(800, 600)

    # Create frames for the dialog
    top_frame = ttk.Frame(image_dialog, padding="10")
    top_frame.pack(fill=tk.X)

    # URL input
    url_frame = ttk.Frame(top_frame)
    url_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

    ttk.Label(url_frame, text="رابط الصورة:").pack(side=tk.LEFT, padx=(0, 5))
    url_entry = ttk.Entry(url_frame, width=50)
    url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

    add_url_button = ttk.Button(url_frame, text="إضافة من الرابط",
                               command=lambda: add_image_from_url(url_entry.get(), mod_images_list))
    add_url_button.pack(side=tk.LEFT)

    # Local file selection
    file_frame = ttk.Frame(top_frame)
    file_frame.pack(side=tk.LEFT, padx=(10, 0))

    add_file_button = ttk.Button(file_frame, text="إضافة من القرص D",
                                command=lambda: add_image_from_disk('D:', mod_images_list))
    add_file_button.pack(side=tk.LEFT, padx=(0, 5))

    add_any_file_button = ttk.Button(file_frame, text="إضافة من أي مكان",
                                    command=lambda: add_image_from_disk(None, mod_images_list))
    add_any_file_button.pack(side=tk.LEFT)

    # Create a frame for the image list
    list_frame = ttk.LabelFrame(image_dialog, text="الصور المضافة", padding="10")
    list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    # Create a canvas and scrollbar for the image list
    canvas = tk.Canvas(list_frame)
    scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # List to store the images
    mod_images_list = []

    # Function to add image from URL
    def add_image_from_url(url, images_list):
        if not url:
            messagebox.showwarning("إدخال مطلوب", "الرجاء إدخال رابط الصورة.", parent=image_dialog)
            return

        try:
            # Download the image
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            # Check if it's an image
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                messagebox.showwarning("نوع ملف غير صالح",
                                      f"الملف ليس صورة. نوع المحتوى: {content_type}",
                                      parent=image_dialog)
                return

            # Get the image data
            image_data = response.content

            # Get the filename from the URL
            filename = os.path.basename(urlparse(url).path)
            if not filename:
                filename = f"image_{len(images_list) + 1}.jpg"

            # Add to the list
            add_image_to_list(filename, image_data, url, images_list, scrollable_frame)

            # Clear the URL entry
            url_entry.delete(0, tk.END)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل تحميل الصورة: {e}", parent=image_dialog)

    # Function to add image from disk
    def add_image_from_disk(initial_dir, images_list):
        filetypes = [("Image files", "*.png *.jpg *.jpeg *.webp *.gif"), ("All files", "*.*")]

        if initial_dir and os.path.exists(initial_dir):
            filepath = filedialog.askopenfilename(
                title="اختر صورة",
                initialdir=initial_dir,
                filetypes=filetypes,
                parent=image_dialog
            )
        else:
            filepath = filedialog.askopenfilename(
                title="اختر صورة",
                filetypes=filetypes,
                parent=image_dialog
            )

        if not filepath:
            return

        try:
            # Read the image file
            with open(filepath, 'rb') as f:
                image_data = f.read()

            # Get the filename
            filename = os.path.basename(filepath)

            # Add to the list
            add_image_to_list(filename, image_data, filepath, images_list, scrollable_frame)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل قراءة الصورة: {e}", parent=image_dialog)

    # Function to add image to the list and display it
    def add_image_to_list(filename, image_data, source, images_list, parent_frame):
        try:
            # Create a thumbnail
            img = Image.open(BytesIO(image_data))
            img.thumbnail((100, 100))
            photo = ImageTk.PhotoImage(img)

            # Create a frame for this image
            img_frame = ttk.Frame(parent_frame, borderwidth=1, relief="solid")
            img_frame.pack(fill=tk.X, padx=5, pady=5)

            # Display the image
            img_label = ttk.Label(img_frame, image=photo)
            img_label.image = photo  # Keep a reference
            img_label.pack(side=tk.LEFT, padx=5, pady=5)

            # Display the filename
            name_frame = ttk.Frame(img_frame)
            name_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)

            ttk.Label(name_frame, text=f"الاسم: {filename}").pack(anchor="w")

            # Show original size
            original_size = len(image_data)
            ttk.Label(name_frame, text=f"الحجم: {format_size(original_size)}").pack(anchor="w")

            # Check if it's a GIF and animated
            is_animated_gif = False
            try:
                if img.format == 'GIF' and getattr(img, 'is_animated', False):
                    is_animated_gif = True
                    ttk.Label(name_frame, text=f"GIF متحرك ({img.n_frames} إطار)").pack(anchor="w")
            except:
                pass

            # Add a text entry for the destination path in the mod
            path_frame = ttk.Frame(name_frame)
            path_frame.pack(fill=tk.X, pady=5)
            ttk.Label(path_frame, text="المسار في المود:").pack(side=tk.LEFT)
            path_entry = ttk.Entry(path_frame, width=30)
            path_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
            path_entry.insert(0, f"textures/")

            # Add compression options
            compression_frame = ttk.Frame(name_frame)
            compression_frame.pack(fill=tk.X, pady=5)
            ttk.Label(compression_frame, text="مستوى الضغط:").pack(side=tk.LEFT)

            compression_var = tk.StringVar(value="normal")
            compression_combo = ttk.Combobox(
                compression_frame,
                textvariable=compression_var,
                values=["none", "normal", "medium", "high"],
                width=8,
                state="readonly"
            )
            compression_combo.pack(side=tk.LEFT, padx=5)

            # Add compress button
            compress_button = ttk.Button(
                compression_frame,
                text="ضغط",
                command=lambda: compress_image_item(img_item, compression_var.get())
            )
            compress_button.pack(side=tk.LEFT, padx=5)

            # Add buttons
            button_frame = ttk.Frame(img_frame)
            button_frame.pack(side=tk.RIGHT, padx=5)

            remove_button = ttk.Button(button_frame, text="إزالة",
                                      command=lambda: remove_image_from_list(img_item, images_list, img_frame))
            remove_button.pack(pady=2)

            # Create the image item and add to the list
            img_item = {
                'filename': filename,
                'data': image_data,
                'source': source,
                'frame': img_frame,
                'photo': photo,
                'path_entry': path_entry,
                'compression_var': compression_var,
                'compress_button': compress_button,
                'original_size': original_size,
                'compressed': False,
                'is_animated_gif': is_animated_gif
            }

            images_list.append(img_item)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل إضافة الصورة: {e}", parent=image_dialog)

    # Function to compress an image item
    def compress_image_item(img_item, compression_level):
        if compression_level == "none":
            # Reset to original image if previously compressed
            if img_item.get('compressed', False):
                img_item['data'] = img_item.get('original_data', img_item['data'])
                img_item['compressed'] = False
                update_status(f"تم إلغاء ضغط الصورة: {img_item['filename']}")

                # Update size label
                for widget in img_item['frame'].winfo_children():
                    if isinstance(widget, ttk.Frame):
                        for child in widget.winfo_children():
                            if isinstance(child, ttk.Label) and child.cget('text').startswith("الحجم:"):
                                child.config(text=f"الحجم: {format_size(len(img_item['data']))}")
                            elif isinstance(child, ttk.Label) and child.cget('text').startswith("بعد الضغط:"):
                                child.destroy()
            return

        try:
            # Store original data if not already stored
            if 'original_data' not in img_item:
                img_item['original_data'] = img_item['data']

            # Compress the image
            update_status(f"جاري ضغط الصورة: {img_item['filename']} بمستوى {compression_level}...")

            compressed_bytes, format_str, content_type, original_size, compressed_size = compress_image(
                img_item['data'] if not img_item.get('compressed', False) else img_item['original_data'],
                compression_level
            )

            # Update the image item with compressed data
            img_item['data'] = compressed_bytes
            img_item['compressed'] = True

            # Calculate compression ratio
            compression_ratio = (1 - (compressed_size / original_size)) * 100 if original_size > 0 else 0

            # Update size labels
            size_label_updated = False
            compression_label_exists = False

            for widget in img_item['frame'].winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Label) and child.cget('text').startswith("الحجم:"):
                            child.config(text=f"الحجم: {format_size(original_size)}")
                            size_label_updated = True
                        elif isinstance(child, ttk.Label) and child.cget('text').startswith("بعد الضغط:"):
                            child.config(text=f"بعد الضغط: {format_size(compressed_size)} (توفير: {compression_ratio:.1f}%)")
                            compression_label_exists = True

            # If compression label doesn't exist, create it
            if not compression_label_exists:
                for widget in img_item['frame'].winfo_children():
                    if isinstance(widget, ttk.Frame) and widget.winfo_children() and isinstance(widget.winfo_children()[0], ttk.Label) and widget.winfo_children()[0].cget('text').startswith("الاسم:"):
                        ttk.Label(widget, text=f"بعد الضغط: {format_size(compressed_size)} (توفير: {compression_ratio:.1f}%)").pack(anchor="w")
                        break

            update_status(f"تم ضغط الصورة: {img_item['filename']}. الحجم الأصلي: {format_size(original_size)}, بعد الضغط: {format_size(compressed_size)}, توفير: {compression_ratio:.1f}%")

            # If format changed, update filename
            if format_str and not img_item['filename'].lower().endswith(f".{format_str}"):
                base_name, _ = os.path.splitext(img_item['filename'])
                new_filename = f"{base_name}.{format_str}"
                img_item['filename'] = new_filename

                # Update filename label
                for widget in img_item['frame'].winfo_children():
                    if isinstance(widget, ttk.Frame):
                        for child in widget.winfo_children():
                            if isinstance(child, ttk.Label) and child.cget('text').startswith("الاسم:"):
                                child.config(text=f"الاسم: {new_filename}")
                                break

        except Exception as e:
            update_status(f"خطأ في ضغط الصورة: {e}")
            messagebox.showerror("خطأ", f"فشل ضغط الصورة: {e}", parent=image_dialog)

    # Function to remove image from the list
    def remove_image_from_list(img_item, images_list, img_frame):
        images_list.remove(img_item)
        img_frame.destroy()

    # Function to format file size
    def format_size(size_bytes):
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.2f} MB"

    # Bottom frame for action buttons
    bottom_frame = ttk.Frame(image_dialog, padding="10")
    bottom_frame.pack(fill=tk.X)

    # Add a checkbox for uploading to Supabase
    upload_var = tk.BooleanVar(value=True)
    upload_checkbox = ttk.Checkbutton(bottom_frame, text="رفع إلى Supabase بعد التعديل", variable=upload_var)
    upload_checkbox.pack(side=tk.LEFT, padx=5)

    # Add buttons for actions
    cancel_button = ttk.Button(bottom_frame, text="إلغاء", command=image_dialog.destroy)
    cancel_button.pack(side=tk.RIGHT, padx=5)

    apply_button = ttk.Button(bottom_frame, text="تطبيق التغييرات",
                             command=lambda: apply_images_to_mod(mod_data, mod_images_list, upload_var.get(), image_dialog))
    apply_button.pack(side=tk.RIGHT, padx=5)

    # Function to apply images to the mod
    def apply_images_to_mod(mod_data, images_list, upload_to_supabase, dialog):
        if not images_list:
            messagebox.showwarning("لا توجد صور", "لم تتم إضافة أي صور للمود.", parent=dialog)
            return

        # Confirm the action
        confirm_msg = f"سيتم إضافة {len(images_list)} صورة إلى المود '{mod_data['name']}'."
        if upload_to_supabase:
            confirm_msg += "\nسيتم رفع المود المعدل إلى Supabase."

        if not messagebox.askyesno("تأكيد", confirm_msg, parent=dialog):
            return

        # Disable the apply button
        apply_button.config(state=tk.DISABLED)

        # Start the process in a separate thread
        threading.Thread(
            target=process_add_images_to_mod,
            args=(mod_data, images_list, upload_to_supabase, dialog),
            daemon=True
        ).start()

    # Center the dialog on the screen
    image_dialog.update_idletasks()
    width = image_dialog.winfo_width()
    height = image_dialog.winfo_height()
    x = (image_dialog.winfo_screenwidth() // 2) - (width // 2)
    y = (image_dialog.winfo_screenheight() // 2) - (height // 2)
    image_dialog.geometry(f"{width}x{height}+{x}+{y}")

    # Make the dialog modal
    image_dialog.transient(window)
    image_dialog.grab_set()
    image_dialog.focus_set()

def process_image_replacements(mod_data, new_images_list, existing_images_list, upload_to_supabase, dialog):
    """معالجة استبدال الصور في المود في خيط منفصل"""
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']
    original_file_path_in_storage = mod_data.get('file_path_in_storage')

    temp_extract_dir = f"temp_extract_img_replace_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"
    local_modified_filepath = None

    try:
        # الخطوة 1: تحميل المود
        update_status(f"الخطوة 1: تحميل المود '{mod_name}' من الرابط '{original_mod_url}'...")

        mod_bytes = None
        mod_content_stream = BytesIO()

        try:
            response = requests.get(original_mod_url, stream=True, timeout=600)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            update_status(f"حجم الملف المتوقع: {format_size_simple(total_size)}" if total_size else "جاري حساب حجم الملف...")

            for chunk in response.iter_content(chunk_size=8192 * 4):
                if chunk:
                    mod_content_stream.write(chunk)

            mod_bytes = mod_content_stream.getvalue()
            update_status(f"اكتمل تحميل الملف. الحجم الكلي: {format_size_simple(len(mod_bytes))}.")

        except requests.exceptions.RequestException as e:
            update_status(f"!!! خطأ فادح أثناء تحميل المود: {e}")
            messagebox.showerror("خطأ", f"فشل تحميل المود: {e}", parent=dialog)
            return

        # الخطوة 2: استخراج المود
        update_status(f"الخطوة 2: استخراج المود إلى المجلد المؤقت: {temp_extract_dir}")

        if not os.path.exists(temp_extract_dir):
            os.makedirs(temp_extract_dir)

        try:
            with zipfile.ZipFile(BytesIO(mod_bytes), 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            update_status("تم استخراج المود بنجاح.")
        except zipfile.BadZipFile:
            update_status("!!! خطأ: الملف الذي تم تحميله ليس ملف ZIP صالحًا.")
            messagebox.showerror("خطأ", "الملف الذي تم تحميله ليس ملف ZIP صالحًا.", parent=dialog)
            return

        # الخطوة 3: استبدال الصور الموجودة
        update_status(f"الخطوة 3: استبدال الصور في المود...")

        # تتبع الصور التي تم استبدالها
        replaced_images = []

        for i, existing_img in enumerate(existing_images_list):
            if existing_img['replace_var'].get() and existing_img['replacement_var'].get():
                # العثور على الصورة الجديدة المحددة للاستبدال
                replacement_filename = existing_img['replacement_var'].get()
                replacement_img = None

                for new_img in new_images_list:
                    if new_img['filename'] == replacement_filename:
                        replacement_img = new_img
                        break

                if not replacement_img:
                    update_status(f"!!! خطأ: لم يتم العثور على الصورة البديلة '{replacement_filename}'")
                    continue

                # المسار الكامل للصورة الموجودة في المود
                existing_path = os.path.join(temp_extract_dir, existing_img['rel_path'])

                # حذف الصورة القديمة
                try:
                    if os.path.exists(existing_path):
                        os.remove(existing_path)
                        update_status(f"تم حذف الصورة القديمة: {existing_img['rel_path']}")
                except Exception as e:
                    update_status(f"!!! خطأ أثناء حذف الصورة القديمة {existing_img['rel_path']}: {e}")

                # كتابة الصورة الجديدة
                try:
                    # التأكد من وجود المجلد
                    os.makedirs(os.path.dirname(existing_path), exist_ok=True)

                    # استخدام الصورة المضغوطة إذا كانت متاحة
                    image_data = replacement_img['data']

                    # كتابة الصورة الجديدة
                    with open(existing_path, 'wb') as f:
                        f.write(image_data)

                    update_status(f"تم استبدال الصورة {i+1}: {existing_img['rel_path']} بـ {replacement_filename}")
                    replaced_images.append(existing_img['rel_path'])

                except Exception as e:
                    update_status(f"!!! خطأ أثناء كتابة الصورة الجديدة {replacement_filename}: {e}")

        # الخطوة 4: إضافة الصور الجديدة التي لم تستخدم في الاستبدال
        update_status(f"الخطوة 4: إضافة الصور الجديدة إلى المود...")

        # تحديد الصور الجديدة التي لم تستخدم في الاستبدال
        unused_new_images = []
        for new_img in new_images_list:
            used_in_replacement = False
            for existing_img in existing_images_list:
                if existing_img['replace_var'].get() and existing_img['replacement_var'].get() == new_img['filename']:
                    used_in_replacement = True
                    break

            if not used_in_replacement:
                unused_new_images.append(new_img)

        # إضافة الصور الجديدة غير المستخدمة
        for i, img_item in enumerate(unused_new_images):
            filename = img_item['filename']
            image_data = img_item['data']
            dest_path = img_item.get('path_entry', '').get().strip() if hasattr(img_item.get('path_entry', ''), 'get') else 'textures/'

            # التأكد من وجود المجلد
            if dest_path:
                full_dest_path = os.path.join(temp_extract_dir, dest_path)
                if not os.path.exists(full_dest_path):
                    os.makedirs(full_dest_path, exist_ok=True)

                # كتابة ملف الصورة
                image_filepath = os.path.join(full_dest_path, filename)
            else:
                # إذا لم يتم تحديد مسار، ضع في المجلد الجذر
                image_filepath = os.path.join(temp_extract_dir, filename)

            with open(image_filepath, 'wb') as f:
                f.write(image_data)

            update_status(f"تمت إضافة الصورة الجديدة {i+1}/{len(unused_new_images)}: {os.path.relpath(image_filepath, temp_extract_dir)}")

        # الخطوة 5: إعادة ضغط المود
        update_status("الخطوة 5: إعادة ضغط المود...")

        if not os.path.exists(LOCAL_REPAIRED_MODS_DIR):
            os.makedirs(LOCAL_REPAIRED_MODS_DIR)

        # الحصول على الامتداد الأصلي
        _, original_ext = os.path.splitext(original_mod_url.split('?')[0])
        if original_ext.lower() not in [".mcaddon", ".mcpack"]:
            original_ext = ".mcaddon"

        # إنشاء اسم الملف المعدل
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        local_modified_filename = f"{sanitize_filename(mod_name)}_replaced_images_{timestamp}{original_ext}"
        local_modified_filepath = os.path.join(LOCAL_REPAIRED_MODS_DIR, local_modified_filename)

        # ضغط المجلد إلى ملف ZIP
        with zipfile.ZipFile(local_modified_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(temp_extract_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, temp_extract_dir)
                    zipf.write(file_path, arcname)

        update_status(f"تم إعادة ضغط المود بنجاح. المسار: {local_modified_filepath}")

        # الخطوة 6: رفع المود المعدل إلى Supabase إذا تم تحديد ذلك
        if upload_to_supabase:
            update_status("الخطوة 6: رفع المود المعدل إلى Supabase...")

            try:
                # قراءة الملف المعدل
                with open(local_modified_filepath, 'rb') as f:
                    modified_mod_bytes = f.read()

                # إنشاء اسم ملف فريد للتخزين
                storage_filename = f"{sanitize_filename(mod_name)}_{mod_id}_{timestamp}{original_ext}"

                # رفع الملف إلى Supabase
                upload_response = app_storage_client.from_(MODS_BUCKET_NAME).upload(
                    storage_filename,
                    modified_mod_bytes,
                    {"content-type": "application/octet-stream"}
                )

                if not upload_response.get('error'):
                    # الحصول على رابط عام للملف المرفوع
                    new_public_url = f"{STORAGE_URL}/storage/v1/object/public/{MODS_BUCKET_NAME}/{storage_filename}"
                    update_status(f"تم رفع المود المعدل بنجاح. الرابط الجديد: {new_public_url}")

                    # حذف الملف القديم من Supabase إذا كان موجودًا
                    if original_file_path_in_storage:
                        try:
                            delete_response = app_storage_client.from_(MODS_BUCKET_NAME).remove([original_file_path_in_storage])
                            if not delete_response.get('error'):
                                update_status(f"تم حذف الملف القديم من Supabase: {original_file_path_in_storage}")
                            else:
                                update_status(f"!!! خطأ أثناء حذف الملف القديم: {delete_response.get('error')}")
                        except Exception as del_err:
                            update_status(f"!!! خطأ أثناء حذف الملف القديم: {del_err}")

                    # تحديث سجل المود في قاعدة البيانات بالرابط الجديد
                    update_status(f"الخطوة 7: تحديث سجل المود (ID: {mod_id}) في قاعدة البيانات بالرابط الجديد...")
                    try:
                        update_data = {
                            "download_url": new_public_url,
                            "file_path_in_storage": storage_filename
                        }
                        db_update_response = app_db_client.table(MODS_TABLE_NAME).update(update_data).eq("id", mod_id).execute()
                        if db_update_response.data:
                            update_status("تم تحديث سجل المود في قاعدة البيانات بنجاح.")
                            # تحديث القائمة في الذاكرة لمنع مشاكل البيانات القديمة للعمليات اللاحقة
                            global loaded_mods_data_list
                            for i, mod_in_list in enumerate(loaded_mods_data_list):
                                if mod_in_list['id'] == mod_id:
                                    loaded_mods_data_list[i]['download_url'] = new_public_url
                                    loaded_mods_data_list[i]['file_path_in_storage'] = storage_filename
                                    break
                        else:
                            update_status(f"!!! خطأ أثناء تحديث سجل المود: {db_update_response.error}")
                    except Exception as db_err:
                        update_status(f"!!! خطأ أثناء تحديث سجل المود: {db_err}")

                    # عرض رسالة نجاح
                    messagebox.showinfo(
                        "تمت العملية بنجاح",
                        f"تم استبدال {len(replaced_images)} صورة وإضافة {len(unused_new_images)} صورة جديدة إلى المود '{mod_name}' ورفعه إلى Supabase بنجاح.",
                        parent=dialog
                    )
                else:
                    update_status(f"!!! خطأ أثناء رفع المود المعدل: {upload_response.get('error')}")
                    messagebox.showerror("خطأ", f"فشل رفع المود المعدل: {upload_response.get('error')}", parent=dialog)
            except Exception as e:
                update_status(f"!!! خطأ أثناء رفع المود المعدل إلى Supabase: {e}")
                messagebox.showerror("خطأ", f"فشل رفع المود المعدل: {e}", parent=dialog)
        else:
            # عرض رسالة نجاح للحفظ المحلي فقط
            messagebox.showinfo(
                "تمت العملية بنجاح",
                f"تم استبدال {len(replaced_images)} صورة وإضافة {len(unused_new_images)} صورة جديدة إلى المود '{mod_name}' وحفظه محليًا بنجاح.\n\nالمسار: {os.path.abspath(local_modified_filepath)}",
                parent=dialog
            )

        # إغلاق النافذة
        dialog.destroy()

    except Exception as e:
        update_status(f"!!! خطأ غير متوقع أثناء استبدال الصور في المود: {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}", parent=dialog)

    finally:
        # تنظيف المجلد المؤقت
        if os.path.exists(temp_extract_dir):
            try:
                shutil.rmtree(temp_extract_dir)
                update_status(f"تم تنظيف المجلد المؤقت: {temp_extract_dir}")
            except Exception as e_clean:
                update_status(f"!!! خطأ أثناء تنظيف المجلد المؤقت: {e_clean}")

def process_add_images_to_mod(mod_data, images_list, upload_to_supabase, dialog):
    """Process adding images to a mod in a separate thread"""
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']
    original_file_path_in_storage = mod_data.get('file_path_in_storage')

    temp_extract_dir = f"temp_extract_img_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"
    local_modified_filepath = None

    try:
        # Step 1: Download the mod
        update_status(f"تحميل المود '{mod_name}' من الرابط '{original_mod_url}'...")

        mod_bytes = None
        mod_content_stream = BytesIO()

        try:
            response = requests.get(original_mod_url, stream=True, timeout=600)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            update_status(f"حجم الملف المتوقع: {format_size_simple(total_size)}" if total_size else "جاري حساب حجم الملف...")

            for chunk in response.iter_content(chunk_size=8192 * 4):
                if chunk:
                    mod_content_stream.write(chunk)

            mod_bytes = mod_content_stream.getvalue()
            update_status(f"اكتمل تحميل الملف. الحجم الكلي: {format_size_simple(len(mod_bytes))}.")

        except requests.exceptions.RequestException as e:
            update_status(f"!!! خطأ فادح أثناء تحميل المود: {e}")
            messagebox.showerror("خطأ", f"فشل تحميل المود: {e}", parent=dialog)
            return

        # Step 2: Extract the mod
        update_status(f"استخراج المود إلى المجلد المؤقت: {temp_extract_dir}")

        if not os.path.exists(temp_extract_dir):
            os.makedirs(temp_extract_dir)

        try:
            with zipfile.ZipFile(BytesIO(mod_bytes), 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            update_status("تم استخراج المود بنجاح.")
        except zipfile.BadZipFile:
            update_status("!!! خطأ: الملف الذي تم تحميله ليس ملف ZIP صالحًا.")
            messagebox.showerror("خطأ", "الملف الذي تم تحميله ليس ملف ZIP صالحًا.", parent=dialog)
            return

        # Step 3: Add the images to the mod
        update_status(f"إضافة {len(images_list)} صورة إلى المود...")

        for i, img_item in enumerate(images_list):
            filename = img_item['filename']
            image_data = img_item['data']
            dest_path = img_item['path_entry'].get().strip()

            # Check if image was compressed
            if img_item.get('compressed', False):
                update_status(f"استخدام الصورة المضغوطة لـ {filename}")
                compression_info = f" (مضغوطة بمستوى {img_item['compression_var'].get()})"
            else:
                compression_info = ""

            # Ensure the destination path exists
            if dest_path:
                full_dest_path = os.path.join(temp_extract_dir, dest_path)
                if not os.path.exists(full_dest_path):
                    os.makedirs(full_dest_path, exist_ok=True)

                # Write the image file
                image_filepath = os.path.join(full_dest_path, filename)
            else:
                # If no path specified, put in the root
                image_filepath = os.path.join(temp_extract_dir, filename)

            with open(image_filepath, 'wb') as f:
                f.write(image_data)

            update_status(f"تمت إضافة الصورة {i+1}/{len(images_list)}: {os.path.relpath(image_filepath, temp_extract_dir)}{compression_info}")

        # Step 4: Repack the mod
        update_status("إعادة ضغط المود...")

        if not os.path.exists(LOCAL_REPAIRED_MODS_DIR):
            os.makedirs(LOCAL_REPAIRED_MODS_DIR)

        # Get the original extension
        _, original_ext = os.path.splitext(original_mod_url.split('?')[0])
        if original_ext.lower() not in [".mcaddon", ".mcpack"]:
            original_ext = ".mcaddon"

        # Create the new filename
        local_modified_filename = f"{sanitize_filename(mod_name)}_with_images_{int(time.time())}{original_ext}"
        local_modified_filepath = os.path.join(LOCAL_REPAIRED_MODS_DIR, local_modified_filename)

        # Create the new zip file
        with zipfile.ZipFile(local_modified_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(temp_extract_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    archive_name = os.path.relpath(file_path, temp_extract_dir)
                    zipf.write(file_path, archive_name)

        update_status(f"تم إعادة ضغط المود بنجاح. المسار: {local_modified_filepath}")

        # Step 5: Upload to Supabase if requested
        if upload_to_supabase:
            if not STORAGE_CLIENT_OK or not app_db_client:
                update_status("!!! خطأ: عملاء Supabase غير متاحين للرفع أو تحديث قاعدة البيانات.")
                messagebox.showerror("خطأ Supabase", "عملاء Supabase غير مهيئين بشكل صحيح.", parent=dialog)
                return

            update_status(f"رفع المود المعدل '{local_modified_filename}' إلى Supabase...")

            try:
                with open(local_modified_filepath, 'rb') as f:
                    upload_response = storage_client.storage.from_(MOD_BUCKET).upload(
                        path=local_modified_filename,
                        file=f,
                        file_options={"content-type": "application/octet-stream", "cache-control": "3600", "upsert": "false"}
                    )
                update_status(f"تم رفع المود المعدل بنجاح. استجابة: {upload_response}")

                # Get the public URL
                new_public_url = storage_client.storage.from_(MOD_BUCKET).get_public_url(local_modified_filename)
                update_status(f"الرابط العام الجديد: {new_public_url}")

                # Update the database record
                update_status(f"تحديث سجل المود (ID: {mod_id}) في قاعدة البيانات...")

                update_data = {"download_url": new_public_url}
                db_update_response = app_db_client.table(MODS_TABLE_NAME).update(update_data).eq("id", mod_id).execute()

                if db_update_response.data:
                    update_status("تم تحديث سجل المود في قاعدة البيانات بنجاح.")

                    # Update in-memory list
                    for i, mod_in_list in enumerate(loaded_mods_data_list):
                        if mod_in_list['id'] == mod_id:
                            loaded_mods_data_list[i]['download_url'] = new_public_url
                            update_status(f"تم تحديث بيانات المود '{mod_name}' في القائمة المحملة محليًا.")
                            break
                else:
                    error_msg = "فشل تحديث سجل المود في قاعدة البيانات."
                    if hasattr(db_update_response, 'error') and db_update_response.error:
                        error_msg += f" تفاصيل الخطأ: {db_update_response.error.message}"
                    update_status(f"!!! {error_msg}")
                    messagebox.showwarning("تحذير", error_msg, parent=dialog)

                # Delete the old file if needed
                if original_file_path_in_storage and original_file_path_in_storage != local_modified_filename:
                    update_status(f"حذف الملف القديم من Supabase (المسار: {original_file_path_in_storage})...")

                    try:
                        delete_response = storage_client.storage.from_(MOD_BUCKET).remove([original_file_path_in_storage])
                        update_status(f"استجابة حذف الملف القديم: {delete_response}")
                    except Exception as e:
                        update_status(f"!!! خطأ أثناء حذف الملف القديم: {e}")

                # Show success message
                messagebox.showinfo(
                    "تمت العملية بنجاح",
                    f"تم إضافة الصور إلى المود '{mod_name}' ورفعه إلى Supabase بنجاح.\n\nالرابط الجديد: {new_public_url}",
                    parent=dialog
                )

            except Exception as e:
                update_status(f"!!! خطأ أثناء رفع المود المعدل إلى Supabase: {e}")
                messagebox.showerror("خطأ", f"فشل رفع المود المعدل: {e}", parent=dialog)
        else:
            # Show success message for local only
            messagebox.showinfo(
                "تمت العملية بنجاح",
                f"تم إضافة الصور إلى المود '{mod_name}' وحفظه محليًا بنجاح.\n\nالمسار: {os.path.abspath(local_modified_filepath)}",
                parent=dialog
            )

        # Close the dialog
        dialog.destroy()

    except Exception as e:
        update_status(f"!!! خطأ غير متوقع أثناء إضافة الصور إلى المود: {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}", parent=dialog)

        # Re-enable the apply button
        if dialog.winfo_exists():
            for widget in dialog.winfo_children():
                if isinstance(widget, ttk.Frame) and widget.winfo_children():
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Button) and child.cget('text') == "تطبيق التغييرات":
                            child.config(state=tk.NORMAL)
    finally:
        # Clean up the temporary directory
        if os.path.exists(temp_extract_dir):
            try:
                shutil.rmtree(temp_extract_dir)
                update_status(f"تم تنظيف المجلد المؤقت: {temp_extract_dir}")
            except Exception as e:
                update_status(f"!!! خطأ أثناء تنظيف المجلد المؤقت: {e}")


def run_in_thread(target_func, *args, **kwargs):
    thread = threading.Thread(target=target_func, args=args, kwargs=kwargs, daemon=True)
    thread.start()

def handle_load_published_mods():
    if not APP_DB_CLIENT_OK:
        messagebox.showerror("خطأ في قاعدة البيانات", "فشل الاتصال بقاعدة بيانات التطبيق.")
        return
    update_status("جاري تحميل قائمة المودات المنشورة...")
    load_mods_button.config(state=tk.DISABLED)
    run_in_thread(load_published_mods_task)

def load_published_mods_task():
    global mods_listbox, loaded_mods_data_list, currently_visible_mod_data
    try:
        # Fetch id, name, download_url, category, downloads, descriptions and telegram descriptions
        response = app_db_client.table(MODS_TABLE_NAME).select("id, name, download_url, category, downloads, description, description_ar, telegram_description_en, telegram_description_ar").execute()

        if hasattr(response, 'data') and response.data:
            loaded_mods_data_list.clear()
            for mod_item in response.data:
                # Ensure category is present, default to "Unknown" or similar if None/missing
                mod_item['category'] = mod_item.get('category', 'غير مصنف')
                loaded_mods_data_list.append(mod_item)

            update_status(f"تم تحميل {len(loaded_mods_data_list)} مود بنجاح من قاعدة البيانات.")
            # Populate the listbox with all mods initially
            populate_mods_listbox() # Uses the default "All" filter

            if repair_mod_button and repair_mod_button.winfo_exists():
                 repair_mod_button.config(state=tk.NORMAL)
            if change_format_button and change_format_button.winfo_exists():
                 change_format_button.config(state=tk.NORMAL)
        else:
            update_status("لم يتم العثور على مودات أو حدث خطأ أثناء الجلب.")
            if hasattr(response, 'error') and response.error:
                update_status(f"Error details: {response.error.message}")
    except Exception as e:
        update_status(f"!!! خطأ فادح أثناء تحميل المودات: {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
    finally:
        if 'load_mods_button' in globals() and load_mods_button.winfo_exists():
            load_mods_button.config(state=tk.NORMAL)

def populate_mods_listbox(category_filter="All", missing_description_filter=False):
    global mods_listbox, loaded_mods_data_list, currently_visible_mod_data, current_category_filter, view_images_button, download_link_button, generate_description_button

    current_category_filter = category_filter # Update global filter state

    if not (mods_listbox and mods_listbox.winfo_exists()):
        update_status("خطأ: مربع قائمة المودات غير متاح.")
        return

    mods_listbox.config(state=tk.NORMAL)
    mods_listbox.delete(0, tk.END)
    currently_visible_mod_data.clear()

    count = 0
    for mod_item in loaded_mods_data_list:
        mod_category = mod_item.get('category', 'غير مصنف')
        mod_name = mod_item['name']
        mod_id = mod_item['id']
        download_url = mod_item.get('download_url', '')
        description_en = mod_item.get('description')
        description_ar = mod_item.get('description_ar')
        telegram_description_en = mod_item.get('telegram_description_en')
        telegram_description_ar = mod_item.get('telegram_description_ar')

        # Apply category filter
        category_match = (category_filter == "All" or (mod_category and category_filter.lower() == mod_category.lower()))

        # Apply missing description filter
        missing_desc_match = False
        if missing_description_filter:
            desc_en_empty = (description_en is None or (description_en.strip() == "" if description_en else True))
            desc_ar_empty = (description_ar is None or (description_ar.strip() == "" if description_ar else True))
            if desc_en_empty and desc_ar_empty:
                missing_desc_match = True
        else:
            missing_desc_match = True # Always show if not filtering for missing descriptions

        if category_match and missing_desc_match:

            file_extension = ""
            if download_url:
                try:
                    path_part = download_url.split('?')[0]
                    _, file_extension = os.path.splitext(path_part)
                except Exception:
                    file_extension = ""

            # Format the display text with extension and icon
            extension_lower = file_extension.lower()
            if extension_lower:
                if extension_lower == '.zip':
                    format_icon = "📦" # Box icon for ZIP
                elif extension_lower == '.mcaddon':
                    format_icon = "🧩" # Puzzle piece for MCADDON
                elif extension_lower == '.mcpack':
                    format_icon = "📚" # Books for MCPACK
                else:
                    format_icon = "📄" # Generic document for other formats

                # Check if Telegram descriptions exist
                has_telegram_desc = bool((telegram_description_en and telegram_description_en.strip()) or 
                                        (telegram_description_ar and telegram_description_ar.strip()))
                telegram_icon = "📱" if has_telegram_desc else "❌"
                
                display_text = f"ID: {mod_id} - {format_icon} {mod_name} ({extension_lower}) {telegram_icon}"
            else:
                # Check if Telegram descriptions exist
                has_telegram_desc = bool((telegram_description_en and telegram_description_en.strip()) or 
                                        (telegram_description_ar and telegram_description_ar.strip()))
                telegram_icon = "📱" if has_telegram_desc else "❌"
                
                display_text = f"ID: {mod_id} - {mod_name} {telegram_icon}"
            mods_listbox.insert(tk.END, display_text)
            currently_visible_mod_data.append(mod_item)
            count +=1

    mods_listbox.config(state=tk.NORMAL) # Keep it normal for selection
    if missing_description_filter:
        update_status(f"عرض المودات بدون وصف (إنجليزي أو عربي) ({count} مود).")
    elif category_filter == "All":
        update_status(f"عرض جميع المودات ({count} مود).")
    else:
        update_status(f"عرض المودات من فئة '{category_filter}' ({count} مود).")

    # Clear previous selections and disable action buttons as the list has changed
    handle_deselect_all_mods() # This will also disable buttons

def on_mod_select(event):
    global selected_mods_for_repair, currently_visible_mod_data, repair_mod_button, change_format_button, replace_mod_button, add_images_button, download_link_button, view_images_button, republish_mod_button, generate_description_button
    widget = event.widget
    selected_indices = widget.curselection()
    selected_mods_for_repair.clear()

    if selected_indices:
        for index in selected_indices:
            if 0 <= index < len(currently_visible_mod_data): # Use currently_visible_mod_data
                selected_mods_for_repair.append(currently_visible_mod_data[index])

        if selected_mods_for_repair:
            if len(selected_mods_for_repair) == 1:
                mod = selected_mods_for_repair[0]
                update_status(f"تم تحديد المود: {mod['name']} (ID: {mod['id']})")
                # Enable buttons that require a single mod selection
                if replace_mod_button and replace_mod_button.winfo_exists():
                    replace_mod_button.config(state=tk.NORMAL)
                if add_images_button and add_images_button.winfo_exists():
                    add_images_button.config(state=tk.NORMAL)
                if view_images_button and view_images_button.winfo_exists():
                    view_images_button.config(state=tk.NORMAL)
                if download_link_button and download_link_button.winfo_exists():
                    download_link_button.config(state=tk.NORMAL)
                if republish_mod_button and republish_mod_button.winfo_exists():
                    republish_mod_button.config(state=tk.NORMAL)
                if generate_description_button and generate_description_button.winfo_exists():
                    generate_description_button.config(state=tk.NORMAL)
            else:
                update_status(f"تم تحديد {len(selected_mods_for_repair)} مودات.")
                # Disable buttons that require a single mod selection when multiple mods are selected
                if replace_mod_button and replace_mod_button.winfo_exists():
                    replace_mod_button.config(state=tk.DISABLED)
                if add_images_button and add_images_button.winfo_exists():
                    add_images_button.config(state=tk.DISABLED)
                if view_images_button and view_images_button.winfo_exists():
                    view_images_button.config(state=tk.DISABLED)
                if download_link_button and download_link_button.winfo_exists():
                    download_link_button.config(state=tk.DISABLED)
                if republish_mod_button and republish_mod_button.winfo_exists():
                    republish_mod_button.config(state=tk.DISABLED)
                if generate_description_button and generate_description_button.winfo_exists():
                    generate_description_button.config(state=tk.DISABLED)

            # Enable buttons that work with multiple mod selection
            if repair_mod_button and repair_mod_button.winfo_exists():
                repair_mod_button.config(state=tk.NORMAL)
            if change_format_button and change_format_button.winfo_exists():
                change_format_button.config(state=tk.NORMAL)
            return

    # If no valid selection or list is empty after processing
    if repair_mod_button and repair_mod_button.winfo_exists():
        repair_mod_button.config(state=tk.DISABLED)
    if change_format_button and change_format_button.winfo_exists():
        change_format_button.config(state=tk.DISABLED)
    if replace_mod_button and replace_mod_button.winfo_exists():
        replace_mod_button.config(state=tk.DISABLED)
    if add_images_button and add_images_button.winfo_exists():
        add_images_button.config(state=tk.DISABLED)
    if view_images_button and view_images_button.winfo_exists():
        view_images_button.config(state=tk.DISABLED)
    if download_link_button and download_link_button.winfo_exists():
        download_link_button.config(state=tk.DISABLED)
    if republish_mod_button and republish_mod_button.winfo_exists():
        republish_mod_button.config(state=tk.DISABLED)
    if generate_description_button and generate_description_button.winfo_exists():
        generate_description_button.config(state=tk.DISABLED)
    update_status("لم يتم تحديد أي مود أو أن التحديدات غير صالحة.")


def handle_select_all_mods():
    global mods_listbox, selected_mods_for_repair, loaded_mods_data_list, repair_mod_button, change_format_button, replace_mod_button, add_images_button, view_images_button, download_link_button, republish_mod_button, generate_description_button
    if mods_listbox and mods_listbox.winfo_exists():
        mods_listbox.select_set(0, tk.END)
        # Manually update selection based on currently_visible_mod_data
        selected_mods_for_repair.clear()
        for item in currently_visible_mod_data: # Select all *visible* items
            selected_mods_for_repair.append(item)

        if selected_mods_for_repair:
            update_status(f"تم تحديد جميع المودات المعروضة حاليًا ({len(selected_mods_for_repair)} مود).")
            if repair_mod_button: repair_mod_button.config(state=tk.NORMAL)
            if change_format_button: change_format_button.config(state=tk.NORMAL)
            # Disable buttons that require a single mod selection when multiple mods are selected
            if len(selected_mods_for_repair) == 1:
                if replace_mod_button: replace_mod_button.config(state=tk.NORMAL)
                if add_images_button: add_images_button.config(state=tk.NORMAL)
                if view_images_button: view_images_button.config(state=tk.NORMAL)
                if download_link_button: download_link_button.config(state=tk.NORMAL)
                if republish_mod_button: republish_mod_button.config(state=tk.NORMAL)
            else:
                if replace_mod_button: replace_mod_button.config(state=tk.DISABLED)
                if add_images_button: add_images_button.config(state=tk.DISABLED)
                if view_images_button: view_images_button.config(state=tk.DISABLED)
                if download_link_button: download_link_button.config(state=tk.DISABLED)
                if republish_mod_button: republish_mod_button.config(state=tk.DISABLED)
        else:
            update_status("لا توجد مودات لتحديدها.")
            if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
            if change_format_button: change_format_button.config(state=tk.DISABLED)
            if replace_mod_button: replace_mod_button.config(state=tk.DISABLED)
            if add_images_button: add_images_button.config(state=tk.DISABLED)
            if view_images_button: view_images_button.config(state=tk.DISABLED)
            if download_link_button: download_link_button.config(state=tk.DISABLED)

def handle_deselect_all_mods():
    global mods_listbox, selected_mods_for_repair, repair_mod_button, change_format_button, replace_mod_button, add_images_button, view_images_button, download_link_button, republish_mod_button
    if mods_listbox and mods_listbox.winfo_exists():
        mods_listbox.selection_clear(0, tk.END)
        selected_mods_for_repair.clear()
        update_status("تم إلغاء تحديد جميع المودات.")
        if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
        if change_format_button: change_format_button.config(state=tk.DISABLED)
        if replace_mod_button: replace_mod_button.config(state=tk.DISABLED)
        if add_images_button: add_images_button.config(state=tk.DISABLED)
        if view_images_button: view_images_button.config(state=tk.DISABLED)
        if download_link_button: download_link_button.config(state=tk.DISABLED)
        if republish_mod_button: republish_mod_button.config(state=tk.DISABLED)


def handle_repair_mod_action():
    global selected_mods_for_repair, upload_after_repair_var, repair_mod_button, change_format_button
    if not selected_mods_for_repair:
        messagebox.showwarning("لم يتم التحديد", "الرجاء تحديد مود واحد أو أكثر من القائمة أولاً.")
        return

    perform_upload = upload_after_repair_var.get() if upload_after_repair_var else False

    num_selected = len(selected_mods_for_repair)
    mod_names_preview = ", ".join([mod['name'] for mod in selected_mods_for_repair[:3]])
    if num_selected > 3:
        mod_names_preview += f", ... و {num_selected - 3} مودات أخرى"

    confirm_action_message = f"سيتم محاولة إصلاح {num_selected} مودات محددة: ({mod_names_preview})."
    if perform_upload:
        confirm_action_message += "\nسيتم رفع كل مود ناجح إلى Supabase بامتداده الأصلي (أو .mcaddon إذا تم إصلاحه)"
        confirm_action_message += "، مما سيؤدي إلى حذف النسخة القديمة وتحديث الرابط في قاعدة البيانات لكل مود."
    else:
        confirm_action_message += "\nسيتم حفظ المودات المصلحة محليًا فقط."
    confirm_action_message += "\n\nهل أنت متأكد أنك تريد المتابعة؟"

    confirmation = messagebox.askyesno(f"تأكيد إصلاح {num_selected} مودات", confirm_action_message)
    if not confirmation:
        update_status("تم إلغاء عملية الإصلاح.")
        return

    if REPAIR_GEMINI_CLIENT_OK is False and not configure_repair_gemini_client():
        update_status("!!! تحذير: لم يتم تكوين Gemini بشكل صحيح. قد تكون جودة الإصلاح محدودة.")

    if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
    if change_format_button: change_format_button.config(state=tk.DISABLED)

    update_status(f"\n--- بدء عملية إصلاح لـ {num_selected} مودات ---")

    for mod_data in selected_mods_for_repair:
        mod_id = mod_data['id']
        mod_name = mod_data['name']
        original_mod_url = mod_data['download_url']
        original_file_path_in_storage = mod_data.get('file_path_in_storage')

        action_description = "والرفع" if perform_upload else "(محليًا فقط)"
        update_status(f"\n--- بدء إصلاح المود: {mod_name} (ID: {mod_id}) {action_description} ---")
        update_status(f"رابط/مسار التحميل الأصلي: {original_mod_url}")
        if original_file_path_in_storage:
            update_status(f"المسار المخزن الأصلي للملف (سيتم حذفه إذا نجح الرفع): {original_file_path_in_storage}")

        # Each mod repair runs in its own thread
        run_in_thread(repair_mod_and_optionally_upload_task,
                      mod_id,
                      mod_name,
                      original_mod_url,
                      original_file_path_in_storage,
                      perform_upload,
                      new_extension_for_repack=None, # Explicitly None for repair only
                      skip_gemini_processing=False) # Gemini processing enabled for repair
        time.sleep(0.1) # Small delay to allow thread to start and potentially avoid overwhelming system/API if many selected

    # Re-enable buttons after launching all threads. Individual threads will manage their own completion status.
    # A more sophisticated approach might track thread completion.
    # For now, we assume the user understands operations are backgrounded.
    # if repair_mod_button: repair_mod_button.config(state=tk.NORMAL) # This might be too soon
    # if change_format_button: change_format_button.config(state=tk.NORMAL)
    update_status(f"--- تم إطلاق مهام الإصلاح لـ {num_selected} مودات في الخلفية ---")


def handle_change_format_action():
    global selected_mods_for_repair, upload_after_repair_var, repair_mod_button, change_format_button
    if not selected_mods_for_repair:
        messagebox.showwarning("لم يتم التحديد", "الرجاء تحديد مود واحد أو أكثر من القائمة أولاً.")
        return

    perform_upload = upload_after_repair_var.get() if upload_after_repair_var else False

    # --- Extension Selection (once for all selected mods) ---
    extensions = [".mcaddon", ".mcpack", ".zip"]
    extension_prompt = "اختر الامتداد الجديد للملفات المحددة:\n"
    for i, ext in enumerate(extensions):
        extension_prompt += f"{i+1}. {ext}\n"
    extension_prompt += "\nأدخل رقم الاختيار (مثلاً 1 لـ .mcaddon):"

    choice_input = simpledialog.askstring("اختيار صيغة الملف (لجميع المودات المحددة)", extension_prompt, parent=window)

    if choice_input is None:
        update_status("تم إلغاء تغيير صيغة الملف.")
        return

    try:
        choice_input_clean = choice_input.strip() if choice_input else ""
        choice_index = int(choice_input_clean) - 1
        if 0 <= choice_index < len(extensions):
            new_target_extension = extensions[choice_index]
        else:
            messagebox.showerror("اختيار غير صالح", "الرجاء إدخال رقم صالح من القائمة.")
            return
    except ValueError:
        messagebox.showerror("إدخال غير صالح", "الرجاء إدخال رقم.")
        return

    update_status(f"سيتم تغيير امتداد الملفات المحددة إلى: {new_target_extension}")

    # Ask if user wants to force format change even if it's the same format
    force_format_change = messagebox.askyesno(
        "تأكيد إجبار التغيير",
        "هل ترغب في إجبار تغيير الصيغة حتى لو كانت نفس الصيغة الحالية؟\n\n"
        "نعم: سيتم تحميل الملف وتحويله وتحديثه في قاعدة البيانات حتى لو كان بنفس الصيغة.\n"
        "لا: سيتم تخطي الملفات التي هي بالفعل بالصيغة المطلوبة."
    )

    num_selected = len(selected_mods_for_repair)
    mod_names_preview = ", ".join([mod['name'] for mod in selected_mods_for_repair[:3]])
    if num_selected > 3:
        mod_names_preview += f", ... و {num_selected - 3} مودات أخرى"

    confirm_action_message = f"سيتم محاولة تغيير صيغة {num_selected} مودات محددة ({mod_names_preview}) إلى '{new_target_extension}'."
    if perform_upload:
        confirm_action_message += f"\nسيتم رفع كل مود ناجح إلى Supabase بالامتداد الجديد '{new_target_extension}'"
        confirm_action_message += "، مما سيؤدي إلى حذف النسخة القديمة وتحديث الرابط في قاعدة البيانات لكل مود."
    else:
        confirm_action_message += f"\nسيتم حفظ المودات محليًا فقط بالامتداد الجديد '{new_target_extension}'."
    confirm_action_message += "\n\nهل أنت متأكد أنك تريد المتابعة؟"

    confirmation = messagebox.askyesno(f"تأكيد تغيير صيغة {num_selected} مودات", confirm_action_message)
    if not confirmation:
        update_status("تم إلغاء عملية تغيير الصيغة.")
        return

    if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
    if change_format_button: change_format_button.config(state=tk.DISABLED)

    update_status(f"\n--- بدء عملية تغيير الصيغة لـ {num_selected} مودات إلى {new_target_extension} (بشكل تسلسلي) ---")

    # Worker function to process mods sequentially
    def sequential_format_change_worker_task(mods_to_process, target_ext, upload_flag, force_format_change_flag=False):
        skipped_count_worker = 0
        processed_count_worker = 0
        force_format_change = force_format_change_flag  # استخدام القيمة المرسلة من المستخدم

        for mod_data_item in mods_to_process:
            mod_id = mod_data_item['id']
            mod_name = mod_data_item['name']
            original_mod_url = mod_data_item['download_url']
            original_file_path_in_storage = mod_data_item.get('file_path_in_storage')

            current_ext_worker = ""
            if original_mod_url:
                path_part_worker = original_mod_url.split('?')[0]
                _, current_ext_worker = os.path.splitext(path_part_worker)

            normalized_current_ext_worker = current_ext_worker.lower()
            normalized_target_ext_worker = target_ext.lower() # target_ext is already normalized

            # تحقق مما إذا كانت الصيغة الحالية هي نفس الصيغة المطلوبة
            same_extension = normalized_current_ext_worker == normalized_target_ext_worker

            if same_extension and not force_format_change:
                # تخطي المود إذا كانت الصيغة الحالية هي نفس الصيغة المطلوبة وميزة الإجبار غير مفعلة
                update_status(f"--- (تسلسلي) تخطي المود: {mod_name} (ID: {mod_id}) لأنه بالفعل بالصيغة المطلوبة ({target_ext}). ---")
                skipped_count_worker += 1
                continue

            # إذا كانت الصيغة الحالية هي نفس الصيغة المطلوبة ولكن ميزة الإجبار مفعلة
            if same_extension and force_format_change:
                update_status(f"--- (تسلسلي) المود: {mod_name} (ID: {mod_id}) بالفعل بالصيغة المطلوبة ({target_ext})، ولكن سيتم إعادة تحميله وتحديثه. ---")

            action_description_worker = "والرفع" if upload_flag else "(محليًا فقط)"
            update_status(f"\n--- (تسلسلي) بدء تغيير صيغة المود: {mod_name} (ID: {mod_id}) إلى {target_ext} {action_description_worker} ---")
            update_status(f"رابط/مسار التحميل الأصلي: {original_mod_url} (الصيغة الحالية: {current_ext_worker})")
            if original_file_path_in_storage:
                update_status(f"المسار المخزن الأصلي للملف (سيتم حذفه إذا نجح الرفع): {original_file_path_in_storage}")

            # Call the task directly for sequential processing
            repair_mod_and_optionally_upload_task(
                mod_id,
                mod_name,
                original_mod_url,
                original_file_path_in_storage,
                upload_flag, # perform_upload
                new_extension_for_repack=target_ext, # new_target_extension
                skip_gemini_processing=True  # Gemini processing SKIPPED for format change
            )
            processed_count_worker += 1
            update_status(f"--- (تسلسلي) اكتمل تغيير صيغة المود: {mod_name} (ID: {mod_id}) ---")
            # No time.sleep needed here as it's sequential within this worker

        final_status_message_worker = f"--- اكتملت جميع مهام تغيير الصيغة التسلسلية. تم معالجة {processed_count_worker} مود."
        if skipped_count_worker > 0:
            final_status_message_worker += f" تم تخطي {skipped_count_worker} مودات لأنها بالفعل بالصيغة المطلوبة."
        update_status(final_status_message_worker)

        # Re-enable buttons on the main GUI thread
        def _re_enable_buttons_after_sequential_task():
            if 'repair_mod_button' in globals() and repair_mod_button and repair_mod_button.winfo_exists():
                repair_mod_button.config(state=tk.NORMAL if selected_mods_for_repair else tk.DISABLED)
            if 'change_format_button' in globals() and change_format_button and change_format_button.winfo_exists():
                change_format_button.config(state=tk.NORMAL if selected_mods_for_repair else tk.DISABLED)

        if 'window' in globals() and window and window.winfo_exists():
            window.after(0, _re_enable_buttons_after_sequential_task)

    # Prepare arguments for the worker
    # Ensure new_target_extension is normalized before passing
    normalized_new_target_extension = new_target_extension.lower()
    if not normalized_new_target_extension.startswith('.'):
        normalized_new_target_extension = '.' + normalized_new_target_extension

    # Run the sequential worker in a single thread
    run_in_thread(sequential_format_change_worker_task,
                  list(selected_mods_for_repair), # Pass a copy of the list
                  normalized_new_target_extension,
                  perform_upload,
                  force_format_change)

    update_status(f"--- تم إطلاق مهمة تغيير الصيغة التسلسلية في الخلفية لـ {num_selected} مودات ---")
    # The old logic for final_status_message and re-enabling buttons if processed_count == 0 is now handled by the worker.


def copy_log_from_status():
    if not PYPERCLIP_AVAILABLE:
        messagebox.showwarning("غير متوفر", "مكتبة pyperclip غير مثبتة.")
        return
    try:
        log_content = status_text.get("1.0", tk.END)
        log_content = log_content.strip() if log_content else ""
        if log_content:
            pyperclip.copy(log_content)
            update_status("تم نسخ السجل.")
        else:
            update_status("السجل فارغ.")
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل نسخ السجل: {e}")


def handle_replace_mod_file():
    global selected_mods_for_repair, upload_after_repair_var, repair_mod_button, change_format_button, replace_mod_button

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لاستبداله.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']
    original_file_path_in_storage = mod_data.get('file_path_in_storage')

    # Get current extension
    current_ext = ""
    if original_mod_url:
        try:
            path_part = original_mod_url.split('?')[0]
            _, current_ext = os.path.splitext(path_part)
        except Exception:
            current_ext = ""

    # Ask user to select a new file
    file_types = [
        ("جميع ملفات المودات", "*.mcaddon;*.mcpack;*.zip"),
        ("ملفات MCADDON", "*.mcaddon"),
        ("ملفات MCPACK", "*.mcpack"),
        ("ملفات ZIP", "*.zip"),
        ("جميع الملفات", "*.*")
    ]

    new_file_path = filedialog.askopenfilename(
        title=f"اختر ملف جديد لاستبدال المود: {mod_name}",
        filetypes=file_types
    )

    if not new_file_path:
        update_status("تم إلغاء عملية استبدال الملف.")
        return

    # Get new file extension
    _, new_ext = os.path.splitext(new_file_path)
    new_ext = new_ext.lower()

    # Ask if user wants to keep the original extension or use the new file's extension
    if current_ext.lower() != new_ext:
        extension_options = [current_ext, new_ext]
        if current_ext.lower() not in ['.mcaddon', '.mcpack', '.zip']:
            extension_options.append('.mcaddon')  # Add default option if current is unknown
        if new_ext.lower() not in ['.mcaddon', '.mcpack', '.zip']:
            extension_options.append('.mcaddon')  # Add default option if new is unknown

        # Remove duplicates and ensure all start with dot
        extension_options = list(set([ext.lower() if ext.startswith('.') else '.' + ext.lower() for ext in extension_options]))

        extension_prompt = "اختر الامتداد النهائي للملف بعد الاستبدال:\n"
        for i, ext in enumerate(extension_options):
            extension_prompt += f"{i+1}. {ext}\n"

        choice_input = simpledialog.askstring(
            "اختيار صيغة الملف النهائية",
            extension_prompt,
            parent=window
        )

        if choice_input is None:
            update_status("تم إلغاء عملية استبدال الملف.")
            return

        try:
            choice_input_clean = choice_input.strip() if choice_input else ""
            choice_index = int(choice_input_clean) - 1
            if 0 <= choice_index < len(extension_options):
                final_extension = extension_options[choice_index]
            else:
                messagebox.showerror("اختيار غير صالح", "الرجاء إدخال رقم صالح من القائمة.")
                return
        except ValueError:
            messagebox.showerror("إدخال غير صالح", "الرجاء إدخال رقم.")
            return
    else:
        final_extension = current_ext

    # Confirm upload
    perform_upload = upload_after_repair_var.get() if upload_after_repair_var else False

    confirm_message = f"سيتم استبدال ملف المود '{mod_name}' بالملف الجديد:\n{new_file_path}\n"
    confirm_message += f"وسيتم تغيير امتداده إلى: {final_extension}\n\n"

    if perform_upload:
        confirm_message += "سيتم رفع الملف الجديد إلى Supabase وتحديث رابط التحميل في قاعدة البيانات."
    else:
        confirm_message += "سيتم حفظ الملف الجديد محليًا فقط."

    confirmation = messagebox.askyesno("تأكيد استبدال الملف", confirm_message)
    if not confirmation:
        update_status("تم إلغاء عملية استبدال الملف.")
        return

    # Disable buttons during processing
    if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
    if change_format_button: change_format_button.config(state=tk.DISABLED)
    if replace_mod_button: replace_mod_button.config(state=tk.DISABLED)

    update_status(f"\n--- بدء عملية استبدال ملف المود: {mod_name} (ID: {mod_id}) ---")

    # Process the replacement in a separate thread
    run_in_thread(
        process_mod_replacement_task,
        mod_id,
        mod_name,
        original_mod_url,
        original_file_path_in_storage,
        new_file_path,
        final_extension,
        perform_upload
    )


def process_mod_replacement_task(mod_id, mod_name, original_mod_url, original_file_path_in_storage, new_file_path, target_extension, perform_upload):
    """Process the replacement of a mod file with a new one, optionally uploading to Supabase."""
    temp_extract_dir = f"temp_extract_replace_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"
    local_replaced_filepath = None

    try:
        # Step 1: Validate the new file is a valid ZIP
        update_status(f"الخطوة 1: التحقق من صحة الملف الجديد: {new_file_path}")

        try:
            with open(new_file_path, 'rb') as f:
                new_file_bytes = f.read()

            # Check if it's a valid ZIP file
            try:
                with zipfile.ZipFile(BytesIO(new_file_bytes), 'r') as zip_ref:
                    # Extract to temp directory to verify contents
                    if not os.path.exists(temp_extract_dir):
                        os.makedirs(temp_extract_dir)
                    zip_ref.extractall(temp_extract_dir)
                update_status("تم التحقق من الملف الجديد بنجاح. إنه ملف ZIP صالح.")
            except zipfile.BadZipFile:
                update_status("!!! خطأ: الملف الجديد ليس ملف ZIP صالح. لا يمكن المتابعة.")
                return
        except Exception as e:
            update_status(f"!!! خطأ أثناء قراءة الملف الجديد: {e}")
            return

        # Step 2: Prepare the new file with the target extension
        update_status("الخطوة 2: إعداد الملف الجديد بالامتداد المطلوب...")

        if not os.path.exists(LOCAL_REPAIRED_MODS_DIR):
            os.makedirs(LOCAL_REPAIRED_MODS_DIR)

        sanitized_mod_name = sanitize_filename(mod_name)
        new_filename_base = f"{sanitized_mod_name}_replaced_{int(time.time())}"

        if not target_extension.startswith('.'):
            target_extension = '.' + target_extension

        local_replaced_filename = f"{new_filename_base}{target_extension}"
        local_replaced_filepath = os.path.join(LOCAL_REPAIRED_MODS_DIR, local_replaced_filename)

        # Copy the file with the new extension
        shutil.copy2(new_file_path, local_replaced_filepath)
        update_status(f"تم إعداد الملف الجديد بنجاح: {local_replaced_filepath}")

        if not perform_upload:
            update_status(f"تم حفظ الملف المستبدل محليًا في: {os.path.abspath(local_replaced_filepath)}")
            messagebox.showinfo(
                "اكتمل الاستبدال المحلي",
                f"تم استبدال المود '{mod_name}' محليًا بنجاح.\n\nالملف المحفوظ: {os.path.abspath(local_replaced_filepath)}"
            )
            update_status(f"--- اكتملت عملية الاستبدال المحلي للمود: {mod_name} ---")
            return

        # Step 3: Upload to Supabase if requested
        if not STORAGE_CLIENT_OK or not app_db_client:
            update_status("!!! خطأ: عملاء Supabase غير متاحين للرفع أو تحديث قاعدة البيانات.")
            messagebox.showerror("خطأ Supabase", "عملاء Supabase غير مهيئين بشكل صحيح. لا يمكن المتابعة مع الرفع.")
            return

        update_status(f"الخطوة 3: رفع الملف الجديد '{local_replaced_filename}' إلى Supabase Storage...")
        new_file_path_on_supabase = local_replaced_filename

        try:
            with open(local_replaced_filepath, 'rb') as f:
                upload_response_dict = storage_client.storage.from_(MOD_BUCKET).upload(
                    path=new_file_path_on_supabase,
                    file=f,
                    file_options={"content-type": "application/octet-stream", "cache-control": "3600", "upsert": "false"}
                )
            update_status(f"تم إرسال طلب الرفع. استجابة أولية: {upload_response_dict}")
            update_status(f"تم رفع الملف '{new_file_path_on_supabase}' بنجاح إلى Supabase.")
        except Exception as e:
            update_status(f"!!! خطأ أثناء رفع الملف الجديد إلى Supabase Storage: {e}")
            error_message_detail = str(e)
            if hasattr(e, 'args') and e.args:
                error_message_detail = f"{e} - {e.args}"
            messagebox.showerror("فشل الرفع", f"فشل رفع الملف الجديد إلى Supabase Storage.\nالخطأ: {error_message_detail}")
            return

        # Step 4: Get public URL
        update_status("الخطوة 4: الحصول على الرابط العام للملف الجديد...")
        try:
            new_public_url = storage_client.storage.from_(MOD_BUCKET).get_public_url(new_file_path_on_supabase)
            update_status(f"الرابط العام الجديد: {new_public_url}")
        except Exception as e:
            update_status(f"!!! خطأ أثناء الحصول على الرابط العام للملف الجديد: {e}")
            messagebox.showerror("خطأ في الرابط", f"فشل الحصول على الرابط العام للملف الجديد.\nالخطأ: {e}")
            return

        # Step 5: Update database record
        update_status(f"الخطوة 5: تحديث سجل المود (ID: {mod_id}) في قاعدة البيانات بالرابط الجديد...")
        try:
            update_data = {"download_url": new_public_url}
            db_update_response = app_db_client.table(MODS_TABLE_NAME).update(update_data).eq("id", mod_id).execute()
            if db_update_response.data:
                update_status("تم تحديث سجل المود في قاعدة البيانات بنجاح.")
                # Update in-memory list
                global loaded_mods_data_list
                for i, mod_in_list in enumerate(loaded_mods_data_list):
                    if mod_in_list['id'] == mod_id:
                        loaded_mods_data_list[i]['download_url'] = new_public_url
                        update_status(f"تم تحديث بيانات المود '{mod_name}' (ID: {mod_id}) في القائمة المحملة محلياً.")
                        break
            else:
                error_msg = f"فشل تحديث سجل المود في قاعدة البيانات. لا توجد بيانات في الاستجابة."
                if hasattr(db_update_response, 'error') and db_update_response.error:
                    error_msg += f" تفاصيل الخطأ من Supabase: {db_update_response.error.message}"
                update_status(f"!!! {error_msg}")
                messagebox.showwarning("تحذير تحديث قاعدة البيانات", error_msg + "\nقد تحتاج إلى التحقق يدويًا.")
                return
        except Exception as e:
            update_status(f"!!! خطأ فادح أثناء تحديث سجل المود في قاعدة البيانات: {e}")
            messagebox.showerror("خطأ قاعدة البيانات", f"فشل تحديث سجل المود.\nالخطأ: {e}")
            return

        # Step 6: Delete old file if needed
        path_to_delete = None
        if original_file_path_in_storage:
            path_to_delete = original_file_path_in_storage
        elif original_mod_url:
            try:
                if MOD_BUCKET + "/" in original_mod_url:
                    path_part = original_mod_url.split(MOD_BUCKET + "/", 1)[1]
                    path_to_delete = path_part.split("?")[0]
                else:
                    update_status(f"!!! لا يمكن اشتقاق مسار الملف القديم من الرابط: {original_mod_url} للحذف.")
            except Exception as e_parse:
                update_status(f"!!! خطأ أثناء محاولة اشتقاق مسار الملف القديم: {e_parse}")

        if path_to_delete and path_to_delete != new_file_path_on_supabase:
            update_status(f"الخطوة 6: حذف الملف القديم من Supabase Storage (المسار: {path_to_delete})...")
            try:
                delete_response = storage_client.storage.from_(MOD_BUCKET).remove([path_to_delete])
                update_status(f"استجابة Supabase لطلب الحذف: {delete_response}")
                if delete_response and isinstance(delete_response, list) and delete_response[0].get('error') is None:
                    update_status(f"تم حذف الملف القديم '{path_to_delete}' بنجاح.")
                elif delete_response and isinstance(delete_response, list) and delete_response[0].get('error'):
                    error_msg = delete_response[0].get('message', 'Unknown error')
                    update_status(f"!!! لم يتم حذف الملف القديم '{path_to_delete}'. خطأ من Supabase: {error_msg}")

                    # إذا كان الخطأ هو عدم وجود الملف، نحاول البحث عنه بطريقة أخرى
                    if "not found" in str(error_msg).lower() or "not exist" in str(error_msg).lower():
                        update_status("محاولة البحث عن الملف القديم بطريقة أخرى...")
                        try:
                            # الحصول على قائمة الملفات في المجلد
                            file_list = storage_client.storage.from_(MOD_BUCKET).list()

                            # البحث عن الملفات التي تحتوي على اسم المود
                            mod_name_sanitized = sanitize_filename(mod_name).lower()
                            found_files = []

                            for file_info in file_list:
                                file_name = file_info.get('name', '')
                                if mod_name_sanitized in file_name.lower() and file_name != new_file_path_on_supabase:
                                    found_files.append(file_name)

                            if found_files:
                                update_status(f"تم العثور على {len(found_files)} ملفات محتملة للحذف: {found_files}")

                                # حذف جميع الملفات المحتملة
                                for file_to_delete in found_files:
                                    try:
                                        delete_resp = storage_client.storage.from_(MOD_BUCKET).remove([file_to_delete])
                                        if delete_resp and isinstance(delete_resp, list) and delete_resp[0].get('error') is None:
                                            update_status(f"تم حذف الملف '{file_to_delete}' بنجاح.")
                                        else:
                                            update_status(f"فشل حذف الملف '{file_to_delete}': {delete_resp}")
                                    except Exception as e_del:
                                        update_status(f"خطأ أثناء حذف الملف '{file_to_delete}': {e_del}")
                            else:
                                update_status("لم يتم العثور على ملفات محتملة للحذف.")
                        except Exception as e_list:
                            update_status(f"خطأ أثناء البحث عن الملفات: {e_list}")
                elif not delete_response:
                    update_status(f"استجابة الحذف فارغة، قد يكون الملف '{path_to_delete}' لم يُعثر عليه أو تم حذفه مسبقًا.")

                    # محاولة البحث عن الملف بطريقة أخرى
                    update_status("محاولة البحث عن الملف القديم بطريقة أخرى...")
                    try:
                        # الحصول على قائمة الملفات في المجلد
                        file_list = storage_client.storage.from_(MOD_BUCKET).list()

                        # البحث عن الملفات التي تحتوي على اسم المود
                        mod_name_sanitized = sanitize_filename(mod_name).lower()
                        found_files = []

                        for file_info in file_list:
                            file_name = file_info.get('name', '')
                            if mod_name_sanitized in file_name.lower() and file_name != new_file_path_on_supabase:
                                found_files.append(file_name)

                        if found_files:
                            update_status(f"تم العثور على {len(found_files)} ملفات محتملة للحذف: {found_files}")

                            # حذف جميع الملفات المحتملة
                            for file_to_delete in found_files:
                                try:
                                    delete_resp = storage_client.storage.from_(MOD_BUCKET).remove([file_to_delete])
                                    if delete_resp and isinstance(delete_resp, list) and delete_resp[0].get('error') is None:
                                        update_status(f"تم حذف الملف '{file_to_delete}' بنجاح.")
                                    else:
                                        update_status(f"فشل حذف الملف '{file_to_delete}': {delete_resp}")
                                except Exception as e_del:
                                    update_status(f"خطأ أثناء حذف الملف '{file_to_delete}': {e_del}")
                        else:
                            update_status("لم يتم العثور على ملفات محتملة للحذف.")
                    except Exception as e_list:
                        update_status(f"خطأ أثناء البحث عن الملفات: {e_list}")
                else:
                    update_status(f"!!! استجابة غير متوقعة من Supabase عند محاولة حذف '{path_to_delete}'.")
            except Exception as e:
                update_status(f"!!! خطأ أثناء حذف الملف القديم '{path_to_delete}': {e}")

                # محاولة البحث عن الملف بطريقة أخرى في حالة الخطأ
                update_status("محاولة البحث عن الملف القديم بطريقة أخرى بعد الخطأ...")
                try:
                    # الحصول على قائمة الملفات في المجلد
                    file_list = storage_client.storage.from_(MOD_BUCKET).list()

                    # البحث عن الملفات التي تحتوي على اسم المود
                    mod_name_sanitized = sanitize_filename(mod_name).lower()
                    found_files = []

                    for file_info in file_list:
                        file_name = file_info.get('name', '')
                        if mod_name_sanitized in file_name.lower() and file_name != new_file_path_on_supabase:
                            found_files.append(file_name)

                    if found_files:
                        update_status(f"تم العثور على {len(found_files)} ملفات محتملة للحذف: {found_files}")

                        # حذف جميع الملفات المحتملة
                        for file_to_delete in found_files:
                            try:
                                delete_resp = storage_client.storage.from_(MOD_BUCKET).remove([file_to_delete])
                                if delete_resp and isinstance(delete_resp, list) and delete_resp[0].get('error') is None:
                                    update_status(f"تم حذف الملف '{file_to_delete}' بنجاح.")
                                else:
                                    update_status(f"فشل حذف الملف '{file_to_delete}': {delete_resp}")
                            except Exception as e_del:
                                update_status(f"خطأ أثناء حذف الملف '{file_to_delete}': {e_del}")
                    else:
                        update_status("لم يتم العثور على ملفات محتملة للحذف.")
                except Exception as e_list:
                    update_status(f"خطأ أثناء البحث عن الملفات: {e_list}")
        elif path_to_delete == new_file_path_on_supabase:
            update_status("!!! تحذير: مسار الملف القديم هو نفسه مسار الملف الجديد. تم تخطي الحذف لمنع فقدان البيانات.")
        else:
            update_status("لم يتم توفير مسار صالح للملف القديم أو لا يمكن اشتقاقه. تم تخطي حذف الملف القديم.")

        # Final success message
        final_message = f"اكتملت عملية استبدال ملف المود '{mod_name}' بنجاح!\n"
        final_message += f"الرابط الجديد: {new_public_url}\n"
        final_message += f"الامتداد الجديد: {target_extension}"
        messagebox.showinfo("اكتملت العملية بنجاح", final_message)
        update_status(f"--- اكتملت عملية استبدال ملف المود: {mod_name} (ID: {mod_id}) ---")

    except Exception as e:
        update_status(f"!!! خطأ فادح وغير متوقع أثناء عملية استبدال الملف للمود {mod_name} (ID: {mod_id}): {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("خطأ فادح", f"حدث خطأ فادح أثناء معالجة المود '{mod_name}'.\nالخطأ: {e}")
    finally:
        # Clean up temp directory
        if os.path.exists(temp_extract_dir):
            try:
                shutil.rmtree(temp_extract_dir)
                update_status(f"تم تنظيف المجلد المؤقت: {temp_extract_dir}")
            except Exception as e_clean:
                update_status(f"!!! خطأ أثناء تنظيف المجلد المؤقت {temp_extract_dir}: {e_clean}")

        # Re-enable buttons
        if 'repair_mod_button' in globals() and repair_mod_button and repair_mod_button.winfo_exists():
            repair_mod_button.config(state=tk.NORMAL)
        if 'change_format_button' in globals() and change_format_button and change_format_button.winfo_exists():
            change_format_button.config(state=tk.NORMAL)
        if 'replace_mod_button' in globals() and replace_mod_button and replace_mod_button.winfo_exists():
            replace_mod_button.config(state=tk.NORMAL)


def sanitize_filename(filename):
    if not filename: return "default_filename"
    name_part, ext_part = os.path.splitext(filename)
    name_part = re.sub(r'[^\w.\-]+', '_', name_part)
    name_part = re.sub(r'_+', '_', name_part)
    name_part = name_part.strip('_')
    name_part = name_part[:100]
    if not name_part: name_part = "sanitized_file"
    return name_part + ext_part

def format_size_simple(size_bytes):
    if size_bytes < 1024: return f"{size_bytes} B"
    elif size_bytes < 1024*1024: return f"{size_bytes/1024:.2f} KB"
    else: return f"{size_bytes/(1024*1024):.2f} MB"

def show_mods_with_zero_downloads():
    """عرض المودات التي لم تحصل على أي تحميل"""
    global loaded_mods_data_list, mods_listbox, currently_visible_mod_data, view_images_button, download_link_button

    # تصفية المودات التي لم تحصل على أي تحميل
    zero_downloads_mods = []
    for mod in loaded_mods_data_list:
        # التحقق من أن عدد التحميلات 0 أو غير موجود
        downloads = mod.get('downloads', 0)
        if downloads == 0 or downloads is None:
            zero_downloads_mods.append(mod)

    # تحديث القائمة بالمودات المصفاة
    mods_listbox.delete(0, tk.END)
    currently_visible_mod_data.clear()

    for mod_item in zero_downloads_mods:
        mod_name = mod_item['name']
        mod_id = mod_item['id']
        download_url = mod_item.get('download_url', '')
        downloads = mod_item.get('downloads', 0)

        file_extension = ""
        if download_url:
            try:
                path_part = download_url.split('?')[0]
                _, file_extension = os.path.splitext(path_part)
            except Exception:
                file_extension = ""

        # Format the display text with extension and icon
        extension_lower = file_extension.lower()
        if extension_lower:
            if extension_lower == '.zip':
                format_icon = "📦" # Box icon for ZIP
            elif extension_lower == '.mcaddon':
                format_icon = "🧩" # Puzzle piece for MCADDON
            elif extension_lower == '.mcpack':
                format_icon = "📚" # Books for MCPACK
            else:
                format_icon = "📄" # Generic document for other formats

            display_text = f"ID: {mod_id} - {format_icon} {mod_name} ({extension_lower}) - التحميلات: {downloads}"
        else:
            display_text = f"ID: {mod_id} - {mod_name} - التحميلات: {downloads}"

        mods_listbox.insert(tk.END, display_text)
        currently_visible_mod_data.append(mod_item)

    update_status(f"تم العثور على {len(zero_downloads_mods)} مود بدون تحميلات")
    handle_deselect_all_mods() # Clear selections and disable buttons

def search_mods(search_term):
    """البحث عن المودات باستخدام مصطلح البحث"""
    global loaded_mods_data_list, mods_listbox

    if not search_term:
        # إذا كان مصطلح البحث فارغًا، أعد تحميل جميع المودات
        populate_mods_listbox("All")
        return

    # تحويل مصطلح البحث إلى أحرف صغيرة للمقارنة غير الحساسة لحالة الأحرف
    search_term = search_term.lower()

    # تصفية المودات التي تطابق مصطلح البحث
    filtered_mods = []
    for mod in loaded_mods_data_list:
        # البحث في اسم المود والوصف والفئة
        if (search_term in mod.get('name', '').lower() or
            search_term in mod.get('description', '').lower() or
            search_term in mod.get('category', '').lower()):
            filtered_mods.append(mod)

    # تحديث القائمة بالمودات المصفاة
    update_mods_listbox(filtered_mods)

    # تحديث حالة التطبيق
    update_status(f"تم العثور على {len(filtered_mods)} مود يطابق مصطلح البحث: '{search_term}'")

def sort_mods_by_date():
    """ترتيب المودات من الأحدث إلى الأقدم"""
    global loaded_mods_data_list, mods_listbox

    # نسخة من القائمة للترتيب
    sorted_mods = loaded_mods_data_list.copy()

    # ترتيب المودات حسب تاريخ الإنشاء (من الأحدث إلى الأقدم)
    # ملاحظة: تم إزالة الاعتماد على 'updated_at' لأنه غير موجود في قاعدة البيانات
    sorted_mods.sort(key=lambda x: x.get('created_at', '1970-01-01T00:00:00'), reverse=True)

    # تحديث القائمة بالمودات المرتبة
    update_mods_listbox(sorted_mods)

    # تحديث حالة التطبيق
    update_status(f"تم ترتيب {len(sorted_mods)} مود من الأحدث إلى الأقدم حسب تاريخ الإنشاء")

def update_button_states():
    """تحديث حالة الأزرار بناءً على المودات المحددة"""
    global selected_mods_for_repair, repair_mod_button, change_format_button, replace_mod_button, add_images_button, view_images_button

    # تحقق من وجود الأزرار قبل محاولة تحديثها
    if not all([repair_mod_button, change_format_button, replace_mod_button, add_images_button]):
        return

    # تعطيل جميع الأزرار إذا لم يتم تحديد أي مود
    if not selected_mods_for_repair:
        repair_mod_button.config(state=tk.DISABLED)
        change_format_button.config(state=tk.DISABLED)
        replace_mod_button.config(state=tk.DISABLED)
        add_images_button.config(state=tk.DISABLED)
        if 'view_images_button' in globals() and view_images_button:
            view_images_button.config(state=tk.DISABLED)
        return

    # تمكين زر إصلاح المود إذا تم تحديد مود واحد على الأقل
    repair_mod_button.config(state=tk.NORMAL)

    # تمكين أزرار تغيير الصيغة واستبدال الملف وإضافة الصور وعرض الصور فقط إذا تم تحديد مود واحد
    if len(selected_mods_for_repair) == 1:
        change_format_button.config(state=tk.NORMAL)
        replace_mod_button.config(state=tk.NORMAL)
        add_images_button.config(state=tk.NORMAL)
        if 'view_images_button' in globals() and view_images_button:
            view_images_button.config(state=tk.NORMAL)
    else:
        change_format_button.config(state=tk.DISABLED)
        replace_mod_button.config(state=tk.DISABLED)
        add_images_button.config(state=tk.DISABLED)
        if 'view_images_button' in globals() and view_images_button:
            view_images_button.config(state=tk.DISABLED)

def update_mods_listbox(mods_list):
    """تحديث قائمة المودات في واجهة المستخدم"""
    global mods_listbox, selected_mods_for_repair, loaded_mods_data_list

    # تحديث المتغير العام للمودات المرئية حاليًا
    global currently_visible_mod_data
    currently_visible_mod_data = mods_list

    # مسح القائمة الحالية
    mods_listbox.delete(0, tk.END)

    # إعادة تعيين المودات المحددة
    selected_mods_for_repair = []

    # إضافة المودات المصفاة إلى القائمة
    for mod in mods_list:
        mod_name = mod.get('name', 'بدون اسم')
        mod_category = mod.get('category', 'بدون فئة')
        display_text = f"{mod_name} ({mod_category})"
        mods_listbox.insert(tk.END, display_text)

    # تحديث حالة الأزرار
    update_button_states()

def compress_image(image_data, compression_level='normal'):
    """Compress an image based on the specified compression level.

    Args:
        image_data: Image data as bytes
        compression_level: 'normal', 'medium', or 'high'

    Returns:
        Tuple of (compressed_bytes, format, content_type, original_size, compressed_size)
    """
    from PIL import Image
    from io import BytesIO

    original_size = len(image_data)

    try:
        # Open the image
        img = Image.open(BytesIO(image_data))

        # Check if it's a GIF and animated
        if img.format == 'GIF' and getattr(img, 'is_animated', False):
            # Directly return the result of compress_animated_gif
            return compress_animated_gif(image_data, compression_level)

        # Define compression parameters based on level
        if compression_level == 'high':
            # High compression (smallest file size, lowest quality)
            jpeg_quality = 60
            png_compression = 9
            webp_quality = 60
            resize_factor = 0.8  # Reduce dimensions to 80%
        elif compression_level == 'medium':
            # Medium compression
            jpeg_quality = 75
            png_compression = 7
            webp_quality = 75
            resize_factor = 0.9  # Reduce dimensions to 90%
        else:  # 'normal' or any other value
            # Normal compression (better quality, larger file size)
            jpeg_quality = 85
            png_compression = 6
            webp_quality = 85
            resize_factor = 1.0  # No resize

        # Resize image if needed
        if resize_factor < 1.0:
            new_width = int(img.width * resize_factor)
            new_height = int(img.height * resize_factor)
            img = img.resize((new_width, new_height), Image.LANCZOS)

        # Determine best format based on image content
        img_format = img.format if img.format else 'JPEG'
        if img_format.upper() in ['PNG', 'WEBP', 'JPEG', 'JPG']:
            img_format_save = img_format.upper()
        else:
            # Default to JPEG for most images
            img_format_save = 'JPEG'

        # Prepare compression parameters
        output_buffer = BytesIO()
        save_kwargs = {'format': img_format_save}

        if img_format_save == 'JPEG':
            save_kwargs['quality'] = jpeg_quality
            save_kwargs['optimize'] = True
        elif img_format_save == 'PNG':
            save_kwargs['optimize'] = True
            save_kwargs['compress_level'] = png_compression
        elif img_format_save == 'WEBP':
            save_kwargs['quality'] = webp_quality

        # Save compressed image
        img.save(output_buffer, **save_kwargs)
        compressed_bytes = output_buffer.getvalue()
        compressed_size = len(compressed_bytes)

        # Return compressed image data and metadata
        content_type = f'image/{img_format_save.lower()}'
        return compressed_bytes, img_format_save.lower(), content_type, original_size, compressed_size

    except Exception as e:
        update_status(f"خطأ في ضغط الصورة: {e}")
        # Return original image if compression fails
        return image_data, None, None, original_size, original_size

def compress_animated_gif(gif_data, compression_level='normal'):
    """Compress an animated GIF while preserving animation.

    Args:
        gif_data: GIF image data as bytes
        compression_level: 'normal', 'medium', or 'high'

    Returns:
        Tuple of (compressed_bytes, format, content_type, original_size, compressed_size)
    """
    from PIL import Image
    from io import BytesIO

    original_size = len(gif_data)

    try:
        # Open the GIF
        gif = Image.open(BytesIO(gif_data))

        # Check if it's actually a GIF and has multiple frames
        if gif.format != 'GIF' or not getattr(gif, 'is_animated', False):
            # Not an animated GIF, use regular compression
            update_status("GIF غير متحرك، سيتم استخدام الضغط العادي.")
            return compress_image(gif_data, compression_level)

        # Define compression parameters based on level
        if compression_level == 'high':
            resize_factor = 0.7  # Reduce dimensions to 70%
            colors = 128  # Reduce color palette
        elif compression_level == 'medium':
            resize_factor = 0.8  # Reduce dimensions to 80%
            colors = 192  # Reduce color palette
        else:  # 'normal' or any other value
            resize_factor = 0.9  # Reduce dimensions to 90%
            colors = 256  # Keep full color palette

        update_status(f"ضغط GIF متحرك بمستوى {compression_level} (عامل التحجيم: {resize_factor}, الألوان: {colors})")

        # Get original dimensions
        original_width, original_height = gif.size

        # Calculate new dimensions
        new_width = int(original_width * resize_factor)
        new_height = int(original_height * resize_factor)

        # Create a new GIF with the same parameters
        frames = []
        durations = []

        # Process each frame
        try:
            i = 0
            while True:
                gif.seek(i)

                # Get frame duration
                duration = gif.info.get('duration', 100)  # Default to 100ms if not specified
                durations.append(duration)

                # Resize frame
                frame = gif.copy()
                if resize_factor < 1.0:
                    frame = frame.resize((new_width, new_height), Image.LANCZOS)

                # Convert to P mode with optimized palette if needed
                if frame.mode != 'P':
                    frame = frame.convert('P', palette=Image.ADAPTIVE, colors=colors)

                frames.append(frame)
                i += 1
        except EOFError:
            # End of frames
            pass

        # Save the compressed GIF
        output_buffer = BytesIO()

        # Save the first frame
        frames[0].save(
            output_buffer,
            format='GIF',
            save_all=True,
            append_images=frames[1:],
            optimize=True,
            duration=durations,
            loop=0,  # Loop forever
            disposal=2  # Restore to background color
        )

        compressed_bytes = output_buffer.getvalue()
        compressed_size = len(compressed_bytes)

        # Return compressed GIF data
        update_status(f"تم ضغط GIF متحرك: الحجم الأصلي: {format_size_simple(original_size)}, الحجم المضغوط: {format_size_simple(compressed_size)}")
        return compressed_bytes, 'gif', 'image/gif', original_size, compressed_size

    except Exception as e:
        update_status(f"خطأ في ضغط GIF متحرك: {e}")
        # Return original image if compression fails
        return gif_data, 'gif', 'image/gif', original_size, original_size

# --- Main GUI Setup ---
def handle_open_mod_download_link():
    """فتح رابط تحميل المود المحدد في المتصفح"""
    global selected_mods_for_repair

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لفتح رابط التحميل الخاص به.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_name = mod_data['name']
    download_url = mod_data.get('download_url', '')

    if not download_url:
        messagebox.showwarning("رابط غير متوفر", f"المود '{mod_name}' ليس له رابط تحميل متوفر.")
        return

    try:
        # فتح الرابط في المتصفح الافتراضي
        update_status(f"جاري فتح رابط تحميل المود '{mod_name}' في المتصفح...")
        webbrowser.open(download_url)
        update_status(f"تم فتح رابط التحميل: {download_url}")
    except Exception as e:
        update_status(f"!!! خطأ أثناء فتح رابط التحميل: {e}")
        messagebox.showerror("خطأ", f"فشل فتح رابط التحميل: {e}")

def handle_republish_mod():
    """إعادة نشر المود المحدد من الصفر"""
    global selected_mods_for_repair, upload_after_repair_var, repair_mod_button, change_format_button, republish_mod_button

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لإعادة نشره.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']
    original_file_path_in_storage = mod_data.get('file_path_in_storage')

    # التحقق من صيغة الملف الحالية
    current_ext = ""
    if original_mod_url:
        try:
            path_part = original_mod_url.split('?')[0]
            _, current_ext = os.path.splitext(path_part)
        except Exception:
            current_ext = ""

    # اختيار الصيغة المناسبة للنشر
    valid_extensions = ['.mcaddon', '.mcpack']
    target_extension = ""

    if current_ext.lower() in valid_extensions:
        target_extension = current_ext
    else:
        # اختيار صيغة افتراضية إذا كانت الصيغة الحالية غير صالحة
        extension_prompt = "اختر الصيغة المناسبة لإعادة نشر المود:\n"
        extension_prompt += "1. .mcaddon (للمودات الكاملة)\n"
        extension_prompt += "2. .mcpack (لحزم الموارد أو السلوك)"

        choice_input = simpledialog.askstring(
            "اختيار صيغة المود",
            extension_prompt,
            parent=window
        )

        if choice_input is None:
            update_status("تم إلغاء عملية إعادة النشر.")
            return

        try:
            choice_input_clean = choice_input.strip() if choice_input else ""
            choice_index = int(choice_input_clean) - 1
            if 0 <= choice_index < len(valid_extensions):
                target_extension = valid_extensions[choice_index]
            else:
                messagebox.showerror("اختيار غير صالح", "الرجاء إدخال رقم صالح من القائمة.")
                return
        except ValueError:
            messagebox.showerror("إدخال غير صالح", "الرجاء إدخال رقم.")
            return

    # تأكيد العملية
    confirm_message = f"سيتم إعادة نشر المود '{mod_name}' بالخطوات التالية:\n\n"
    confirm_message += "1. تحميل الملف الحالي\n"
    confirm_message += "2. حذف المود من قاعدة البيانات\n"
    confirm_message += "3. حذف جميع الملفات المرتبطة به من التخزين\n"
    confirm_message += f"4. إعادة نشر المود بصيغة {target_extension}\n\n"
    confirm_message += "هل أنت متأكد من المتابعة؟"

    confirmation = messagebox.askyesno("تأكيد إعادة النشر", confirm_message)
    if not confirmation:
        update_status("تم إلغاء عملية إعادة النشر.")
        return

    # تعطيل الأزرار أثناء المعالجة
    if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
    if change_format_button: change_format_button.config(state=tk.DISABLED)
    if republish_mod_button: republish_mod_button.config(state=tk.DISABLED)

    update_status(f"\n--- بدء عملية إعادة نشر المود: {mod_name} (ID: {mod_id}) ---")

    # تنفيذ العملية في خلفية
    run_in_thread(
        republish_mod_task,
        mod_id,
        mod_name,
        original_mod_url,
        original_file_path_in_storage,
        target_extension
    )

def republish_mod_task(mod_id, mod_name, mod_url, original_file_path_in_storage, target_extension):
    """مهمة إعادة نشر المود"""
    temp_extract_dir = f"temp_republish_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"
    local_repaired_filepath = None

    try:
        # الخطوة 1: تحميل ملف المود
        update_status(f"الخطوة 1: تحميل ملف المود '{mod_name}' من الرابط '{mod_url}'...")
        mod_content_stream = BytesIO()
        total_downloaded = 0
        last_prog_update = time.time()

        try:
            response = requests.get(mod_url, stream=True, timeout=600)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            update_status(f"!!! خطأ فادح أثناء تحميل المود: {e}")
            _re_enable_buttons_after_republish()
            return

        total_size = int(response.headers.get('content-length', 0))
        update_status(f"حجم الملف المتوقع: {format_size_simple(total_size)}" if total_size else "جاري حساب حجم الملف...")

        for chunk in response.iter_content(chunk_size=8192 * 4):
            if chunk:
                mod_content_stream.write(chunk)
                total_downloaded += len(chunk)
                if time.time() - last_prog_update > 0.5 or total_downloaded == total_size:
                    prog_percent = f" ({(total_downloaded/total_size*100):.1f}%)" if total_size else ""
                    update_status(f"جاري التحميل: {format_size_simple(total_downloaded)}{' / ' + format_size_simple(total_size) if total_size else ''}{prog_percent}")
                    last_prog_update = time.time()

        mod_bytes = mod_content_stream.getvalue()
        update_status(f"اكتمل تحميل الملف. الحجم الكلي: {format_size_simple(len(mod_bytes))}.")

        # الخطوة 2: التحقق من ملف ZIP وفك الضغط
        update_status(f"الخطوة 2: التحقق من ملف ZIP وفك الضغط في المجلد المؤقت: {temp_extract_dir}")
        if not os.path.exists(temp_extract_dir): os.makedirs(temp_extract_dir)

        try:
            with zipfile.ZipFile(BytesIO(mod_bytes), 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            update_status("تم فك ضغط المود بنجاح.")
        except zipfile.BadZipFile:
            update_status("!!! خطأ: الملف الذي تم تحميله ليس ملف ZIP صالحًا. لا يمكن المتابعة.")
            _re_enable_buttons_after_republish()
            return

        # الخطوة 3: حذف المود من قاعدة البيانات
        update_status(f"الخطوة 3: حذف المود (ID: {mod_id}) من قاعدة البيانات...")
        try:
            delete_response = app_db_client.table(MODS_TABLE_NAME).delete().eq("id", mod_id).execute()
            if delete_response.data:
                update_status(f"تم حذف المود '{mod_name}' (ID: {mod_id}) من قاعدة البيانات بنجاح.")
            else:
                update_status(f"!!! تحذير: لم يتم العثور على المود (ID: {mod_id}) في قاعدة البيانات أو حدث خطأ أثناء الحذف.")
        except Exception as e:
            update_status(f"!!! خطأ أثناء حذف المود من قاعدة البيانات: {e}")
            # نستمر في العملية حتى لو فشل الحذف

        # الخطوة 4: حذف جميع الملفات المرتبطة بالمود من التخزين
        update_status("الخطوة 4: حذف جميع الملفات المرتبطة بالمود من التخزين...")

        # محاولة حذف الملف الرئيسي
        path_to_delete = None
        if original_file_path_in_storage:
            path_to_delete = original_file_path_in_storage
        elif mod_url:
            try:
                if MOD_BUCKET + "/" in mod_url:
                    path_part = mod_url.split(MOD_BUCKET + "/", 1)[1]
                    path_to_delete = path_part.split("?")[0]
                else:
                    url_path = urlparse(mod_url).path
                    filename = os.path.basename(url_path)
                    if filename:
                        path_to_delete = filename.split("?")[0]
            except Exception as e_parse:
                update_status(f"!!! خطأ أثناء محاولة اشتقاق مسار الملف القديم: {e_parse}")

        if path_to_delete:
            try:
                delete_response = storage_client.storage.from_(MOD_BUCKET).remove([path_to_delete])
                update_status(f"محاولة حذف الملف الرئيسي: {path_to_delete}")
                if delete_response and isinstance(delete_response, list) and delete_response[0].get('error') is None:
                    update_status(f"تم حذف الملف الرئيسي '{path_to_delete}' بنجاح.")
                else:
                    update_status(f"!!! تحذير: فشل حذف الملف الرئيسي '{path_to_delete}' أو لم يتم العثور عليه.")
            except Exception as e:
                update_status(f"!!! خطأ أثناء حذف الملف الرئيسي: {e}")

        # البحث عن جميع الملفات المرتبطة بالمود وحذفها
        try:
            # الحصول على قائمة الملفات في المجلد
            file_list = storage_client.storage.from_(MOD_BUCKET).list()
            update_status(f"تم العثور على {len(file_list)} ملف في المجلد")

            # البحث عن الملفات التي تحتوي على اسم المود
            mod_name_sanitized = sanitize_filename(mod_name).lower()
            found_files = []

            for file_info in file_list:
                file_name = file_info.get('name', '')
                if mod_name_sanitized in file_name.lower():
                    found_files.append(file_name)

            if found_files:
                update_status(f"تم العثور على {len(found_files)} ملفات محتملة للحذف: {found_files}")

                # حذف جميع الملفات المحتملة
                for file_to_delete in found_files:
                    try:
                        delete_resp = storage_client.storage.from_(MOD_BUCKET).remove([file_to_delete])
                        if delete_resp and isinstance(delete_resp, list) and delete_resp[0].get('error') is None:
                            update_status(f"تم حذف الملف '{file_to_delete}' بنجاح.")
                        else:
                            update_status(f"فشل حذف الملف '{file_to_delete}': {delete_resp}")
                    except Exception as e_del:
                        update_status(f"خطأ أثناء حذف الملف '{file_to_delete}': {e_del}")
            else:
                update_status("لم يتم العثور على ملفات إضافية محتملة للحذف.")
        except Exception as e_list:
            update_status(f"خطأ أثناء البحث عن الملفات: {e_list}")

        # الخطوة 5: إعادة ضغط المود بالصيغة المطلوبة
        update_status(f"الخطوة 5: إعادة ضغط المود بصيغة {target_extension}...")
        if not os.path.exists(LOCAL_REPAIRED_MODS_DIR): os.makedirs(LOCAL_REPAIRED_MODS_DIR)

        repaired_filename_base = sanitize_filename(mod_name)
        local_repaired_filename = f"{repaired_filename_base}_republished_{int(time.time())}{target_extension}"
        local_repaired_filepath = os.path.join(LOCAL_REPAIRED_MODS_DIR, local_repaired_filename)

        with zipfile.ZipFile(local_repaired_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(temp_extract_dir):
                for file_in_dir in files:
                    file_path_abs = os.path.join(root, file_in_dir)
                    archive_name = os.path.relpath(file_path_abs, temp_extract_dir)
                    zipf.write(file_path_abs, archive_name)

        repacked_size_bytes = os.path.getsize(local_repaired_filepath)
        update_status(f"تم إعادة ضغط المود بنجاح كـ '{local_repaired_filename}' (الحجم: {format_size_simple(repacked_size_bytes)}).")
        update_status(f"المود المُعاد نشره محفوظ محليًا في: {os.path.abspath(local_repaired_filepath)}")

        # الخطوة 6: رفع المود الجديد إلى Supabase
        update_status(f"الخطوة 6: رفع المود الجديد '{local_repaired_filename}' إلى Supabase Storage...")
        new_file_path_on_supabase = local_repaired_filename

        try:
            with open(local_repaired_filepath, 'rb') as f:
                upload_response_dict = storage_client.storage.from_(MOD_BUCKET).upload(
                    path=new_file_path_on_supabase,
                    file=f,
                    file_options={"content-type": "application/vnd.microsoft.appx", "cache-control": "3600", "upsert": "false"}
                )
            update_status(f"تم إرسال طلب الرفع. استجابة أولية: {upload_response_dict}")
            update_status(f"تم رفع الملف '{new_file_path_on_supabase}' بنجاح إلى Supabase.")
        except Exception as e:
            update_status(f"!!! خطأ أثناء رفع المود الجديد إلى Supabase Storage: {e}")
            error_message_detail = str(e)
            if hasattr(e, 'args') and e.args: error_message_detail = f"{e} - {e.args}"
            messagebox.showerror("فشل الرفع", f"فشل رفع المود الجديد إلى Supabase Storage.\nالخطأ: {error_message_detail}")
            _re_enable_buttons_after_republish()
            return

        # الخطوة 7: الحصول على الرابط العام للمود الجديد
        update_status("الخطوة 7: الحصول على الرابط العام للمود الجديد...")
        try:
            new_public_url = storage_client.storage.from_(MOD_BUCKET).get_public_url(new_file_path_on_supabase)
            update_status(f"الرابط العام الجديد: {new_public_url}")
        except Exception as e:
            update_status(f"!!! خطأ أثناء الحصول على الرابط العام للمود الجديد: {e}")
            messagebox.showerror("خطأ في الرابط", f"فشل الحصول على الرابط العام للمود الجديد.\nالخطأ: {e}")
            _re_enable_buttons_after_republish()
            return

        # الخطوة 8: إعادة إنشاء سجل المود في قاعدة البيانات
        update_status("الخطوة 8: إعادة إنشاء سجل المود في قاعدة البيانات...")
        try:
            # استخدام نفس معرف المود السابق للحفاظ على التوافق
            new_mod_data = {
                "id": mod_id,
                "name": mod_name,
                "download_url": new_public_url,
                # إعادة تعيين عدادات التحميلات والإعجابات
                "downloads": 0,
                "likes": 0,
                "clicks": 0
            }

            # إذا كانت هناك بيانات إضافية في المود الأصلي، يمكن استخدامها هنا
            if 'description' in selected_mods_for_repair[0]:
                new_mod_data['description'] = selected_mods_for_repair[0]['description']
            if 'category' in selected_mods_for_repair[0]:
                new_mod_data['category'] = selected_mods_for_repair[0]['category']
            if 'image_urls' in selected_mods_for_repair[0]:
                new_mod_data['image_urls'] = selected_mods_for_repair[0]['image_urls']
            if 'version' in selected_mods_for_repair[0]:
                new_mod_data['version'] = selected_mods_for_repair[0]['version']
            if 'size' in selected_mods_for_repair[0]:
                new_mod_data['size'] = format_size_simple(repacked_size_bytes)

            db_insert_response = app_db_client.table(MODS_TABLE_NAME).insert(new_mod_data).execute()

            if db_insert_response.data:
                update_status("تم إعادة إنشاء سجل المود في قاعدة البيانات بنجاح.")

                # تحديث القائمة المحلية
                global loaded_mods_data_list
                for i, mod_in_list in enumerate(loaded_mods_data_list):
                    if mod_in_list['id'] == mod_id:
                        loaded_mods_data_list[i] = new_mod_data
                        update_status(f"تم تحديث بيانات المود '{mod_name}' (ID: {mod_id}) في القائمة المحملة محلياً.")
                        break
            else:
                error_msg = f"فشل إعادة إنشاء سجل المود في قاعدة البيانات. لا توجد بيانات في الاستجابة."
                if hasattr(db_insert_response, 'error') and db_insert_response.error:
                    error_msg += f" تفاصيل الخطأ من Supabase: {db_insert_response.error.message}"
                update_status(f"!!! {error_msg}")
                messagebox.showwarning("تحذير تحديث قاعدة البيانات", error_msg + "\nقد تحتاج إلى التحقق يدويًا.")
                _re_enable_buttons_after_republish()
                return
        except Exception as e:
            update_status(f"!!! خطأ فادح أثناء إعادة إنشاء سجل المود في قاعدة البيانات: {e}")
            messagebox.showerror("خطأ قاعدة البيانات", f"فشل إعادة إنشاء سجل المود.\nالخطأ: {e}")
            _re_enable_buttons_after_republish()
            return

        # رسالة النجاح النهائية
        final_message = f"اكتملت عملية إعادة نشر المود '{mod_name}' بنجاح!\n"
        final_message += f"الرابط الجديد: {new_public_url}\n"
        final_message += f"الصيغة الجديدة: {target_extension}\n"
        final_message += "تم إعادة تعيين عدادات التحميلات والإعجابات."
        messagebox.showinfo("اكتملت العملية بنجاح", final_message)
        update_status(f"--- اكتملت عملية إعادة نشر المود: {mod_name} (ID: {mod_id}) ---")

    except Exception as e:
        update_status(f"!!! خطأ فادح وغير متوقع أثناء عملية إعادة النشر للمود {mod_name} (ID: {mod_id}): {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("خطأ فادح", f"حدث خطأ فادح أثناء معالجة المود '{mod_name}'.\nالخطأ: {e}")
    finally:
        # تنظيف المجلد المؤقت
        if os.path.exists(temp_extract_dir):
            try:
                shutil.rmtree(temp_extract_dir)
                update_status(f"تم تنظيف المجلد المؤقت: {temp_extract_dir}")
            except Exception as e_clean:
                update_status(f"!!! خطأ أثناء تنظيف المجلد المؤقت {temp_extract_dir}: {e_clean}")

        # إعادة تفعيل الأزرار
        _re_enable_buttons_after_republish()

def _re_enable_buttons_after_republish():
    """إعادة تفعيل الأزرار بعد عملية إعادة النشر"""
    if 'repair_mod_button' in globals() and repair_mod_button and repair_mod_button.winfo_exists():
        repair_mod_button.config(state=tk.NORMAL if selected_mods_for_repair else tk.DISABLED)
    if 'change_format_button' in globals() and change_format_button and change_format_button.winfo_exists():
        change_format_button.config(state=tk.NORMAL if selected_mods_for_repair else tk.DISABLED)
    if 'republish_mod_button' in globals() and republish_mod_button and republish_mod_button.winfo_exists():
        republish_mod_button.config(state=tk.NORMAL if selected_mods_for_repair and len(selected_mods_for_repair) == 1 else tk.DISABLED)

def main_gui():
    global window, mods_listbox, status_text, load_mods_button, repair_mod_button, change_format_button, replace_mod_button, add_images_button, upload_after_repair_var, select_all_button, deselect_all_button, download_link_button, view_images_button, republish_mod_button, generate_description_button, manage_api_keys_button, telegram_desc_button

    window = tk.Tk()
    window.title("أداة إصلاح وتغيير صيغة المودات")
    window.geometry("1000x700")  # Increased width to accommodate new button

    top_controls_frame = ttk.Frame(window, padding="10")
    top_controls_frame.pack(fill=tk.X)

    load_mods_button = ttk.Button(top_controls_frame, text="تحميل قائمة المودات", command=handle_load_published_mods)
    load_mods_button.pack(side=tk.LEFT, padx=2)

    select_all_button = ttk.Button(top_controls_frame, text="تحديد الكل", command=handle_select_all_mods)
    select_all_button.pack(side=tk.LEFT, padx=2)

    deselect_all_button = ttk.Button(top_controls_frame, text="إلغاء تحديد الكل", command=handle_deselect_all_mods)
    deselect_all_button.pack(side=tk.LEFT, padx=2)

    repair_mod_button = ttk.Button(top_controls_frame, text="إصلاح المحدد", command=handle_repair_mod_action, state=tk.DISABLED)
    repair_mod_button.pack(side=tk.LEFT, padx=2)

    change_format_button = ttk.Button(top_controls_frame, text="تغيير صيغة المحدد", command=handle_change_format_action, state=tk.DISABLED)
    change_format_button.pack(side=tk.LEFT, padx=2)

    replace_mod_button = ttk.Button(top_controls_frame, text="استبدال ملف المود", command=handle_replace_mod_file, state=tk.DISABLED)
    replace_mod_button.pack(side=tk.LEFT, padx=2)

    add_images_button = ttk.Button(top_controls_frame, text="إضافة صور للمود", command=handle_add_images_to_mod, state=tk.DISABLED)
    add_images_button.pack(side=tk.LEFT, padx=2)

    view_images_button = ttk.Button(top_controls_frame, text="عرض صور المود", command=handle_view_mod_images, state=tk.DISABLED)
    view_images_button.pack(side=tk.LEFT, padx=2)

    download_link_button = ttk.Button(top_controls_frame, text="فتح رابط التحميل", command=handle_open_mod_download_link, state=tk.DISABLED)
    download_link_button.pack(side=tk.LEFT, padx=2)

    republish_mod_button = ttk.Button(top_controls_frame, text="إعادة نشر المود", command=handle_republish_mod, state=tk.DISABLED)
    republish_mod_button.pack(side=tk.LEFT, padx=2)

    # إضافة أزرار إدارة الأوصاف

    generate_description_button = ttk.Button(top_controls_frame, text="إدارة جميع الأوصاف", command=find_mods_missing_descriptions)
    generate_description_button.pack(side=tk.LEFT, padx=2)

    # NEW: Button to show mods missing Telegram descriptions (includes broken descriptions detection)
    ttk.Button(top_controls_frame, text="فحص أوصاف تيليجرام المتقدم", command=find_mods_missing_telegram_descriptions).pack(side=tk.LEFT, padx=2)

    # NEW: Button to show tracked problematic mods
    ttk.Button(top_controls_frame, text="📋 المودات المتتبعة", command=show_tracked_problematic_mods).pack(side=tk.LEFT, padx=2)

    # إضافة زر إدارة مفاتيح API
    manage_api_keys_button = ttk.Button(top_controls_frame, text="إدارة مفاتيح API", command=open_repair_gemini_management_dialog)
    manage_api_keys_button.pack(side=tk.LEFT, padx=2)

    # إضافة زر إصلاح قاعدة البيانات
    database_fix_button = ttk.Button(top_controls_frame, text="إصلاح قاعدة البيانات", command=show_database_fix_instructions)
    database_fix_button.pack(side=tk.LEFT, padx=2)

    upload_after_repair_var = tk.BooleanVar(value=True)
    upload_checkbox = ttk.Checkbutton(top_controls_frame, text="رفع إلى Supabase بعد المعالجة (يحذف القديم)", variable=upload_after_repair_var)
    upload_checkbox.pack(side=tk.LEFT, padx=5)

    # --- Filter and Search Frame ---
    filter_frame = ttk.Frame(window, padding="10")
    filter_frame.pack(fill=tk.X)

    # إضافة مربع البحث
    search_frame = ttk.Frame(filter_frame)
    search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

    ttk.Label(search_frame, text="بحث:").pack(side=tk.LEFT, padx=(0, 5))
    search_entry = ttk.Entry(search_frame, width=30)
    search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

    search_button = ttk.Button(search_frame, text="بحث",
                              command=lambda: search_mods(search_entry.get()))
    search_button.pack(side=tk.LEFT)

    # إضافة زر الترتيب من الأحدث إلى الأقدم
    sort_button = ttk.Button(filter_frame, text="ترتيب من الأحدث إلى الأقدم",
                            command=sort_mods_by_date)
    sort_button.pack(side=tk.RIGHT, padx=5)

    ttk.Label(filter_frame, text="تصفية حسب الفئة:").pack(side=tk.LEFT, padx=(0,5))

    all_button = ttk.Button(filter_frame, text="الكل", command=lambda: populate_mods_listbox("All"))
    all_button.pack(side=tk.LEFT, padx=2)

    # Assuming category names like 'Shaders', 'Packs', 'Addons'. Adjust if different.
    shaders_button = ttk.Button(filter_frame, text="شادر", command=lambda: populate_mods_listbox("Shaders")) # Or "Shader"
    shaders_button.pack(side=tk.LEFT, padx=2)

    packs_button = ttk.Button(filter_frame, text="باكات", command=lambda: populate_mods_listbox("Packs")) # Or "Texture Packs"
    packs_button.pack(side=tk.LEFT, padx=2)

    addons_button = ttk.Button(filter_frame, text="إضافات", command=lambda: populate_mods_listbox("Addons")) # Or "Addon"
    addons_button.pack(side=tk.LEFT, padx=2)

    # Add button for mods with zero downloads
    zero_downloads_button = ttk.Button(filter_frame, text="بدون تحميلات", command=show_mods_with_zero_downloads)
    zero_downloads_button.pack(side=tk.LEFT, padx=2)

    # You can add more buttons for other categories if needed

    list_frame = ttk.LabelFrame(window, text="المودات المنشورة (اختر مودًا أو أكثر)", padding="10") # Updated text
    list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    mods_listbox_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL)
    mods_listbox = tk.Listbox(list_frame, yscrollcommand=mods_listbox_scrollbar.set, exportselection=False, height=10, selectmode=tk.EXTENDED) # Added selectmode=tk.EXTENDED
    mods_listbox_scrollbar.config(command=mods_listbox.yview)
    mods_listbox_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    mods_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    mods_listbox.bind('<<ListboxSelect>>', on_mod_select)

    status_frame = ttk.LabelFrame(window, text="سجل الحالة", padding="10")
    status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    status_text = scrolledtext.ScrolledText(status_frame, wrap=tk.WORD, height=15, state=tk.DISABLED, font=("Segoe UI", 9))
    status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0,5))
    copy_log_button = ttk.Button(status_frame, text="نسخ\nالسجل", command=copy_log_from_status)
    copy_log_button.pack(side=tk.RIGHT, fill=tk.Y, pady=2)
    if not PYPERCLIP_AVAILABLE:
        copy_log_button.config(state=tk.DISABLED)
        update_status("زر نسخ السجل معطل لعدم توفر pyperclip.")

    if not os.path.exists(LOCAL_REPAIRED_MODS_DIR):
        try: os.makedirs(LOCAL_REPAIRED_MODS_DIR)
        except OSError as e: print(f"Could not create directory for repaired mods: {LOCAL_REPAIRED_MODS_DIR} - {e}")

    update_status("أداة الإصلاح جاهزة.")
    if not configure_repair_gemini_client() and GEMINI_AVAILABLE:
        update_status("!!! تحذير: فشل التكوين الأولي لـ Gemini. قد يتم تعطيل ميزات الإصلاح الذكي.")

    if not APP_DB_CLIENT_OK:
        update_status("!!! تحذير: فشل الاتصال بقاعدة بيانات التطبيق. تحميل المودات وتحديثها سيكون معطلاً.")
        load_mods_button.config(state=tk.DISABLED)
        if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
        if change_format_button: change_format_button.config(state=tk.DISABLED)
        upload_checkbox.config(state=tk.DISABLED)
    if not STORAGE_CLIENT_OK:
        update_status("!!! تحذير: فشل الاتصال بـ Supabase Storage. تحميل المودات الأصلية والرفع سيكون معطلاً.")
        if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
        if change_format_button: change_format_button.config(state=tk.DISABLED)
        upload_checkbox.config(state=tk.DISABLED)

    # Add custom description generation button
    custom_desc_button = ttk.Button(
        top_controls_frame,
        text="إنشاء أوصاف مخصصة",
        command=handle_generate_custom_descriptions
    )
    custom_desc_button.pack(side=tk.LEFT, padx=5)

    # Add Telegram custom description generation button
    telegram_desc_button = ttk.Button(
        top_controls_frame,
        text="إنشاء أوصاف تليجرام",
        command=handle_generate_telegram_descriptions
    )
    telegram_desc_button.pack(side=tk.LEFT, padx=5)

    # Add button to view Telegram descriptions
    view_telegram_desc_button = ttk.Button(
        top_controls_frame,
        text="عرض أوصاف تليجرام",
        command=show_telegram_descriptions
    )
    view_telegram_desc_button.pack(side=tk.LEFT, padx=5)

    window.mainloop()

def check_database_schema():
    """فحص قاعدة البيانات للتأكد من وجود العمود updated_at"""
    if not APP_DB_CLIENT_OK:
        return False

    try:
        # محاولة تحديث وهمي لاختبار وجود العمود updated_at
        test_response = app_db_client.table('mods').select('id').limit(1).execute()
        if test_response.data:
            # محاولة تحديث وهمي
            test_id = test_response.data[0]['id']
            app_db_client.table('mods').update({'description': 'test'}).eq('id', test_id).execute()
            return True
    except Exception as e:
        if "updated_at" in str(e) and "PGRST204" in str(e):
            return False
        # خطأ آخر، نعتبر أن قاعدة البيانات تعمل
        return True

    return True

def generate_custom_description(mod_data):
    """Generate custom descriptions in English and Arabic using Gemini AI"""
    if not REPAIR_GEMINI_CLIENT_OK or not repair_gemini_model:
        return None, None

    prompt = f"""
أنشئ وصفًا لهذا المود في ماين كرافت باللغتين العربية والإنجليزية، بحيث تكون الإنجليزية باللهجة البريطانية والعربية باللهجة السعودية.

تعرف على اذا كان شادر او تكستر باك اوسكن او مود باك او ماب وألخ وقم بأنشاء وصف بمتطلبات تالية:
1. أن تكون الأوصاف طبيعية وكأن بشري مراهق يحيب ماين كرافت قام بكتابتها
2. كل وصف يجب أن يكون ما بين 300 إلى 400 حرفًا
3. تضمين 2 إلى 3 رموز تعبيرية مناسبة في كل وصف
4. التركيز على الميزات والفوائد الأساسية لمود او شادر او تكستر باك او ماب
5. استخدام لغة غير رسمية تشبه أسلوب البشر (بلهجة بريطانية في الإنجليزية، ولهجة سعودية في العربية)
6. تجنب اللغة الرسمية أو العبارات التي تبدو كأنها من الذكاء الاصطناعي
7. تجنب هاذه كلمات (تخيل) (تجربة لعب) (اضافة) (عش تجربة) (عش مغامرة)
8. استعمل كلمات بلهجة مصرية،سعودية،عراقية احياناً،وبريطانية امريكية لوصف انجليجي
9. اذكر في بداية هاذا مود او شادر او تكتسر باك او ماب او دا اليوم جبتلكم.. او اليوم راح اقدم لكم... جبتلكم... او حبيت اقدم لكم... وكثير وكثير من عبارات اخرى متنوعة
10. لا تستعمل عبارات مثل مرحبا شباب،اليوم سوق اقدم لكم
11. يجب ان يكون وصف بطابع بشري قدر الأمكان

معلومات المود:
اسم المود: {mod_data.get('name', '')}
الفئة: {mod_data.get('category', '')}
الوصف الحالي: {mod_data.get('description', '')}

نسّق ردك على شكل كائن JSON صالح بالهيكل التالي تمامًا:
{{
  "ar": "الوصف العربي هنا مع الإيموجيات",
  "en": "الوصف الإنجليزي هنا مع الإيموجيات"
}}
"""

    try:
        response = smart_repair_gemini_request(prompt)
        if response:
            try:
                result = json.loads(response)
                return result.get('en'), result.get('ar')
            except json.JSONDecodeError:
                print("Failed to parse Gemini response as JSON")
                return None, None
    except Exception as e:
        print(f"Error generating custom descriptions: {e}")
        return None, None

def generate_telegram_custom_description(mod_data):
    """Generate Telegram-specific custom descriptions in English and Arabic using Gemini AI"""
    if not REPAIR_GEMINI_CLIENT_OK:
        update_status("❌ Gemini غير متوفر لإنشاء أوصاف التيليجرام")
        return None

    prompt = TELEGRAM_CUSTOM_DESCRIPTION_PROMPT.format(
        mod_name=mod_data.get('name', ''),
        mod_category=mod_data.get('category', ''),
        existing_english_description=mod_data.get('description', '')
    )

    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            update_status(f"📤 إرسال طلب إنشاء أوصاف التيليجرام (المحاولة {attempt + 1})...")
            response = smart_repair_gemini_request(prompt)

            if response:
                try:
                    # تنظيف الاستجابة من أي نص إضافي
                    response_clean = response.strip()
                    if response_clean.startswith('```json'):
                        response_clean = response_clean[7:]
                    if response_clean.endswith('```'):
                        response_clean = response_clean[:-3]
                    response_clean = response_clean.strip()

                    result = json.loads(response_clean)

                    # فحص جودة الأوصاف المُنشأة
                    english_desc = result.get('en', '')
                    arabic_desc = result.get('ar', '')

                    if english_desc and arabic_desc and is_description_valid(english_desc, arabic_desc):
                        update_status("✅ تم إنشاء أوصاف التيليجرام بنجاح")
                        return result
                    else:
                        update_status(f"⚠️ المحاولة {attempt + 1}: الوصف المُنشأ غير صالح، إعادة المحاولة...")
                        if attempt == max_attempts - 1:
                            update_status("❌ فشل في إنشاء وصف صالح بعد عدة محاولات")
                            return None
                        continue

                except json.JSONDecodeError as e:
                    update_status(f"❌ خطأ في تحليل استجابة Gemini: {e}")
                    print(f"Response was: {response}")
                    if attempt == max_attempts - 1:
                        return None
                    continue
            else:
                update_status(f"⚠️ استجابة فارغة من Gemini (المحاولة {attempt + 1})")
                if attempt == max_attempts - 1:
                    update_status("❌ فشل في الحصول على استجابة من Gemini")
                    return None
                continue

        except Exception as e:
            update_status(f"❌ خطأ في إنشاء أوصاف التيليجرام (المحاولة {attempt + 1}): {e}")
            if attempt == max_attempts - 1:
                return None
            continue

    return None

def is_description_valid(english_desc, arabic_desc):
    """فحص صحة الأوصاف المُنشأة"""

    # التأكد من وجود الأوصاف
    if not english_desc or not arabic_desc:
        update_status("❌ أحد الأوصاف فارغ")
        return False

    # الكلمات المفتاحية التي تدل على وصف معيب
    broken_indicators = [
        "📋 الوصف:",
        "هناك عدة خيارات",
        "الخيار الأول",
        "الخيار الثاني",
        "حسب الجمهور المستهدف",
        "بسيط ومباشر",
        "أكثر تفصيلاً",
        "يمكنك اختيار",
        "Description options:",
        "Option 1:",
        "Option 2:",
        "Choose from:",
        "Here are",
        "several options",
        "different versions",
        "يمكن أن يكون",
        "يمكن استخدام",
        "اختر من بين",
        "إليك خيارات"
    ]

    # فحص الوصف العربي
    for indicator in broken_indicators:
        if indicator in arabic_desc:
            update_status(f"❌ وصف عربي معيب: يحتوي على '{indicator}'")
            return False

    # فحص الوصف الإنجليزي
    for indicator in broken_indicators:
        if indicator in english_desc:
            update_status(f"❌ وصف إنجليزي معيب: يحتوي على '{indicator}'")
            return False

    # فحص طول الأوصاف (تقليل الحد الأدنى)
    if len(arabic_desc.strip()) < 30:
        update_status(f"❌ الوصف العربي قصير جداً ({len(arabic_desc.strip())} حرف)")
        return False

    if len(english_desc.strip()) < 30:
        update_status(f"❌ الوصف الإنجليزي قصير جداً ({len(english_desc.strip())} حرف)")
        return False

    # فحص وجود إيموجيات (اختياري - تحذير فقط)
    import re
    emoji_pattern = re.compile(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\U00002702-\U000027B0\U000024C2-\U0001F251]+')

    if not emoji_pattern.search(arabic_desc) and not emoji_pattern.search(english_desc):
        update_status("⚠️ تحذير: لا توجد إيموجيات في الأوصاف (سيتم قبولها)")

    update_status("✅ الأوصاف صالحة")
    return True

def update_mod_custom_descriptions(mod_id, english_description, arabic_description):
    """Update mod's custom descriptions in the database"""
    try:
        if not APP_DB_CLIENT_OK:
            print("Database client not initialized")
            return False

        result = app_db_client.table(MODS_TABLE_NAME)\
            .update({
                'custom_description_en': english_description,
                'custom_description_ar': arabic_description
            })\
            .eq('id', mod_id)\
            .execute()

        return True if result.data else False
    except Exception as e:
        print(f"Error updating custom descriptions: {e}")
        return False

def update_mod_telegram_descriptions(mod_id, english_description, arabic_description):
    """Update mod's Telegram-specific descriptions in the database"""
    try:
        if not APP_DB_CLIENT_OK:
            update_status("❌ قاعدة البيانات غير متصلة")
            return False

        update_data = {}
        if english_description and english_description.strip():
            update_data['telegram_description_en'] = english_description.strip()
        if arabic_description and arabic_description.strip():
            update_data['telegram_description_ar'] = arabic_description.strip()

        if not update_data:
            update_status("❌ لا توجد أوصاف صالحة للتحديث")
            return False

        update_status(f"🔄 تحديث أوصاف تيليجرام للمود {mod_id}...")

        result = app_db_client.table(MODS_TABLE_NAME)\
            .update(update_data)\
            .eq('id', mod_id)\
            .execute()

        if result.data:
            update_status(f"✅ تم تحديث أوصاف تيليجرام للمود {mod_id}")

            # تحديث البيانات المحلية
            for mod in loaded_mods_data_list:
                if mod['id'] == mod_id:
                    if english_description:
                        mod['telegram_description_en'] = english_description
                    if arabic_description:
                        mod['telegram_description_ar'] = arabic_description
                    break

            return True
        else:
            update_status(f"❌ فشل في تحديث أوصاف تيليجرام للمود {mod_id} - لا توجد بيانات في الاستجابة")
            return False

    except Exception as e:
        error_message = str(e)

        # التعامل مع خطأ العمود المفقود 'updated_at'
        if ("updated_at" in error_message and ("PGRST204" in error_message or "42703" in error_message)) or \
           ('record "new" has no field "updated_at"' in error_message):
            update_status(f"⚠️ تحذير: العمود 'updated_at' غير موجود في قاعدة البيانات. سيتم تجاهل هذا الخطأ.")

            # محاولة التحديث مرة أخرى بدون تحديث الطابع الزمني
            try:
                result = app_db_client.table(MODS_TABLE_NAME)\
                    .update(update_data)\
                    .eq('id', mod_id)\
                    .execute()

                if result.data:
                    update_status(f"✅ تم تحديث أوصاف تيليجرام للمود {mod_id} (تم تجاهل خطأ updated_at)")

                    # تحديث البيانات المحلية
                    for mod in loaded_mods_data_list:
                        if mod['id'] == mod_id:
                            if english_description:
                                mod['telegram_description_en'] = english_description
                            if arabic_description:
                                mod['telegram_description_ar'] = arabic_description
                            break

                    return True
                else:
                    update_status(f"❌ فشل في تحديث أوصاف تيليجرام للمود {mod_id} حتى بعد تجاهل خطأ updated_at")
                    return False

            except Exception as retry_e:
                update_status(f"❌ خطأ في إعادة محاولة تحديث أوصاف تيليجرام للمود {mod_id}: {retry_e}")
                return False
        else:
            # خطأ آخر غير متعلق بـ updated_at
            update_status(f"❌ خطأ في تحديث أوصاف تيليجرام للمود {mod_id}: {e}")
            print(f"Error updating Telegram descriptions: {e}")
            return False

def handle_generate_custom_descriptions():
    """Handle the custom description generation process"""
    global selected_mods_for_repair

    if not selected_mods_for_repair:
        messagebox.showwarning("تحذير", "الرجاء اختيار مود واحد على الأقل")
        return

    if not REPAIR_GEMINI_CLIENT_OK:
        messagebox.showerror("خطأ", "Gemini غير متاح حالياً. الرجاء التحقق من إعدادات Gemini.")
        return

    def generation_task():
        success_count = 0
        fail_count = 0

        for mod in selected_mods_for_repair:
            try:
                update_status(f"جاري إنشاء أوصاف مخصصة للمود: {mod['name']}")
                en_desc, ar_desc = generate_custom_description(mod)

                if en_desc and ar_desc:
                    if update_mod_custom_descriptions(mod['id'], en_desc, ar_desc):
                        update_status(f"✅ تم إنشاء وتحديث الأوصاف المخصصة للمود: {mod['name']}")
                        success_count += 1
                    else:
                        update_status(f"❌ فشل تحديث الأوصاف المخصصة للمود: {mod['name']}")
                        fail_count += 1
                else:
                    update_status(f"❌ فشل إنشاء الأوصاف المخصصة للمود: {mod['name']}")
                    fail_count += 1

                time.sleep(1)  # Prevent rate limiting
            except Exception as e:
                update_status(f"❌ خطأ غير متوقع أثناء معالجة المود {mod['name']}: {str(e)}")
                fail_count += 1

        # عرض ملخص النتائج
        summary = f"اكتملت عملية إنشاء الأوصاف المخصصة\n"
        summary += f"✅ نجح: {success_count}\n"
        summary += f"❌ فشل: {fail_count}"
        messagebox.showinfo("اكتمل", summary)
        update_status("✅ اكتملت عملية إنشاء الأوصاف المخصصة")

    # تشغيل المهمة في خيط منفصل
    thread = threading.Thread(target=generation_task)
    thread.daemon = True
    thread.start()

def handle_generate_telegram_descriptions():
    """Handle the Telegram-specific description generation process"""
    global selected_mods_for_repair

    if not selected_mods_for_repair:
        messagebox.showwarning("تحذير", "الرجاء اختيار مود واحد على الأقل")
        return

    if not REPAIR_GEMINI_CLIENT_OK:
        messagebox.showerror("خطأ", "Gemini غير متاح حالياً. الرجاء التحقق من إعدادات Gemini.")
        return

    def generation_task():
        success_count = 0
        fail_count = 0
        total_mods = len(selected_mods_for_repair)

        update_status(f"🚀 بدء إنشاء أوصاف تليجرام لـ {total_mods} مود...")

        for i, mod in enumerate(selected_mods_for_repair, 1):
            try:
                mod_name = mod.get('name', 'بدون اسم')
                update_status(f"📝 ({i}/{total_mods}) جاري إنشاء أوصاف تليجرام للمود: {mod_name}")

                telegram_descriptions = generate_telegram_custom_description(mod)

                if telegram_descriptions and isinstance(telegram_descriptions, dict):
                    en_desc = telegram_descriptions.get('en', '').strip()
                    ar_desc = telegram_descriptions.get('ar', '').strip()

                    if en_desc and ar_desc:
                        update_status(f"✅ تم إنشاء الأوصاف بنجاح، جاري الحفظ...")
                        if update_mod_telegram_descriptions(mod['id'], en_desc, ar_desc):
                            update_status(f"✅ ({i}/{total_mods}) تم إنشاء وتحديث أوصاف تليجرام للمود: {mod_name}")
                            success_count += 1
                        else:
                            update_status(f"❌ ({i}/{total_mods}) فشل تحديث أوصاف تليجرام للمود: {mod_name}")
                            fail_count += 1
                    else:
                        update_status(f"❌ ({i}/{total_mods}) الأوصاف المُنشأة فارغة للمود: {mod_name}")
                        update_status(f"   الوصف الإنجليزي: {'موجود' if en_desc else 'فارغ'}")
                        update_status(f"   الوصف العربي: {'موجود' if ar_desc else 'فارغ'}")
                        fail_count += 1
                else:
                    update_status(f"❌ ({i}/{total_mods}) فشل إنشاء أوصاف تليجرام للمود: {mod_name}")
                    update_status(f"   نوع الاستجابة: {type(telegram_descriptions)}")
                    fail_count += 1

                # تأخير قصير لتجنب حدود المعدل
                if i < total_mods:  # لا تأخير بعد آخر مود
                    time.sleep(2)

            except Exception as e:
                update_status(f"❌ ({i}/{total_mods}) خطأ غير متوقع أثناء معالجة المود {mod.get('name', 'بدون اسم')}: {str(e)}")
                import traceback
                update_status(f"   تفاصيل الخطأ: {traceback.format_exc()}")
                fail_count += 1

        # عرض ملخص النتائج
        summary = f"اكتملت عملية إنشاء أوصاف تليجرام المخصصة\n"
        summary += f"✅ نجح: {success_count}\n"
        summary += f"❌ فشل: {fail_count}"
        messagebox.showinfo("اكتمل", summary)
        update_status("✅ اكتملت عملية إنشاء أوصاف تليجرام المخصصة")

    # تشغيل المهمة في خيط منفصل
    thread = threading.Thread(target=generation_task)
    thread.daemon = True
    thread.start()

def show_telegram_descriptions():
    """عرض الأوصاف المخصصة للتليجرام للمودات المحددة"""
    global selected_mods_for_repair

    if not selected_mods_for_repair:
        messagebox.showwarning("تحذير", "الرجاء اختيار مود واحد على الأقل")
        return

    # إنشاء نافذة لعرض الأوصاف
    descriptions_window = tk.Toplevel()
    descriptions_window.title("أوصاف التليجرام المخصصة")
    descriptions_window.geometry("900x700")
    descriptions_window.resizable(True, True)

    # إطار رئيسي مع تمرير
    main_frame = ttk.Frame(descriptions_window)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # عنوان
    title_label = ttk.Label(main_frame, text="أوصاف التليجرام المخصصة", font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 10))

    # إنشاء تبويبات للمودات المختلفة
    notebook = ttk.Notebook(main_frame)
    notebook.pack(fill=tk.BOTH, expand=True)

    for mod in selected_mods_for_repair:
        # إنشاء تبويب لكل مود
        mod_frame = ttk.Frame(notebook)
        notebook.add(mod_frame, text=f"{mod.get('name', 'بدون اسم')[:20]}...")

        # معلومات المود
        info_frame = ttk.LabelFrame(mod_frame, text="معلومات المود", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(info_frame, text=f"الاسم: {mod.get('name', 'بدون اسم')}").pack(anchor="w")
        ttk.Label(info_frame, text=f"الفئة: {mod.get('category', 'غير مصنف')}").pack(anchor="w")
        ttk.Label(info_frame, text=f"ID: {mod.get('id', 'N/A')}").pack(anchor="w")

        # الوصف العربي للتليجرام
        ar_frame = ttk.LabelFrame(mod_frame, text="الوصف العربي للتليجرام", padding=10)
        ar_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        ar_text = scrolledtext.ScrolledText(ar_frame, height=8, wrap=tk.WORD, font=("Arial", 10))
        ar_text.pack(fill=tk.BOTH, expand=True)

        telegram_ar = mod.get('telegram_description_ar', '')
        if telegram_ar:
            ar_text.insert(tk.END, telegram_ar)
        else:
            ar_text.insert(tk.END, "لا يوجد وصف عربي للتليجرام")

        # الوصف الإنجليزي للتليجرام
        en_frame = ttk.LabelFrame(mod_frame, text="الوصف الإنجليزي للتليجرام", padding=10)
        en_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        en_text = scrolledtext.ScrolledText(en_frame, height=8, wrap=tk.WORD, font=("Arial", 10))
        en_text.pack(fill=tk.BOTH, expand=True)

        telegram_en = mod.get('telegram_description_en', '')
        if telegram_en:
            en_text.insert(tk.END, telegram_en)
        else:
            en_text.insert(tk.END, "No English Telegram description available")

        # أزرار العمليات
        actions_frame = ttk.Frame(mod_frame)
        actions_frame.pack(fill=tk.X, pady=(10, 0))

        def copy_ar_desc(ar_content=telegram_ar):
            if PYPERCLIP_AVAILABLE and ar_content:
                pyperclip.copy(ar_content)
                messagebox.showinfo("تم النسخ", "تم نسخ الوصف العربي إلى الحافظة")
            else:
                messagebox.showwarning("تحذير", "لا يوجد محتوى للنسخ أو pyperclip غير متوفر")

        def copy_en_desc(en_content=telegram_en):
            if PYPERCLIP_AVAILABLE and en_content:
                pyperclip.copy(en_content)
                messagebox.showinfo("تم النسخ", "تم نسخ الوصف الإنجليزي إلى الحافظة")
            else:
                messagebox.showwarning("تحذير", "لا يوجد محتوى للنسخ أو pyperclip غير متوفر")

        ttk.Button(actions_frame, text="نسخ الوصف العربي", command=copy_ar_desc).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="نسخ الوصف الإنجليزي", command=copy_en_desc).pack(side=tk.LEFT, padx=5)

    # أزرار التحكم السفلية
    bottom_frame = ttk.Frame(main_frame)
    bottom_frame.pack(fill=tk.X, pady=(10, 0))

    ttk.Button(bottom_frame, text="إغلاق", command=descriptions_window.destroy).pack(side=tk.RIGHT)

if __name__ == "__main__":
    # تحميل الإعدادات المحفوظة
    load_repair_config()

    # تكوين Gemini عند بدء التشغيل
    if GEMINI_AVAILABLE and REPAIR_GEMINI_API_KEYS:
        configure_repair_gemini_client()
    elif not REPAIR_GEMINI_API_KEYS and GEMINI_AVAILABLE:
        print("******************************************************************************")
        print("!!! تنبيه هام: لا توجد مفاتيح Gemini API محفوظة !!!")
        print("!!! استخدم نافذة إدارة مفاتيح API لإضافة مفاتيح Gemini لتفعيل ميزات الإصلاح الذكي. !!!")
        print("******************************************************************************")

    # فحص قاعدة البيانات عند بدء التشغيل
    if APP_DB_CLIENT_OK and not check_database_schema():
        print("******************************************************************************")
        print("!!! تحذير: تم اكتشاف مشكلة في قاعدة البيانات (العمود updated_at مفقود) !!!")
        print("!!! استخدم زر 'إصلاح قاعدة البيانات' في الواجهة لحل هذه المشكلة. !!!")
        print("******************************************************************************")

    main_gui()
