#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص نظام تحسين جودة الصور
"""

import os
import json
import requests
from io import BytesIO
from PIL import Image

def check_phosus_api_keys():
    """فحص مفاتيح Phosus API"""
    print("=== فحص مفاتيح Phosus API ===")
    
    # فحص متغيرات البيئة
    auto_enhance_key = os.environ.get("PHOSUS_AUTO_ENHANCE_API_KEY", "")
    super_res_key = os.environ.get("PHOSUS_SUPER_RES_API_KEY", "")
    
    print(f"🔑 PHOSUS_AUTO_ENHANCE_API_KEY: {'✅ موجود' if auto_enhance_key else '❌ غير موجود'}")
    print(f"🔑 PHOSUS_SUPER_RES_API_KEY: {'✅ موجود' if super_res_key else '❌ غير موجود'}")
    
    # فحص ملف الإعدادات
    try:
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config_auto_key = config.get("PHOSUS_AUTO_ENHANCE_API_KEY", "")
        config_super_key = config.get("PHOSUS_SUPER_RES_API_KEY", "")
        
        print(f"📄 من api_keys.json - Auto Enhance: {'✅ موجود' if config_auto_key else '❌ غير موجود'}")
        print(f"📄 من api_keys.json - Super Resolution: {'✅ موجود' if config_super_key else '❌ غير موجود'}")
        
        return {
            'auto_enhance': auto_enhance_key or config_auto_key,
            'super_resolution': super_res_key or config_super_key
        }
        
    except FileNotFoundError:
        print("❌ ملف api_keys.json غير موجود")
        return {
            'auto_enhance': auto_enhance_key,
            'super_resolution': super_res_key
        }
    except Exception as e:
        print(f"❌ خطأ في قراءة api_keys.json: {e}")
        return {
            'auto_enhance': auto_enhance_key,
            'super_resolution': super_res_key
        }

def test_phosus_api_connection(api_keys):
    """اختبار الاتصال بـ Phosus API"""
    print("\n=== اختبار الاتصال بـ Phosus API ===")
    
    # إنشاء صورة اختبار صغيرة
    test_image = Image.new('RGB', (100, 100), color='red')
    test_buffer = BytesIO()
    test_image.save(test_buffer, format='JPEG')
    test_image_bytes = test_buffer.getvalue()
    
    endpoints = {
        'auto_enhance': "https://api.phosus.com/v1/enhancement/auto",
        'super_resolution': "https://api.phosus.com/v1/enhancement/super-resolution"
    }
    
    results = {}
    
    for enhancement_type, endpoint in endpoints.items():
        api_key = api_keys.get(enhancement_type)
        
        if not api_key:
            print(f"⚠️ {enhancement_type}: لا يوجد مفتاح API")
            results[enhancement_type] = {'status': 'no_key', 'message': 'لا يوجد مفتاح API'}
            continue
        
        print(f"🔄 اختبار {enhancement_type}...")
        
        try:
            headers = {'X-Phosus-Key': api_key}
            files = {'image': test_image_bytes}
            
            # محاولة الاتصال مع timeout قصير
            response = requests.post(endpoint, files=files, headers=headers, timeout=10)
            
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '').lower()
                if content_type.startswith('image/'):
                    print(f"✅ {enhancement_type}: يعمل بشكل صحيح")
                    results[enhancement_type] = {'status': 'working', 'message': 'يعمل بشكل صحيح'}
                else:
                    print(f"⚠️ {enhancement_type}: استجابة غير متوقعة (ليست صورة)")
                    results[enhancement_type] = {'status': 'invalid_response', 'message': 'استجابة غير متوقعة'}
            elif response.status_code == 401:
                print(f"❌ {enhancement_type}: مفتاح API غير صحيح")
                results[enhancement_type] = {'status': 'invalid_key', 'message': 'مفتاح API غير صحيح'}
            elif response.status_code == 403:
                print(f"❌ {enhancement_type}: ممنوع - تحقق من الصلاحيات")
                results[enhancement_type] = {'status': 'forbidden', 'message': 'ممنوع - تحقق من الصلاحيات'}
            else:
                print(f"❌ {enhancement_type}: خطأ HTTP {response.status_code}")
                results[enhancement_type] = {'status': 'http_error', 'message': f'خطأ HTTP {response.status_code}'}
                
        except requests.exceptions.Timeout:
            print(f"⏰ {enhancement_type}: انتهت مهلة الاتصال")
            results[enhancement_type] = {'status': 'timeout', 'message': 'انتهت مهلة الاتصال'}
        except requests.exceptions.ConnectionError:
            print(f"🌐 {enhancement_type}: خطأ في الاتصال")
            results[enhancement_type] = {'status': 'connection_error', 'message': 'خطأ في الاتصال'}
        except Exception as e:
            print(f"❌ {enhancement_type}: خطأ غير متوقع - {e}")
            results[enhancement_type] = {'status': 'error', 'message': str(e)}
    
    return results

def check_image_processing_system():
    """فحص نظام معالجة الصور"""
    print("\n=== فحص نظام معالجة الصور ===")
    
    try:
        from PIL import Image
        print("✅ PIL (Pillow) متوفر")
        
        # اختبار إنشاء صورة
        test_img = Image.new('RGB', (100, 100), color='blue')
        print("✅ إنشاء الصور يعمل")
        
        # اختبار ضغط الصور
        buffer = BytesIO()
        test_img.save(buffer, format='JPEG', quality=85)
        compressed_size = len(buffer.getvalue())
        print(f"✅ ضغط الصور يعمل (حجم الاختبار: {compressed_size} بايت)")
        
        # اختبار تغيير الحجم
        resized_img = test_img.resize((50, 50))
        print("✅ تغيير حجم الصور يعمل")
        
        return True
        
    except ImportError:
        print("❌ PIL (Pillow) غير متوفر")
        return False
    except Exception as e:
        print(f"❌ خطأ في نظام معالجة الصور: {e}")
        return False

def generate_enhancement_report():
    """إنشاء تقرير شامل عن نظام تحسين الصور"""
    print("=" * 60)
    print("تقرير تشخيص نظام تحسين جودة الصور")
    print("=" * 60)
    
    # فحص مفاتيح API
    api_keys = check_phosus_api_keys()
    
    # فحص نظام معالجة الصور
    image_system_ok = check_image_processing_system()
    
    # اختبار Phosus API
    api_results = test_phosus_api_connection(api_keys)
    
    # إنشاء التقرير النهائي
    print("\n" + "=" * 60)
    print("ملخص التقرير")
    print("=" * 60)
    
    print(f"🖼️ نظام معالجة الصور: {'✅ يعمل' if image_system_ok else '❌ لا يعمل'}")
    
    working_apis = sum(1 for result in api_results.values() if result['status'] == 'working')
    total_apis = len(api_results)
    print(f"🔧 Phosus APIs: {working_apis}/{total_apis} يعمل")
    
    for api_name, result in api_results.items():
        status_icon = "✅" if result['status'] == 'working' else "❌"
        print(f"   {status_icon} {api_name}: {result['message']}")
    
    # توصيات
    print("\n📋 التوصيات:")
    
    if not image_system_ok:
        print("   • قم بتثبيت Pillow: pip install Pillow")
    
    if not any(result['status'] == 'working' for result in api_results.values()):
        print("   • احصل على مفاتيح Phosus API صحيحة")
        print("   • أضف المفاتيح إلى api_keys.json أو متغيرات البيئة")
        print("   • تحقق من اتصال الإنترنت")
    
    if working_apis > 0:
        print("   ✅ نظام تحسين الصور جاهز للاستخدام!")
    
    return {
        'image_system': image_system_ok,
        'api_results': api_results,
        'working_apis': working_apis,
        'total_apis': total_apis
    }

if __name__ == "__main__":
    report = generate_enhancement_report()
    
    # حفظ التقرير في ملف
    try:
        with open('image_enhancement_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"\n💾 تم حفظ التقرير في: image_enhancement_report.json")
    except Exception as e:
        print(f"\n⚠️ لم يتم حفظ التقرير: {e}")
