#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة لإنشاء buckets في Supabase باستخدام service key
"""

import json
import os
from supabase import create_client, Client

def create_buckets_with_service_key():
    """إنشاء buckets باستخدام service key"""
    
    # تحميل الإعدادات
    try:
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ ملف api_keys.json غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الإعدادات: {e}")
        return False
    
    # الحصول على معلومات Supabase
    supabase_url = "https://ytqxxodyecdeosnqoure.supabase.co"
    service_key = config.get("SUPABASE_SERVICE_KEY", "")
    
    if not service_key:
        print("❌ مفتاح SUPABASE_SERVICE_KEY غير موجود في الإعدادات")
        print("💡 تحتاج إلى service key لإنشاء buckets")
        print("🔗 يمكنك الحصول عليه من: https://supabase.com/dashboard/project/[project-id]/settings/api")
        return False
    
    try:
        # إنشاء عميل Supabase بصلاحيات admin
        print("🔄 إنشاء عميل Supabase بصلاحيات admin...")
        supabase: Client = create_client(supabase_url, service_key)
        
        # اختبار الاتصال أولاً
        print("🔄 اختبار الاتصال...")
        try:
            buckets = supabase.storage.list_buckets()
            print(f"✅ نجح الاتصال. عدد buckets الموجودة: {len(buckets)}")
            
            # طباعة أسماء buckets الموجودة
            if buckets:
                bucket_names = [bucket['name'] for bucket in buckets]
                print(f"📋 Buckets الموجودة: {bucket_names}")
            
        except Exception as conn_e:
            print(f"❌ فشل الاتصال: {conn_e}")
            return False
        
        # قائمة buckets المطلوبة
        required_buckets = [
            {'id': 'mods', 'name': 'mods', 'public': True},
            {'id': 'images', 'name': 'images', 'public': True}
        ]
        
        existing_bucket_names = [bucket['name'] for bucket in buckets]
        
        for bucket_config in required_buckets:
            bucket_name = bucket_config['id']
            
            if bucket_name not in existing_bucket_names:
                print(f"🔄 إنشاء bucket: {bucket_name}")
                try:
                    result = supabase.storage.create_bucket(
                        id=bucket_config['id'],
                        name=bucket_config['name'],
                        options={'public': bucket_config['public']}
                    )
                    print(f"✅ تم إنشاء bucket بنجاح: {bucket_name}")
                    print(f"📋 نتيجة الإنشاء: {result}")
                    
                except Exception as create_e:
                    print(f"❌ فشل إنشاء bucket {bucket_name}: {create_e}")
                    
                    # إذا كانت المشكلة في الصلاحيات، اعطي تعليمات
                    if "Unauthorized" in str(create_e) or "403" in str(create_e):
                        print("💡 يبدو أن المفتاح المستخدم لا يملك صلاحيات إنشاء buckets")
                        print("🔧 يرجى إنشاء buckets يدوياً من لوحة تحكم Supabase:")
                        print(f"   1. اذهب إلى: {supabase_url}/project/default/storage/buckets")
                        print(f"   2. انقر على 'New bucket'")
                        print(f"   3. أدخل اسم bucket: {bucket_name}")
                        print(f"   4. فعل 'Public bucket' إذا كنت تريد الوصول العام")
                        print(f"   5. انقر على 'Create bucket'")
                        
            else:
                print(f"✅ bucket موجود مسبقاً: {bucket_name}")
        
        # التحقق النهائي من buckets
        print("🔄 التحقق النهائي من buckets...")
        final_buckets = supabase.storage.list_buckets()
        final_bucket_names = [bucket['name'] for bucket in final_buckets]
        print(f"📋 Buckets النهائية: {final_bucket_names}")
        
        # التحقق من وجود buckets المطلوبة
        missing_buckets = []
        for bucket_config in required_buckets:
            if bucket_config['id'] not in final_bucket_names:
                missing_buckets.append(bucket_config['id'])
        
        if missing_buckets:
            print(f"⚠️ buckets مفقودة: {missing_buckets}")
            print("💡 يرجى إنشاؤها يدوياً من لوحة تحكم Supabase")
            return False
        else:
            print("✅ جميع buckets المطلوبة موجودة!")
            return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def create_buckets_manually_instructions():
    """طباعة تعليمات إنشاء buckets يدوياً"""
    print("\n" + "="*60)
    print("📋 تعليمات إنشاء Buckets يدوياً في Supabase")
    print("="*60)
    print("1. اذهب إلى لوحة تحكم Supabase:")
    print("   https://ytqxxodyecdeosnqoure.supabase.co/project/default/storage/buckets")
    print("\n2. انقر على زر 'New bucket'")
    print("\n3. أنشئ bucket للمودات:")
    print("   - Name: mods")
    print("   - ✅ فعل 'Public bucket'")
    print("   - انقر 'Create bucket'")
    print("\n4. أنشئ bucket للصور:")
    print("   - Name: images") 
    print("   - ✅ فعل 'Public bucket'")
    print("   - انقر 'Create bucket'")
    print("\n5. تأكد من أن كلا bucket مُنشأ ومرئي في القائمة")
    print("="*60)

if __name__ == "__main__":
    print("=== إنشاء Buckets في Supabase ===")
    success = create_buckets_with_service_key()
    
    if not success:
        create_buckets_manually_instructions()
    else:
        print("✅ تم إنشاء جميع buckets بنجاح!")
        print("🎉 يمكنك الآن استخدام التطبيق لرفع الملفات")
