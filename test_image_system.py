#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لنظام معالجة الصور
"""

import json
import os

def test_basic_image_system():
    """اختبار أساسي لنظام الصور"""
    print("=== اختبار نظام معالجة الصور ===")
    
    try:
        from PIL import Image
        from io import BytesIO
        print("✅ PIL (Pillow) متوفر")
        
        # إنشاء صورة اختبار
        test_img = Image.new('RGB', (100, 100), color='red')
        print("✅ إنشاء الصور يعمل")
        
        # اختبار الضغط
        buffer = BytesIO()
        test_img.save(buffer, format='JPEG', quality=85)
        size = len(buffer.getvalue())
        print(f"✅ ضغط JPEG يعمل (حجم: {size} بايت)")
        
        # اختبار PNG
        buffer2 = BytesIO()
        test_img.save(buffer2, format='PNG')
        size2 = len(buffer2.getvalue())
        print(f"✅ حفظ PNG يعمل (حجم: {size2} بايت)")
        
        # اختبار تغيير الحجم
        resized = test_img.resize((50, 50))
        print("✅ تغيير الحجم يعمل")
        
        return True
        
    except ImportError as e:
        print(f"❌ PIL غير متوفر: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def check_api_keys():
    """فحص مفاتيح API"""
    print("\n=== فحص مفاتيح API ===")
    
    try:
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        phosus_auto = config.get("PHOSUS_AUTO_ENHANCE_API_KEY", "")
        phosus_super = config.get("PHOSUS_SUPER_RES_API_KEY", "")
        
        print(f"🔑 PHOSUS_AUTO_ENHANCE_API_KEY: {'✅ موجود' if phosus_auto else '❌ غير موجود'}")
        print(f"🔑 PHOSUS_SUPER_RES_API_KEY: {'✅ موجود' if phosus_super else '❌ غير موجود'}")
        
        if not phosus_auto and not phosus_super:
            print("💡 لاستخدام تحسين الصور، أضف مفاتيح Phosus API إلى api_keys.json")
            
        return phosus_auto or phosus_super
        
    except FileNotFoundError:
        print("❌ ملف api_keys.json غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def main():
    print("تشخيص نظام تحسين جودة الصور")
    print("=" * 40)
    
    # اختبار نظام الصور الأساسي
    image_ok = test_basic_image_system()
    
    # فحص مفاتيح API
    api_ok = check_api_keys()
    
    print("\n" + "=" * 40)
    print("النتيجة النهائية:")
    print("=" * 40)
    
    if image_ok:
        print("✅ نظام معالجة الصور يعمل بشكل صحيح")
        print("   - يمكن ضغط الصور")
        print("   - يمكن تغيير أحجام الصور")
        print("   - يمكن تحويل تنسيقات الصور")
    else:
        print("❌ نظام معالجة الصور لا يعمل")
        print("   - قم بتثبيت Pillow: pip install Pillow")
    
    if api_ok:
        print("✅ مفاتيح Phosus API متوفرة")
        print("   - يمكن استخدام تحسين الجودة")
    else:
        print("⚠️ مفاتيح Phosus API غير متوفرة")
        print("   - تحسين الجودة غير متاح حالياً")
        print("   - الضغط العادي متاح")
    
    print("\n📋 الميزات المتاحة:")
    print("   ✅ ضغط الصور (عادي)")
    print("   ✅ تغيير أحجام الصور")
    print("   ✅ تحويل تنسيقات الصور")
    if api_ok:
        print("   ✅ تحسين جودة الصور (Phosus)")
    else:
        print("   ❌ تحسين جودة الصور (يحتاج مفاتيح API)")

if __name__ == "__main__":
    main()
