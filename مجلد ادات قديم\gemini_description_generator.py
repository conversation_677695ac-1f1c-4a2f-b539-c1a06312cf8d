# -*- coding: utf-8 -*-
"""
مولد الوصف بالعربية والإنجليزية باستخدام Gemini
"""

import google.generativeai as genai
from typing import Dict, Optional, Tuple
import json
import re

class GeminiDescriptionGenerator:
    """مولد الوصف باستخدام Gemini AI"""

    def __init__(self, api_key: str):
        """تهيئة Gemini"""
        try:
            genai.configure(api_key=api_key)
            # استخدام النموذج الجديد
            self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
            print("✅ تم تهيئة Gemini بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تهيئة Gemini: {e}")
            # محاولة النموذج البديل
            try:
                self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
                print("✅ تم تهيئة Gemini بالنموذج البديل")
            except Exception as e2:
                print(f"❌ خطأ في النموذج البديل: {e2}")
                self.model = None

    def generate_descriptions(self, mod_data: Dict, extracted_content: str) -> Tuple[str, str]:
        """إنشاء وصف بالإنجليزية والعربية"""
        if not self.model:
            return self.get_fallback_descriptions(mod_data)

        try:
            # إنشاء الوصف الإنجليزي
            english_desc = self.generate_english_description(mod_data, extracted_content)

            # إنشاء الوصف العربي
            arabic_desc = self.generate_arabic_description(mod_data, extracted_content, english_desc)

            return english_desc, arabic_desc

        except Exception as e:
            print(f"❌ خطأ في إنشاء الوصف: {e}")
            return self.get_fallback_descriptions(mod_data)

    def generate_english_description(self, mod_data: Dict, content: str) -> str:
        """إنشاء وصف إنجليزي مفصل"""

        prompt = f"""
Create a detailed, engaging English description for this Minecraft addon/mod:

**Mod Name:** {mod_data.get('name', 'Unknown')}
**Category:** {mod_data.get('category', 'Addon')}
**Creator:** {mod_data.get('creator_name', 'Unknown')}
**Version:** {mod_data.get('version', '1.21+')}
**Size:** {mod_data.get('size', 'Unknown')}

**Extracted Content from Page:**
{content[:3000]}

**Instructions:**
1. Write a compelling, detailed description (300-500 words) in line-by-line format
2. Each sentence should be on a separate line
3. Avoid long connected paragraphs
4. Analyze the extracted content to identify specific features and benefits
5. Focus on what makes this mod unique and exciting
6. Use engaging language that appeals to Minecraft players
7. Mention gameplay improvements and visual enhancements
8. Add compatibility information and installation ease
9. Make it sound professional yet exciting

**Required Format:**
- Write each sentence on a separate line
- Start with an engaging sentence about what this mod brings to Minecraft
- Describe the main features and benefits in separate sentences
- Mention technical details in separate sentences
- End with a strong call to action

**Example Format:**
The Dragon Mounts Addon transforms your Minecraft world by adding rideable dragons with unique abilities.
Each dragon type offers different powers and characteristics for diverse gameplay experiences.
The Cherry Dragon provides healing abilities while the Phantom Dragon offers stealth capabilities.
Compatible with Minecraft 1.21+ and designed for smooth performance across all devices.
Easy installation process requires no additional tools or complex setup procedures.

**Style Guidelines:**
- Use active voice and exciting adjectives
- Focus on player benefits and experiences
- Mention specific Minecraft elements (blocks, items, mobs, etc.)
- Include words like "enhance", "transform", "revolutionize", "incredible"
- Make it sound like a professional game description
- Write each complete thought as a separate line

Generate a detailed description:
"""

        try:
            response = self.model.generate_content(prompt)
            description = response.text.strip()

            # تنظيف الوصف
            description = self.clean_description(description)

            print(f"✅ تم إنشاء الوصف الإنجليزي: {len(description)} حرف")
            return description

        except Exception as e:
            print(f"❌ خطأ في إنشاء الوصف الإنجليزي: {e}")
            return self.get_fallback_english_description(mod_data)

    def generate_arabic_description(self, mod_data: Dict, content: str, english_desc: str) -> str:
        """إنشاء وصف عربي مفصل"""

        prompt = f"""
أنشئ وصفاً عربياً مفصلاً وجذاباً لهذا المود/الإضافة في ماين كرافت:

**اسم المود:** {mod_data.get('name', 'غير معروف')}
**الفئة:** {mod_data.get('category', 'إضافة')}
**المطور:** {mod_data.get('creator_name', 'غير معروف')}
**الإصدار:** {mod_data.get('version', '1.21+')}
**الحجم:** {mod_data.get('size', 'غير معروف')}

**الوصف الإنجليزي المرجعي:**
{english_desc[:1000]}

**المحتوى المستخرج من الصفحة:**
{content[:2000]}

**التعليمات:**
1. اكتب وصفاً مفصلاً وجذاباً باللغة العربية (300-500 كلمة) بتنسيق سطر تلو الآخر
2. كل جملة يجب أن تكون في سطر منفصل
3. تجنب الفقرات الطويلة المتصلة
4. حلل المحتوى المستخرج لتحديد المميزات والفوائد المحددة
5. ركز على ما يجعل هذا المود فريداً ومثيراً
6. استخدم لغة شيقة ومهنية تجذب لاعبي ماين كرافت العرب
7. اذكر تحسينات اللعب والتحسينات البصرية
8. أضف معلومات التوافق وسهولة التثبيت
9. اجعله يبدو مهنياً ومثيراً في نفس الوقت

**التنسيق المطلوب:**
- اكتب كل جملة في سطر منفصل
- ابدأ بجملة جذابة حول ما يجلبه هذا المود لماين كرافت
- اوصف المميزات والفوائد الرئيسية في جمل منفصلة
- اذكر التفاصيل التقنية في جمل منفصلة
- اختتم بدعوة قوية للعمل

**مثال على التنسيق:**
إضافة التنانين تحول عالم ماين كرافت من خلال إضافة تنانين قابلة للركوب مع قدرات فريدة.
كل نوع تنين يوفر قوى وخصائص مختلفة لتجارب لعب متنوعة.
تنين الكرز يوفر قدرات الشفاء بينما تنين الشبح يوفر قدرات التخفي.
متوافق مع ماين كرافت 1.21+ ومصمم للأداء السلس عبر جميع الأجهزة.
عملية تثبيت سهلة لا تتطلب أدوات إضافية أو إجراءات إعداد معقدة.

**إرشادات الأسلوب:**
- استخدم الصوت النشط والصفات المثيرة
- ركز على فوائد وتجارب اللاعب
- اذكر عناصر ماين كرافت المحددة (الكتل، العناصر، الوحوش، إلخ)
- استخدم كلمات مثل "عزز"، "حوّل"، "أحدث ثورة"، "رائع"
- اجعله يبدو كوصف لعبة مهني
- اكتب كل فكرة كاملة في سطر منفصل

أنشئ وصفاً مفصلاً:
"""

        try:
            response = self.model.generate_content(prompt)
            description = response.text.strip()

            # تنظيف الوصف
            description = self.clean_arabic_description(description)

            print(f"✅ تم إنشاء الوصف العربي: {len(description)} حرف")
            return description

        except Exception as e:
            print(f"❌ خطأ في إنشاء الوصف العربي: {e}")
            return self.get_fallback_arabic_description(mod_data)

    def clean_description(self, description: str) -> str:
        """تنظيف الوصف الإنجليزي"""
        # إزالة النصوص غير المرغوبة
        unwanted_patterns = [
            r'\*\*[^*]+\*\*',  # Bold markdown
            r'Generate the description:',
            r'Description:',
            r'Here\'s the description:',
            r'Here is the description:'
        ]

        for pattern in unwanted_patterns:
            description = re.sub(pattern, '', description, flags=re.IGNORECASE)

        # تنظيف المسافات
        description = re.sub(r'\n\s*\n', '\n\n', description)
        description = description.strip()

        return description

    def clean_arabic_description(self, description: str) -> str:
        """تنظيف الوصف العربي"""
        # إزالة النصوص غير المرغوبة
        unwanted_patterns = [
            r'أنشئ الوصف:',
            r'الوصف:',
            r'إليك الوصف:',
            r'هذا هو الوصف:'
        ]

        for pattern in unwanted_patterns:
            description = re.sub(pattern, '', description, flags=re.IGNORECASE)

        # تنظيف المسافات
        description = re.sub(r'\n\s*\n', '\n\n', description)
        description = description.strip()

        return description

    def get_fallback_descriptions(self, mod_data: Dict) -> Tuple[str, str]:
        """أوصاف احتياطية في حالة فشل Gemini"""
        english = self.get_fallback_english_description(mod_data)
        arabic = self.get_fallback_arabic_description(mod_data)
        return english, arabic

    def get_fallback_english_description(self, mod_data: Dict) -> str:
        """وصف إنجليزي احتياطي"""
        name = mod_data.get('name', 'This Minecraft Addon')
        category = mod_data.get('category', 'addon')
        version = mod_data.get('version', '1.21+')

        return f"""Enhance your Minecraft experience with {name}!
This amazing {category.lower()} brings exciting new features and content to your world.
Features new gameplay mechanics and content that transform your gaming experience.
Includes high-quality textures and models designed for visual excellence.
Offers smooth performance and compatibility across different devices.
Easy installation and setup process requires no technical expertise.
Perfect for players looking to expand their Minecraft adventure with fresh content.
Compatible with Minecraft {version} and designed for optimal performance.
Download now and discover what makes this {category.lower()} special!"""

    def get_fallback_arabic_description(self, mod_data: Dict) -> str:
        """وصف عربي احتياطي"""
        name = mod_data.get('name', 'هذا المود')
        category = mod_data.get('category', 'إضافة')
        version = mod_data.get('version', '1.21+')

        return f"""عزز تجربة ماين كرافت الخاصة بك مع {name}!
هذه {category} الرائعة تجلب مميزات ومحتوى جديد ومثير إلى عالمك.
يتضمن آليات لعب ومحتوى جديد يحول تجربة اللعب الخاصة بك.
يشمل تكسشرز ونماذج عالية الجودة مصممة للتميز البصري.
يوفر أداء سلس وتوافق ممتاز عبر الأجهزة المختلفة.
عملية تثبيت وإعداد سهلة لا تتطلب خبرة تقنية.
مثالية للاعبين الذين يبحثون عن توسيع مغامرة ماين كرافت مع محتوى جديد.
متوافقة مع ماين كرافت {version} ومصممة للأداء الأمثل.
حمّل الآن واكتشف ما يجعل هذه {category} مميزة!"""

def test_gemini_generator():
    """اختبار مولد الوصف"""
    print("🧪 اختبار مولد الوصف...")

    # بيانات تجريبية
    test_mod_data = {
        'name': 'Dragon Mounts',
        'category': 'Addons',
        'creator_name': 'Tomanex',
        'version': '1.21.81'
    }

    test_content = """
    Dragon Mounts Community Edition brings dragons to your Minecraft world.
    Features include Cherry Dragon and Phantom Dragon with unique abilities.
    Compatible with latest Minecraft versions.
    """

    # محاولة استخدام مفتاح API (يجب توفيره)
    try:
        # يجب استبدال هذا بمفتاح API حقيقي
        generator = GeminiDescriptionGenerator("your-api-key-here")

        if generator.model:
            english, arabic = generator.generate_descriptions(test_mod_data, test_content)
            print(f"✅ الوصف الإنجليزي: {english[:100]}...")
            print(f"✅ الوصف العربي: {arabic[:100]}...")
        else:
            print("⚠️ لم يتم تهيئة Gemini - استخدام الأوصاف الاحتياطية")
            english, arabic = generator.get_fallback_descriptions(test_mod_data)
            print(f"✅ الوصف الاحتياطي الإنجليزي: {english[:100]}...")
            print(f"✅ الوصف الاحتياطي العربي: {arabic[:100]}...")

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_gemini_generator()
