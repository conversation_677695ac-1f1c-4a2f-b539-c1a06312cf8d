# -*- coding: utf-8 -*-
"""
ملف اختبار للتأكد من أن الإصلاحات الجديدة تعمل بشكل صحيح
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_categories():
    """اختبار التصنيفات الجديدة"""
    print("🧪 اختبار التصنيفات الجديدة...")
    
    try:
        # استيراد التصنيفات من الملف الرئيسي
        from mod_processor_broken_final import CATEGORIES, map_mcpedl_category_to_app_category
        
        print(f"✅ التصنيفات المتاحة: {CATEGORIES}")
        
        # التحقق من وجود التصنيفات الجديدة
        expected_categories = ["Addons", "Shaders", "Texture Pack", "Maps", "Skins", "Seeds"]
        
        for category in expected_categories:
            if category in CATEGORIES:
                print(f"✅ التصنيف '{category}' موجود")
            else:
                print(f"❌ التصنيف '{category}' مفقود!")
                return False
        
        # اختبار دالة تحويل التصنيفات
        test_cases = [
            ("map", "Maps"),
            ("world", "Maps"),
            ("skin", "Skins"),
            ("character", "Skins"),
            ("seed", "Seeds"),
            ("addon", "Addons"),
            ("shader", "Shaders"),
            ("texture", "Texture Pack"),
            ("unknown", "Addons")  # افتراضي
        ]
        
        print("\n🧪 اختبار دالة تحويل التصنيفات...")
        for input_category, expected_output in test_cases:
            result = map_mcpedl_category_to_app_category(input_category)
            if result == expected_output:
                print(f"✅ '{input_category}' -> '{result}'")
            else:
                print(f"❌ '{input_category}' -> '{result}' (متوقع: '{expected_output}')")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التصنيفات: {e}")
        return False

def test_image_urls_structure():
    """اختبار بنية روابط الصور الجديدة"""
    print("\n🧪 اختبار بنية روابط الصور...")
    
    try:
        # محاكاة بيانات النشر
        test_image_urls = [
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg", 
            "https://example.com/image3.jpg"
        ]
        
        # محاكاة منطق النشر الجديد
        final_image_urls = test_image_urls[:10] if test_image_urls else []
        
        publish_data = {
            "name": "Test Mod",
            "description": "Test Description",
            "category": "Addons",
            "image_urls": final_image_urls if final_image_urls else [],
        }
        
        # التحقق من البنية
        if "image_urls" in publish_data:
            print(f"✅ عمود image_urls موجود: {len(publish_data['image_urls'])} صورة")
        else:
            print("❌ عمود image_urls مفقود!")
            return False
            
        # التحقق من عدم وجود additional_image_urls
        if "additional_image_urls" not in publish_data:
            print("✅ لا يوجد اعتماد على additional_image_urls")
        else:
            print("❌ ما زال هناك اعتماد على additional_image_urls!")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار بنية الصور: {e}")
        return False

def test_database_compatibility():
    """اختبار التوافق مع قاعدة البيانات"""
    print("\n🧪 اختبار التوافق مع قاعدة البيانات...")
    
    try:
        # محاكاة بيانات المود للنشر
        mod_data = {
            "name": "Test Mod",
            "description": "Test Description", 
            "description_ar": "وصف تجريبي",
            "category": "Maps",  # تصنيف جديد
            "version": "1.0.0",
            "size": "5 MB",
            "download_url": "https://example.com/mod.mcpack",
            "image_urls": [
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg"
            ],
            "creator_name": "Test Creator",
            "creator_contact_info": "<EMAIL>"
        }
        
        # التحقق من جميع الحقول المطلوبة
        required_fields = ["name", "category", "image_urls"]
        for field in required_fields:
            if field in mod_data:
                print(f"✅ الحقل '{field}' موجود")
            else:
                print(f"❌ الحقل '{field}' مفقود!")
                return False
        
        # التحقق من صحة التصنيف
        if mod_data["category"] in ["Addons", "Shaders", "Texture Pack", "Maps", "Skins", "Seeds"]:
            print(f"✅ التصنيف '{mod_data['category']}' صحيح")
        else:
            print(f"❌ التصنيف '{mod_data['category']}' غير صحيح!")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التوافق مع قاعدة البيانات: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار الإصلاحات الجديدة...\n")
    
    tests = [
        ("اختبار التصنيفات الجديدة", test_categories),
        ("اختبار بنية روابط الصور", test_image_urls_structure),
        ("اختبار التوافق مع قاعدة البيانات", test_database_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"📋 {test_name}...")
        if test_func():
            print(f"✅ {test_name} نجح!\n")
            passed += 1
        else:
            print(f"❌ {test_name} فشل!\n")
    
    print("=" * 50)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! الإصلاحات تعمل بشكل صحيح.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
