# -*- coding: utf-8 -*-
import os
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog # Keep simpledialog for new extension input
import threading
import time
import json
import zipfile
from io import BytesIO
import requests
import webbrowser
from supabase import create_client, Client
# from supabase.lib.client_options import StorageException # For specific Supabase storage errors -> Removed due to import issues
import re
import shutil
from urllib.parse import urlparse
from PIL import Image, ImageTk

# --- NEW: Gemini Import ---
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("Warning: google-generativeai library not found. AI repair features will be disabled.")
    print("Install it using: pip install google-generativeai")

try:
    import pyperclip
    PYPERCLIP_AVAILABLE = True
except ImportError:
    PYPERCLIP_AVAILABLE = False
    print("Warning: pyperclip library not found. Copy log button will be disabled.")
    print("Install it using: pip install pyperclip")


# --- Supabase Configurations ---
STORAGE_URL = "https://mwxzwfeqsashcwvqthmd.supabase.co"
STORAGE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im13eHp3ZmVxc2FzaGN3dnF0aG1kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MzU2NDcsImV4cCI6MjA2MTQxMTY0N30.nU0smAgNsoLi1zRNKA3AFM3q112jp4fhPgYeeXqKmPU"
MOD_BUCKET = "my_new_mods_bucket"
APP_DB_URL = 'https://ytqxxodyecdeosnqoure.supabase.co'
APP_DB_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4'
MODS_TABLE_NAME = 'mods'

# --- Initialize Supabase Clients ---
storage_client: Client | None = None
app_db_client: Client | None = None
STORAGE_CLIENT_OK = False
APP_DB_CLIENT_OK = False

try:
    if STORAGE_URL and STORAGE_KEY:
        storage_client = create_client(STORAGE_URL, STORAGE_KEY)
        print("Supabase storage client initialized for repair tool.")
        STORAGE_CLIENT_OK = True
except Exception as e:
    print(f"Error initializing Supabase storage client for repair tool: {e}")

try:
    if APP_DB_URL and APP_DB_KEY:
        app_db_client = create_client(APP_DB_URL, APP_DB_KEY)
        print("Supabase App DB client initialized for repair tool.")
        APP_DB_CLIENT_OK = True
except Exception as e:
    print(f"Error initializing Supabase App DB client for repair tool: {e}")

# --- Gemini Configuration for Repair Tool ---
REPAIR_GEMINI_API_KEY = "AIzaSyBY58cZFvFzRQpzzYJ7m1VjwvS7af-vHhM" # الرجاء وضع مفتاح API الخاص بك هنا
REPAIR_GEMINI_MODEL_NAME = "models/gemini-2.0-flash-exp" # أو "models/gemini-pro"
repair_gemini_model = None
REPAIR_GEMINI_CLIENT_OK = False

def configure_repair_gemini_client():
    global repair_gemini_model, REPAIR_GEMINI_CLIENT_OK
    if not GEMINI_AVAILABLE:
        REPAIR_GEMINI_CLIENT_OK = False
        return False
    if not REPAIR_GEMINI_API_KEY or REPAIR_GEMINI_API_KEY == "YOUR_GEMINI_API_KEY":
        update_status("!!! تحذير: لم يتم تكوين مفتاح Gemini API. ميزات الإصلاح الذكي معطلة.")
        REPAIR_GEMINI_CLIENT_OK = False
        return False
    try:
        genai.configure(api_key=REPAIR_GEMINI_API_KEY)
        repair_gemini_model = genai.GenerativeModel(REPAIR_GEMINI_MODEL_NAME)
        REPAIR_GEMINI_CLIENT_OK = True
        update_status("تم تكوين عميل Gemini للإصلاح بنجاح.")
        return True
    except Exception as e:
        update_status(f"!!! خطأ في تهيئة عميل Gemini للإصلاح: {e}")
        REPAIR_GEMINI_CLIENT_OK = False
        return False

# --- Global Variables ---
selected_mods_for_repair = [] # Changed from selected_mod_for_repair = None
loaded_mods_data_list = [] # This will store ALL mods fetched from Supabase
currently_visible_mod_data = [] # This will store mods currently visible in the listbox after filtering
current_category_filter = "All" # Default filter
LOCAL_REPAIRED_MODS_DIR = "repaired_mods_output"

# --- GUI Variables ---
upload_after_repair_var = None # tk.BooleanVar() for the checkbox
select_all_button = None # Added for global access
deselect_all_button = None # Added for global access
repair_mod_button = None # Added for global access
change_format_button = None # Added for global access
replace_mod_button = None # Added for replacing mod files

# --- GUI Functions ---
def update_status(message):
    if 'status_text' in globals() and status_text.winfo_exists():
        try:
            status_text.config(state=tk.NORMAL)
            current_time = time.strftime('%H:%M:%S')
            status_text.insert(tk.END, f"{current_time} - {message}\n")
            status_text.see(tk.END)
            status_text.config(state=tk.DISABLED)
        except tk.TclError: print(f"Status Update (GUI Error): {message}")
    else: print(f"{time.strftime('%H:%M:%S')} - {message}")

# --- Image Management for Mods ---
def handle_view_mod_images():
    """Handle viewing images of a selected mod"""
    global selected_mods_for_repair

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لعرض صوره.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']

    # Create a dialog for viewing images
    image_dialog = tk.Toplevel()
    image_dialog.title(f"عرض صور المود: {mod_name}")
    image_dialog.geometry("800x600")
    image_dialog.minsize(800, 600)

    # Create frames for the dialog
    top_frame = ttk.Frame(image_dialog, padding="10")
    top_frame.pack(fill=tk.X)

    ttk.Label(top_frame, text=f"صور المود: {mod_name}", font=("Segoe UI", 12, "bold")).pack(pady=5)

    # Create a scrollable frame for images
    main_frame = ttk.Frame(image_dialog)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    # Add a canvas with scrollbar
    canvas = tk.Canvas(main_frame)
    scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Status label
    status_label = ttk.Label(image_dialog, text="جاري تحميل الصور...")
    status_label.pack(pady=5)

    # Bottom frame for action buttons
    bottom_frame = ttk.Frame(image_dialog, padding="10")
    bottom_frame.pack(fill=tk.X)

    # Add close button
    close_button = ttk.Button(bottom_frame, text="إغلاق", command=image_dialog.destroy)
    close_button.pack(side=tk.RIGHT, padx=5)

    # Function to extract and display images from the mod
    def extract_and_display_images():
        nonlocal status_label

        temp_extract_dir = f"temp_view_images_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"

        try:
            # Update status
            status_label.config(text="جاري تحميل ملف المود...")
            image_dialog.update_idletasks()

            # Download the mod file
            try:
                response = requests.get(original_mod_url, stream=True, timeout=600)
                response.raise_for_status()
            except requests.exceptions.RequestException as e:
                status_label.config(text=f"خطأ في تحميل المود: {e}")
                return

            # Save the mod file temporarily
            mod_bytes = response.content
            temp_mod_path = os.path.join(temp_extract_dir, os.path.basename(urlparse(original_mod_url).path))

            # Create temp directory
            os.makedirs(temp_extract_dir, exist_ok=True)

            with open(temp_mod_path, 'wb') as f:
                f.write(mod_bytes)

            # Extract the mod
            status_label.config(text="جاري استخراج محتويات المود...")
            image_dialog.update_idletasks()

            # Extract the mod based on its extension
            _, ext = os.path.splitext(temp_mod_path.lower())

            if ext in ['.zip', '.mcpack', '.mcaddon']:
                with zipfile.ZipFile(temp_mod_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_extract_dir)
            else:
                status_label.config(text=f"صيغة الملف غير مدعومة: {ext}")
                return

            # Find all image files in the extracted directory
            status_label.config(text="جاري البحث عن الصور...")
            image_dialog.update_idletasks()

            image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp']
            image_files = []

            for root, _, files in os.walk(temp_extract_dir):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        image_files.append(os.path.join(root, file))

            if not image_files:
                status_label.config(text="لم يتم العثور على صور في هذا المود.")
                return

            # Display the images
            status_label.config(text=f"تم العثور على {len(image_files)} صورة. جاري العرض...")
            image_dialog.update_idletasks()

            for img_path in image_files:
                try:
                    # Create a frame for this image
                    img_frame = ttk.Frame(scrollable_frame, borderwidth=1, relief="solid")
                    img_frame.pack(fill=tk.X, padx=5, pady=5)

                    # Load and display the image
                    img = Image.open(img_path)

                    # Check if it's an animated GIF
                    is_animated = False
                    if img.format == 'GIF' and getattr(img, 'is_animated', False):
                        is_animated = True

                    # Create a thumbnail for display
                    img.thumbnail((200, 200))
                    photo = ImageTk.PhotoImage(img)

                    # Display the image
                    img_label = ttk.Label(img_frame, image=photo)
                    img_label.image = photo  # Keep a reference
                    img_label.pack(side=tk.LEFT, padx=5, pady=5)

                    # Display image info
                    info_frame = ttk.Frame(img_frame)
                    info_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)

                    # Get relative path within the mod
                    rel_path = os.path.relpath(img_path, temp_extract_dir)
                    ttk.Label(info_frame, text=f"المسار: {rel_path}").pack(anchor="w")

                    # Get image dimensions and size
                    width, height = img.size
                    file_size = os.path.getsize(img_path)
                    ttk.Label(info_frame, text=f"الأبعاد: {width}x{height}").pack(anchor="w")
                    ttk.Label(info_frame, text=f"الحجم: {file_size/1024:.1f} KB").pack(anchor="w")

                    if is_animated:
                        ttk.Label(info_frame, text="نوع: GIF متحرك").pack(anchor="w")
                    else:
                        ttk.Label(info_frame, text=f"نوع: {img.format}").pack(anchor="w")

                except Exception as e:
                    ttk.Label(img_frame, text=f"خطأ في عرض الصورة: {os.path.basename(img_path)}").pack(side=tk.LEFT, padx=5)
                    ttk.Label(img_frame, text=f"الخطأ: {e}").pack(side=tk.LEFT, padx=5)

            status_label.config(text=f"تم عرض {len(image_files)} صورة من المود.")

        except Exception as e:
            status_label.config(text=f"حدث خطأ: {e}")
        finally:
            # Schedule cleanup of temp directory
            image_dialog.after(60000, lambda: shutil.rmtree(temp_extract_dir, ignore_errors=True))

    # Start extraction in a separate thread
    threading.Thread(target=extract_and_display_images, daemon=True).start()

def handle_add_images_to_mod():
    """Handle adding images to a selected mod"""
    global selected_mods_for_repair

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لإضافة الصور إليه.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']

    # Ask if user wants to add new images or replace existing ones
    action = messagebox.askquestion("اختيار العملية",
                                   "هل تريد استبدال الصور الموجودة في المود؟\n\n"
                                   "نعم: استبدال الصور الموجودة\n"
                                   "لا: إضافة صور جديدة فقط",
                                   icon='question')

    replace_mode = (action == 'yes')

    if replace_mode:
        # Redirect to view images function for now
        handle_view_mod_images()
        return

    # Create a dialog for image management
    image_dialog = tk.Toplevel()
    image_dialog.title(f"إضافة وتعديل الصور للمود: {mod_name}")
    image_dialog.geometry("800x600")
    image_dialog.minsize(800, 600)

    # Create frames for the dialog
    top_frame = ttk.Frame(image_dialog, padding="10")
    top_frame.pack(fill=tk.X)

    # URL input
    url_frame = ttk.Frame(top_frame)
    url_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

    ttk.Label(url_frame, text="رابط الصورة:").pack(side=tk.LEFT, padx=(0, 5))
    url_entry = ttk.Entry(url_frame, width=50)
    url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

    add_url_button = ttk.Button(url_frame, text="إضافة من الرابط",
                               command=lambda: add_image_from_url(url_entry.get(), mod_images_list))
    add_url_button.pack(side=tk.LEFT)

    # Local file selection
    file_frame = ttk.Frame(top_frame)
    file_frame.pack(side=tk.LEFT, padx=(10, 0))

    add_file_button = ttk.Button(file_frame, text="إضافة من القرص D",
                                command=lambda: add_image_from_disk('D:', mod_images_list))
    add_file_button.pack(side=tk.LEFT, padx=(0, 5))

    add_any_file_button = ttk.Button(file_frame, text="إضافة من أي مكان",
                                    command=lambda: add_image_from_disk(None, mod_images_list))
    add_any_file_button.pack(side=tk.LEFT)

    # Create a frame for the image list
    list_frame = ttk.LabelFrame(image_dialog, text="الصور المضافة", padding="10")
    list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    # Create a canvas and scrollbar for the image list
    canvas = tk.Canvas(list_frame)
    scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # List to store the images
    mod_images_list = []

    # Function to add image from URL
    def add_image_from_url(url, images_list):
        if not url:
            messagebox.showwarning("إدخال مطلوب", "الرجاء إدخال رابط الصورة.", parent=image_dialog)
            return

        try:
            # Download the image
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            # Check if it's an image
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                messagebox.showwarning("نوع ملف غير صالح",
                                      f"الملف ليس صورة. نوع المحتوى: {content_type}",
                                      parent=image_dialog)
                return

            # Get the image data
            image_data = response.content

            # Get the filename from the URL
            filename = os.path.basename(urlparse(url).path)
            if not filename:
                filename = f"image_{len(images_list) + 1}.jpg"

            # Add to the list
            add_image_to_list(filename, image_data, url, images_list, scrollable_frame)

            # Clear the URL entry
            url_entry.delete(0, tk.END)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل تحميل الصورة: {e}", parent=image_dialog)

    # Function to add image from disk
    def add_image_from_disk(initial_dir, images_list):
        filetypes = [("Image files", "*.png *.jpg *.jpeg *.webp *.gif"), ("All files", "*.*")]

        if initial_dir and os.path.exists(initial_dir):
            filepath = filedialog.askopenfilename(
                title="اختر صورة",
                initialdir=initial_dir,
                filetypes=filetypes,
                parent=image_dialog
            )
        else:
            filepath = filedialog.askopenfilename(
                title="اختر صورة",
                filetypes=filetypes,
                parent=image_dialog
            )

        if not filepath:
            return

        try:
            # Read the image file
            with open(filepath, 'rb') as f:
                image_data = f.read()

            # Get the filename
            filename = os.path.basename(filepath)

            # Add to the list
            add_image_to_list(filename, image_data, filepath, images_list, scrollable_frame)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل قراءة الصورة: {e}", parent=image_dialog)

    # Function to add image to the list and display it
    def add_image_to_list(filename, image_data, source, images_list, parent_frame):
        try:
            # Create a thumbnail
            img = Image.open(BytesIO(image_data))
            img.thumbnail((100, 100))
            photo = ImageTk.PhotoImage(img)

            # Create a frame for this image
            img_frame = ttk.Frame(parent_frame, borderwidth=1, relief="solid")
            img_frame.pack(fill=tk.X, padx=5, pady=5)

            # Display the image
            img_label = ttk.Label(img_frame, image=photo)
            img_label.image = photo  # Keep a reference
            img_label.pack(side=tk.LEFT, padx=5, pady=5)

            # Display the filename
            name_frame = ttk.Frame(img_frame)
            name_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)

            ttk.Label(name_frame, text=f"الاسم: {filename}").pack(anchor="w")

            # Show original size
            original_size = len(image_data)
            ttk.Label(name_frame, text=f"الحجم: {format_size(original_size)}").pack(anchor="w")

            # Check if it's a GIF and animated
            is_animated_gif = False
            try:
                if img.format == 'GIF' and getattr(img, 'is_animated', False):
                    is_animated_gif = True
                    ttk.Label(name_frame, text=f"GIF متحرك ({img.n_frames} إطار)").pack(anchor="w")
            except:
                pass

            # Add a text entry for the destination path in the mod
            path_frame = ttk.Frame(name_frame)
            path_frame.pack(fill=tk.X, pady=5)
            ttk.Label(path_frame, text="المسار في المود:").pack(side=tk.LEFT)
            path_entry = ttk.Entry(path_frame, width=30)
            path_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
            path_entry.insert(0, f"textures/")

            # Add compression options
            compression_frame = ttk.Frame(name_frame)
            compression_frame.pack(fill=tk.X, pady=5)
            ttk.Label(compression_frame, text="مستوى الضغط:").pack(side=tk.LEFT)

            compression_var = tk.StringVar(value="normal")
            compression_combo = ttk.Combobox(
                compression_frame,
                textvariable=compression_var,
                values=["none", "normal", "medium", "high"],
                width=8,
                state="readonly"
            )
            compression_combo.pack(side=tk.LEFT, padx=5)

            # Add compress button
            compress_button = ttk.Button(
                compression_frame,
                text="ضغط",
                command=lambda: compress_image_item(img_item, compression_var.get())
            )
            compress_button.pack(side=tk.LEFT, padx=5)

            # Add buttons
            button_frame = ttk.Frame(img_frame)
            button_frame.pack(side=tk.RIGHT, padx=5)

            remove_button = ttk.Button(button_frame, text="إزالة",
                                      command=lambda: remove_image_from_list(img_item, images_list, img_frame))
            remove_button.pack(pady=2)

            # Create the image item and add to the list
            img_item = {
                'filename': filename,
                'data': image_data,
                'source': source,
                'frame': img_frame,
                'photo': photo,
                'path_entry': path_entry,
                'compression_var': compression_var,
                'compress_button': compress_button,
                'original_size': original_size,
                'compressed': False,
                'is_animated_gif': is_animated_gif
            }

            images_list.append(img_item)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل إضافة الصورة: {e}", parent=image_dialog)

    # Function to compress an image item
    def compress_image_item(img_item, compression_level):
        if compression_level == "none":
            # Reset to original image if previously compressed
            if img_item.get('compressed', False):
                img_item['data'] = img_item.get('original_data', img_item['data'])
                img_item['compressed'] = False
                update_status(f"تم إلغاء ضغط الصورة: {img_item['filename']}")

                # Update size label
                for widget in img_item['frame'].winfo_children():
                    if isinstance(widget, ttk.Frame):
                        for child in widget.winfo_children():
                            if isinstance(child, ttk.Label) and child.cget('text').startswith("الحجم:"):
                                child.config(text=f"الحجم: {format_size(len(img_item['data']))}")
                            elif isinstance(child, ttk.Label) and child.cget('text').startswith("بعد الضغط:"):
                                child.destroy()
            return

        try:
            # Store original data if not already stored
            if 'original_data' not in img_item:
                img_item['original_data'] = img_item['data']

            # Compress the image
            update_status(f"جاري ضغط الصورة: {img_item['filename']} بمستوى {compression_level}...")

            compressed_bytes, format_str, content_type, original_size, compressed_size = compress_image(
                img_item['data'] if not img_item.get('compressed', False) else img_item['original_data'],
                compression_level
            )

            # Update the image item with compressed data
            img_item['data'] = compressed_bytes
            img_item['compressed'] = True

            # Calculate compression ratio
            compression_ratio = (1 - (compressed_size / original_size)) * 100 if original_size > 0 else 0

            # Update size labels
            size_label_updated = False
            compression_label_exists = False

            for widget in img_item['frame'].winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Label) and child.cget('text').startswith("الحجم:"):
                            child.config(text=f"الحجم: {format_size(original_size)}")
                            size_label_updated = True
                        elif isinstance(child, ttk.Label) and child.cget('text').startswith("بعد الضغط:"):
                            child.config(text=f"بعد الضغط: {format_size(compressed_size)} (توفير: {compression_ratio:.1f}%)")
                            compression_label_exists = True

            # If compression label doesn't exist, create it
            if not compression_label_exists:
                for widget in img_item['frame'].winfo_children():
                    if isinstance(widget, ttk.Frame) and widget.winfo_children() and isinstance(widget.winfo_children()[0], ttk.Label) and widget.winfo_children()[0].cget('text').startswith("الاسم:"):
                        ttk.Label(widget, text=f"بعد الضغط: {format_size(compressed_size)} (توفير: {compression_ratio:.1f}%)").pack(anchor="w")
                        break

            update_status(f"تم ضغط الصورة: {img_item['filename']}. الحجم الأصلي: {format_size(original_size)}, بعد الضغط: {format_size(compressed_size)}, توفير: {compression_ratio:.1f}%")

            # If format changed, update filename
            if format_str and not img_item['filename'].lower().endswith(f".{format_str}"):
                base_name, _ = os.path.splitext(img_item['filename'])
                new_filename = f"{base_name}.{format_str}"
                img_item['filename'] = new_filename

                # Update filename label
                for widget in img_item['frame'].winfo_children():
                    if isinstance(widget, ttk.Frame):
                        for child in widget.winfo_children():
                            if isinstance(child, ttk.Label) and child.cget('text').startswith("الاسم:"):
                                child.config(text=f"الاسم: {new_filename}")
                                break

        except Exception as e:
            update_status(f"خطأ في ضغط الصورة: {e}")
            messagebox.showerror("خطأ", f"فشل ضغط الصورة: {e}", parent=image_dialog)

    # Function to remove image from the list
    def remove_image_from_list(img_item, images_list, img_frame):
        images_list.remove(img_item)
        img_frame.destroy()

    # Function to format file size
    def format_size(size_bytes):
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.2f} MB"

    # Bottom frame for action buttons
    bottom_frame = ttk.Frame(image_dialog, padding="10")
    bottom_frame.pack(fill=tk.X)

    # Add a checkbox for uploading to Supabase
    upload_var = tk.BooleanVar(value=True)
    upload_checkbox = ttk.Checkbutton(bottom_frame, text="رفع إلى Supabase بعد التعديل", variable=upload_var)
    upload_checkbox.pack(side=tk.LEFT, padx=5)

    # Add buttons for actions
    cancel_button = ttk.Button(bottom_frame, text="إلغاء", command=image_dialog.destroy)
    cancel_button.pack(side=tk.RIGHT, padx=5)

    apply_button = ttk.Button(bottom_frame, text="تطبيق التغييرات",
                             command=lambda: apply_images_to_mod(mod_data, mod_images_list, upload_var.get(), image_dialog))
    apply_button.pack(side=tk.RIGHT, padx=5)

    # Function to apply images to the mod
    def apply_images_to_mod(mod_data, images_list, upload_to_supabase, dialog):
        if not images_list:
            messagebox.showwarning("لا توجد صور", "لم تتم إضافة أي صور للمود.", parent=dialog)
            return

        # Confirm the action
        confirm_msg = f"سيتم إضافة {len(images_list)} صورة إلى المود '{mod_data['name']}'."
        if upload_to_supabase:
            confirm_msg += "\nسيتم رفع المود المعدل إلى Supabase."

        if not messagebox.askyesno("تأكيد", confirm_msg, parent=dialog):
            return

        # Disable the apply button
        apply_button.config(state=tk.DISABLED)

        # Start the process in a separate thread
        threading.Thread(
            target=process_add_images_to_mod,
            args=(mod_data, images_list, upload_to_supabase, dialog),
            daemon=True
        ).start()

    # Center the dialog on the screen
    image_dialog.update_idletasks()
    width = image_dialog.winfo_width()
    height = image_dialog.winfo_height()
    x = (image_dialog.winfo_screenwidth() // 2) - (width // 2)
    y = (image_dialog.winfo_screenheight() // 2) - (height // 2)
    image_dialog.geometry(f"{width}x{height}+{x}+{y}")

    # Make the dialog modal
    image_dialog.transient(window)
    image_dialog.grab_set()
    image_dialog.focus_set()

def process_image_replacements(mod_data, new_images_list, existing_images_list, upload_to_supabase, dialog):
    """معالجة استبدال الصور في المود في خيط منفصل"""
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']
    original_file_path_in_storage = mod_data.get('file_path_in_storage')

    temp_extract_dir = f"temp_extract_img_replace_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"
    local_modified_filepath = None

    try:
        # الخطوة 1: تحميل المود
        update_status(f"الخطوة 1: تحميل المود '{mod_name}' من الرابط '{original_mod_url}'...")

        mod_bytes = None
        mod_content_stream = BytesIO()

        try:
            response = requests.get(original_mod_url, stream=True, timeout=600)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            update_status(f"حجم الملف المتوقع: {format_size_simple(total_size)}" if total_size else "جاري حساب حجم الملف...")

            for chunk in response.iter_content(chunk_size=8192 * 4):
                if chunk:
                    mod_content_stream.write(chunk)

            mod_bytes = mod_content_stream.getvalue()
            update_status(f"اكتمل تحميل الملف. الحجم الكلي: {format_size_simple(len(mod_bytes))}.")

        except requests.exceptions.RequestException as e:
            update_status(f"!!! خطأ فادح أثناء تحميل المود: {e}")
            messagebox.showerror("خطأ", f"فشل تحميل المود: {e}", parent=dialog)
            return

        # الخطوة 2: استخراج المود
        update_status(f"الخطوة 2: استخراج المود إلى المجلد المؤقت: {temp_extract_dir}")

        if not os.path.exists(temp_extract_dir):
            os.makedirs(temp_extract_dir)

        try:
            with zipfile.ZipFile(BytesIO(mod_bytes), 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            update_status("تم استخراج المود بنجاح.")
        except zipfile.BadZipFile:
            update_status("!!! خطأ: الملف الذي تم تحميله ليس ملف ZIP صالحًا.")
            messagebox.showerror("خطأ", "الملف الذي تم تحميله ليس ملف ZIP صالحًا.", parent=dialog)
            return

        # الخطوة 3: استبدال الصور الموجودة
        update_status(f"الخطوة 3: استبدال الصور في المود...")

        # تتبع الصور التي تم استبدالها
        replaced_images = []

        for i, existing_img in enumerate(existing_images_list):
            if existing_img['replace_var'].get() and existing_img['replacement_var'].get():
                # العثور على الصورة الجديدة المحددة للاستبدال
                replacement_filename = existing_img['replacement_var'].get()
                replacement_img = None

                for new_img in new_images_list:
                    if new_img['filename'] == replacement_filename:
                        replacement_img = new_img
                        break

                if not replacement_img:
                    update_status(f"!!! خطأ: لم يتم العثور على الصورة البديلة '{replacement_filename}'")
                    continue

                # المسار الكامل للصورة الموجودة في المود
                existing_path = os.path.join(temp_extract_dir, existing_img['rel_path'])

                # حذف الصورة القديمة
                try:
                    if os.path.exists(existing_path):
                        os.remove(existing_path)
                        update_status(f"تم حذف الصورة القديمة: {existing_img['rel_path']}")
                except Exception as e:
                    update_status(f"!!! خطأ أثناء حذف الصورة القديمة {existing_img['rel_path']}: {e}")

                # كتابة الصورة الجديدة
                try:
                    # التأكد من وجود المجلد
                    os.makedirs(os.path.dirname(existing_path), exist_ok=True)

                    # استخدام الصورة المضغوطة إذا كانت متاحة
                    image_data = replacement_img['data']

                    # كتابة الصورة الجديدة
                    with open(existing_path, 'wb') as f:
                        f.write(image_data)

                    update_status(f"تم استبدال الصورة {i+1}: {existing_img['rel_path']} بـ {replacement_filename}")
                    replaced_images.append(existing_img['rel_path'])

                except Exception as e:
                    update_status(f"!!! خطأ أثناء كتابة الصورة الجديدة {replacement_filename}: {e}")

        # الخطوة 4: إضافة الصور الجديدة التي لم تستخدم في الاستبدال
        update_status(f"الخطوة 4: إضافة الصور الجديدة إلى المود...")

        # تحديد الصور الجديدة التي لم تستخدم في الاستبدال
        unused_new_images = []
        for new_img in new_images_list:
            used_in_replacement = False
            for existing_img in existing_images_list:
                if existing_img['replace_var'].get() and existing_img['replacement_var'].get() == new_img['filename']:
                    used_in_replacement = True
                    break

            if not used_in_replacement:
                unused_new_images.append(new_img)

        # إضافة الصور الجديدة غير المستخدمة
        for i, img_item in enumerate(unused_new_images):
            filename = img_item['filename']
            image_data = img_item['data']
            dest_path = img_item.get('path_entry', '').get().strip() if hasattr(img_item.get('path_entry', ''), 'get') else 'textures/'

            # التأكد من وجود المجلد
            if dest_path:
                full_dest_path = os.path.join(temp_extract_dir, dest_path)
                if not os.path.exists(full_dest_path):
                    os.makedirs(full_dest_path, exist_ok=True)

                # كتابة ملف الصورة
                image_filepath = os.path.join(full_dest_path, filename)
            else:
                # إذا لم يتم تحديد مسار، ضع في المجلد الجذر
                image_filepath = os.path.join(temp_extract_dir, filename)

            with open(image_filepath, 'wb') as f:
                f.write(image_data)

            update_status(f"تمت إضافة الصورة الجديدة {i+1}/{len(unused_new_images)}: {os.path.relpath(image_filepath, temp_extract_dir)}")

        # الخطوة 5: إعادة ضغط المود
        update_status("الخطوة 5: إعادة ضغط المود...")

        if not os.path.exists(LOCAL_REPAIRED_MODS_DIR):
            os.makedirs(LOCAL_REPAIRED_MODS_DIR)

        # الحصول على الامتداد الأصلي
        _, original_ext = os.path.splitext(original_mod_url.split('?')[0])
        if original_ext.lower() not in [".mcaddon", ".mcpack"]:
            original_ext = ".mcaddon"

        # إنشاء اسم الملف المعدل
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        local_modified_filename = f"{sanitize_filename(mod_name)}_replaced_images_{timestamp}{original_ext}"
        local_modified_filepath = os.path.join(LOCAL_REPAIRED_MODS_DIR, local_modified_filename)

        # ضغط المجلد إلى ملف ZIP
        with zipfile.ZipFile(local_modified_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(temp_extract_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, temp_extract_dir)
                    zipf.write(file_path, arcname)

        update_status(f"تم إعادة ضغط المود بنجاح. المسار: {local_modified_filepath}")

        # الخطوة 6: رفع المود المعدل إلى Supabase إذا تم تحديد ذلك
        if upload_to_supabase:
            update_status("الخطوة 6: رفع المود المعدل إلى Supabase...")

            try:
                # قراءة الملف المعدل
                with open(local_modified_filepath, 'rb') as f:
                    modified_mod_bytes = f.read()

                # إنشاء اسم ملف فريد للتخزين
                storage_filename = f"{sanitize_filename(mod_name)}_{mod_id}_{timestamp}{original_ext}"

                # رفع الملف إلى Supabase
                upload_response = app_storage_client.from_(MODS_BUCKET_NAME).upload(
                    storage_filename,
                    modified_mod_bytes,
                    {"content-type": "application/octet-stream"}
                )

                if not upload_response.get('error'):
                    # الحصول على رابط عام للملف المرفوع
                    new_public_url = f"{STORAGE_URL}/storage/v1/object/public/{MODS_BUCKET_NAME}/{storage_filename}"
                    update_status(f"تم رفع المود المعدل بنجاح. الرابط الجديد: {new_public_url}")

                    # حذف الملف القديم من Supabase إذا كان موجودًا
                    if original_file_path_in_storage:
                        try:
                            delete_response = app_storage_client.from_(MODS_BUCKET_NAME).remove([original_file_path_in_storage])
                            if not delete_response.get('error'):
                                update_status(f"تم حذف الملف القديم من Supabase: {original_file_path_in_storage}")
                            else:
                                update_status(f"!!! خطأ أثناء حذف الملف القديم: {delete_response.get('error')}")
                        except Exception as del_err:
                            update_status(f"!!! خطأ أثناء حذف الملف القديم: {del_err}")

                    # تحديث سجل المود في قاعدة البيانات بالرابط الجديد
                    update_status(f"الخطوة 7: تحديث سجل المود (ID: {mod_id}) في قاعدة البيانات بالرابط الجديد...")
                    try:
                        update_data = {
                            "download_url": new_public_url,
                            "file_path_in_storage": storage_filename
                        }
                        db_update_response = app_db_client.table(MODS_TABLE_NAME).update(update_data).eq("id", mod_id).execute()
                        if db_update_response.data:
                            update_status("تم تحديث سجل المود في قاعدة البيانات بنجاح.")
                            # تحديث القائمة في الذاكرة لمنع مشاكل البيانات القديمة للعمليات اللاحقة
                            global loaded_mods_data_list
                            for i, mod_in_list in enumerate(loaded_mods_data_list):
                                if mod_in_list['id'] == mod_id:
                                    loaded_mods_data_list[i]['download_url'] = new_public_url
                                    loaded_mods_data_list[i]['file_path_in_storage'] = storage_filename
                                    break
                        else:
                            update_status(f"!!! خطأ أثناء تحديث سجل المود: {db_update_response.error}")
                    except Exception as db_err:
                        update_status(f"!!! خطأ أثناء تحديث سجل المود: {db_err}")

                    # عرض رسالة نجاح
                    messagebox.showinfo(
                        "تمت العملية بنجاح",
                        f"تم استبدال {len(replaced_images)} صورة وإضافة {len(unused_new_images)} صورة جديدة إلى المود '{mod_name}' ورفعه إلى Supabase بنجاح.",
                        parent=dialog
                    )
                else:
                    update_status(f"!!! خطأ أثناء رفع المود المعدل: {upload_response.get('error')}")
                    messagebox.showerror("خطأ", f"فشل رفع المود المعدل: {upload_response.get('error')}", parent=dialog)
            except Exception as e:
                update_status(f"!!! خطأ أثناء رفع المود المعدل إلى Supabase: {e}")
                messagebox.showerror("خطأ", f"فشل رفع المود المعدل: {e}", parent=dialog)
        else:
            # عرض رسالة نجاح للحفظ المحلي فقط
            messagebox.showinfo(
                "تمت العملية بنجاح",
                f"تم استبدال {len(replaced_images)} صورة وإضافة {len(unused_new_images)} صورة جديدة إلى المود '{mod_name}' وحفظه محليًا بنجاح.\n\nالمسار: {os.path.abspath(local_modified_filepath)}",
                parent=dialog
            )

        # إغلاق النافذة
        dialog.destroy()

    except Exception as e:
        update_status(f"!!! خطأ غير متوقع أثناء استبدال الصور في المود: {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}", parent=dialog)

    finally:
        # تنظيف المجلد المؤقت
        if os.path.exists(temp_extract_dir):
            try:
                shutil.rmtree(temp_extract_dir)
                update_status(f"تم تنظيف المجلد المؤقت: {temp_extract_dir}")
            except Exception as e_clean:
                update_status(f"!!! خطأ أثناء تنظيف المجلد المؤقت: {e_clean}")

def process_add_images_to_mod(mod_data, images_list, upload_to_supabase, dialog):
    """Process adding images to a mod in a separate thread"""
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']
    original_file_path_in_storage = mod_data.get('file_path_in_storage')

    temp_extract_dir = f"temp_extract_img_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"
    local_modified_filepath = None

    try:
        # Step 1: Download the mod
        update_status(f"تحميل المود '{mod_name}' من الرابط '{original_mod_url}'...")

        mod_bytes = None
        mod_content_stream = BytesIO()

        try:
            response = requests.get(original_mod_url, stream=True, timeout=600)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            update_status(f"حجم الملف المتوقع: {format_size_simple(total_size)}" if total_size else "جاري حساب حجم الملف...")

            for chunk in response.iter_content(chunk_size=8192 * 4):
                if chunk:
                    mod_content_stream.write(chunk)

            mod_bytes = mod_content_stream.getvalue()
            update_status(f"اكتمل تحميل الملف. الحجم الكلي: {format_size_simple(len(mod_bytes))}.")

        except requests.exceptions.RequestException as e:
            update_status(f"!!! خطأ فادح أثناء تحميل المود: {e}")
            messagebox.showerror("خطأ", f"فشل تحميل المود: {e}", parent=dialog)
            return

        # Step 2: Extract the mod
        update_status(f"استخراج المود إلى المجلد المؤقت: {temp_extract_dir}")

        if not os.path.exists(temp_extract_dir):
            os.makedirs(temp_extract_dir)

        try:
            with zipfile.ZipFile(BytesIO(mod_bytes), 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            update_status("تم استخراج المود بنجاح.")
        except zipfile.BadZipFile:
            update_status("!!! خطأ: الملف الذي تم تحميله ليس ملف ZIP صالحًا.")
            messagebox.showerror("خطأ", "الملف الذي تم تحميله ليس ملف ZIP صالحًا.", parent=dialog)
            return

        # Step 3: Add the images to the mod
        update_status(f"إضافة {len(images_list)} صورة إلى المود...")

        for i, img_item in enumerate(images_list):
            filename = img_item['filename']
            image_data = img_item['data']
            dest_path = img_item['path_entry'].get().strip()

            # Check if image was compressed
            if img_item.get('compressed', False):
                update_status(f"استخدام الصورة المضغوطة لـ {filename}")
                compression_info = f" (مضغوطة بمستوى {img_item['compression_var'].get()})"
            else:
                compression_info = ""

            # Ensure the destination path exists
            if dest_path:
                full_dest_path = os.path.join(temp_extract_dir, dest_path)
                if not os.path.exists(full_dest_path):
                    os.makedirs(full_dest_path, exist_ok=True)

                # Write the image file
                image_filepath = os.path.join(full_dest_path, filename)
            else:
                # If no path specified, put in the root
                image_filepath = os.path.join(temp_extract_dir, filename)

            with open(image_filepath, 'wb') as f:
                f.write(image_data)

            update_status(f"تمت إضافة الصورة {i+1}/{len(images_list)}: {os.path.relpath(image_filepath, temp_extract_dir)}{compression_info}")

        # Step 4: Repack the mod
        update_status("إعادة ضغط المود...")

        if not os.path.exists(LOCAL_REPAIRED_MODS_DIR):
            os.makedirs(LOCAL_REPAIRED_MODS_DIR)

        # Get the original extension
        _, original_ext = os.path.splitext(original_mod_url.split('?')[0])
        if original_ext.lower() not in [".mcaddon", ".mcpack"]:
            original_ext = ".mcaddon"

        # Create the new filename
        local_modified_filename = f"{sanitize_filename(mod_name)}_with_images_{int(time.time())}{original_ext}"
        local_modified_filepath = os.path.join(LOCAL_REPAIRED_MODS_DIR, local_modified_filename)

        # Create the new zip file
        with zipfile.ZipFile(local_modified_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(temp_extract_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    archive_name = os.path.relpath(file_path, temp_extract_dir)
                    zipf.write(file_path, archive_name)

        update_status(f"تم إعادة ضغط المود بنجاح. المسار: {local_modified_filepath}")

        # Step 5: Upload to Supabase if requested
        if upload_to_supabase:
            if not STORAGE_CLIENT_OK or not app_db_client:
                update_status("!!! خطأ: عملاء Supabase غير متاحين للرفع أو تحديث قاعدة البيانات.")
                messagebox.showerror("خطأ Supabase", "عملاء Supabase غير مهيئين بشكل صحيح.", parent=dialog)
                return

            update_status(f"رفع المود المعدل '{local_modified_filename}' إلى Supabase...")

            try:
                with open(local_modified_filepath, 'rb') as f:
                    upload_response = storage_client.storage.from_(MOD_BUCKET).upload(
                        path=local_modified_filename,
                        file=f,
                        file_options={"content-type": "application/octet-stream", "cache-control": "3600", "upsert": "false"}
                    )
                update_status(f"تم رفع المود المعدل بنجاح. استجابة: {upload_response}")

                # Get the public URL
                new_public_url = storage_client.storage.from_(MOD_BUCKET).get_public_url(local_modified_filename)
                update_status(f"الرابط العام الجديد: {new_public_url}")

                # Update the database record
                update_status(f"تحديث سجل المود (ID: {mod_id}) في قاعدة البيانات...")

                update_data = {"download_url": new_public_url}
                db_update_response = app_db_client.table(MODS_TABLE_NAME).update(update_data).eq("id", mod_id).execute()

                if db_update_response.data:
                    update_status("تم تحديث سجل المود في قاعدة البيانات بنجاح.")

                    # Update in-memory list
                    for i, mod_in_list in enumerate(loaded_mods_data_list):
                        if mod_in_list['id'] == mod_id:
                            loaded_mods_data_list[i]['download_url'] = new_public_url
                            update_status(f"تم تحديث بيانات المود '{mod_name}' في القائمة المحملة محليًا.")
                            break
                else:
                    error_msg = "فشل تحديث سجل المود في قاعدة البيانات."
                    if hasattr(db_update_response, 'error') and db_update_response.error:
                        error_msg += f" تفاصيل الخطأ: {db_update_response.error.message}"
                    update_status(f"!!! {error_msg}")
                    messagebox.showwarning("تحذير", error_msg, parent=dialog)

                # Delete the old file if needed
                if original_file_path_in_storage and original_file_path_in_storage != local_modified_filename:
                    update_status(f"حذف الملف القديم من Supabase (المسار: {original_file_path_in_storage})...")

                    try:
                        delete_response = storage_client.storage.from_(MOD_BUCKET).remove([original_file_path_in_storage])
                        update_status(f"استجابة حذف الملف القديم: {delete_response}")
                    except Exception as e:
                        update_status(f"!!! خطأ أثناء حذف الملف القديم: {e}")

                # Show success message
                messagebox.showinfo(
                    "تمت العملية بنجاح",
                    f"تم إضافة الصور إلى المود '{mod_name}' ورفعه إلى Supabase بنجاح.\n\nالرابط الجديد: {new_public_url}",
                    parent=dialog
                )

            except Exception as e:
                update_status(f"!!! خطأ أثناء رفع المود المعدل إلى Supabase: {e}")
                messagebox.showerror("خطأ", f"فشل رفع المود المعدل: {e}", parent=dialog)
        else:
            # Show success message for local only
            messagebox.showinfo(
                "تمت العملية بنجاح",
                f"تم إضافة الصور إلى المود '{mod_name}' وحفظه محليًا بنجاح.\n\nالمسار: {os.path.abspath(local_modified_filepath)}",
                parent=dialog
            )

        # Close the dialog
        dialog.destroy()

    except Exception as e:
        update_status(f"!!! خطأ غير متوقع أثناء إضافة الصور إلى المود: {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}", parent=dialog)

        # Re-enable the apply button
        if dialog.winfo_exists():
            for widget in dialog.winfo_children():
                if isinstance(widget, ttk.Frame) and widget.winfo_children():
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Button) and child.cget('text') == "تطبيق التغييرات":
                            child.config(state=tk.NORMAL)
    finally:
        # Clean up the temporary directory
        if os.path.exists(temp_extract_dir):
            try:
                shutil.rmtree(temp_extract_dir)
                update_status(f"تم تنظيف المجلد المؤقت: {temp_extract_dir}")
            except Exception as e:
                update_status(f"!!! خطأ أثناء تنظيف المجلد المؤقت: {e}")


def run_in_thread(target_func, *args, **kwargs):
    thread = threading.Thread(target=target_func, args=args, kwargs=kwargs, daemon=True)
    thread.start()

def handle_load_published_mods():
    if not APP_DB_CLIENT_OK:
        messagebox.showerror("خطأ في قاعدة البيانات", "فشل الاتصال بقاعدة بيانات التطبيق.")
        return
    update_status("جاري تحميل قائمة المودات المنشورة...")
    load_mods_button.config(state=tk.DISABLED)
    run_in_thread(load_published_mods_task)

def load_published_mods_task():
    global mods_listbox, loaded_mods_data_list, currently_visible_mod_data
    try:
        # Fetch id, name, download_url, category, and downloads
        response = app_db_client.table(MODS_TABLE_NAME).select("id, name, download_url, category, downloads").execute()

        if hasattr(response, 'data') and response.data:
            loaded_mods_data_list.clear()
            for mod_item in response.data:
                # Ensure category is present, default to "Unknown" or similar if None/missing
                mod_item['category'] = mod_item.get('category', 'غير مصنف')
                loaded_mods_data_list.append(mod_item)

            update_status(f"تم تحميل {len(loaded_mods_data_list)} مود بنجاح من قاعدة البيانات.")
            # Populate the listbox with all mods initially
            populate_mods_listbox() # Uses the default "All" filter

            if repair_mod_button and repair_mod_button.winfo_exists():
                 repair_mod_button.config(state=tk.NORMAL)
            if change_format_button and change_format_button.winfo_exists():
                 change_format_button.config(state=tk.NORMAL)
        else:
            update_status("لم يتم العثور على مودات أو حدث خطأ أثناء الجلب.")
            if hasattr(response, 'error') and response.error:
                update_status(f"Error details: {response.error.message}")
    except Exception as e:
        update_status(f"!!! خطأ فادح أثناء تحميل المودات: {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
    finally:
        if 'load_mods_button' in globals() and load_mods_button.winfo_exists():
            load_mods_button.config(state=tk.NORMAL)

def populate_mods_listbox(category_filter="All"):
    global mods_listbox, loaded_mods_data_list, currently_visible_mod_data, current_category_filter, view_images_button, download_link_button

    current_category_filter = category_filter # Update global filter state

    if not (mods_listbox and mods_listbox.winfo_exists()):
        update_status("خطأ: مربع قائمة المودات غير متاح.")
        return

    mods_listbox.config(state=tk.NORMAL)
    mods_listbox.delete(0, tk.END)
    currently_visible_mod_data.clear()

    count = 0
    for mod_item in loaded_mods_data_list:
        mod_category = mod_item.get('category', 'غير مصنف')
        # Case-insensitive comparison for filter
        if category_filter == "All" or (mod_category and category_filter.lower() == mod_category.lower()):
            mod_name = mod_item['name']
            mod_id = mod_item['id']
            download_url = mod_item.get('download_url', '')

            file_extension = ""
            if download_url:
                try:
                    path_part = download_url.split('?')[0]
                    _, file_extension = os.path.splitext(path_part)
                except Exception:
                    file_extension = ""

            # Format the display text with extension and icon
            extension_lower = file_extension.lower()
            if extension_lower:
                if extension_lower == '.zip':
                    format_icon = "📦" # Box icon for ZIP
                elif extension_lower == '.mcaddon':
                    format_icon = "🧩" # Puzzle piece for MCADDON
                elif extension_lower == '.mcpack':
                    format_icon = "📚" # Books for MCPACK
                else:
                    format_icon = "📄" # Generic document for other formats

                display_text = f"ID: {mod_id} - {format_icon} {mod_name} ({extension_lower})"
            else:
                display_text = f"ID: {mod_id} - {mod_name}"
            mods_listbox.insert(tk.END, display_text)
            currently_visible_mod_data.append(mod_item)
            count +=1

    mods_listbox.config(state=tk.NORMAL) # Keep it normal for selection
    if category_filter == "All":
        update_status(f"عرض جميع المودات ({count} مود).")
    else:
        update_status(f"عرض المودات من فئة '{category_filter}' ({count} مود).")

    # Clear previous selections and disable action buttons as the list has changed
    handle_deselect_all_mods() # This will also disable buttons

def on_mod_select(event):
    global selected_mods_for_repair, currently_visible_mod_data, repair_mod_button, change_format_button, replace_mod_button, add_images_button, download_link_button, view_images_button, republish_mod_button
    widget = event.widget
    selected_indices = widget.curselection()
    selected_mods_for_repair.clear()

    if selected_indices:
        for index in selected_indices:
            if 0 <= index < len(currently_visible_mod_data): # Use currently_visible_mod_data
                selected_mods_for_repair.append(currently_visible_mod_data[index])

        if selected_mods_for_repair:
            if len(selected_mods_for_repair) == 1:
                mod = selected_mods_for_repair[0]
                update_status(f"تم تحديد المود: {mod['name']} (ID: {mod['id']})")
                # Enable buttons that require a single mod selection
                if replace_mod_button and replace_mod_button.winfo_exists():
                    replace_mod_button.config(state=tk.NORMAL)
                if add_images_button and add_images_button.winfo_exists():
                    add_images_button.config(state=tk.NORMAL)
                if view_images_button and view_images_button.winfo_exists():
                    view_images_button.config(state=tk.NORMAL)
                if download_link_button and download_link_button.winfo_exists():
                    download_link_button.config(state=tk.NORMAL)
                if republish_mod_button and republish_mod_button.winfo_exists():
                    republish_mod_button.config(state=tk.NORMAL)
            else:
                update_status(f"تم تحديد {len(selected_mods_for_repair)} مودات.")
                # Disable buttons that require a single mod selection when multiple mods are selected
                if replace_mod_button and replace_mod_button.winfo_exists():
                    replace_mod_button.config(state=tk.DISABLED)
                if add_images_button and add_images_button.winfo_exists():
                    add_images_button.config(state=tk.DISABLED)
                if view_images_button and view_images_button.winfo_exists():
                    view_images_button.config(state=tk.DISABLED)
                if download_link_button and download_link_button.winfo_exists():
                    download_link_button.config(state=tk.DISABLED)
                if republish_mod_button and republish_mod_button.winfo_exists():
                    republish_mod_button.config(state=tk.DISABLED)

            # Enable buttons that work with multiple mod selection
            if repair_mod_button and repair_mod_button.winfo_exists():
                repair_mod_button.config(state=tk.NORMAL)
            if change_format_button and change_format_button.winfo_exists():
                change_format_button.config(state=tk.NORMAL)
            return

    # If no valid selection or list is empty after processing
    if repair_mod_button and repair_mod_button.winfo_exists():
        repair_mod_button.config(state=tk.DISABLED)
    if change_format_button and change_format_button.winfo_exists():
        change_format_button.config(state=tk.DISABLED)
    if replace_mod_button and replace_mod_button.winfo_exists():
        replace_mod_button.config(state=tk.DISABLED)
    if add_images_button and add_images_button.winfo_exists():
        add_images_button.config(state=tk.DISABLED)
    if view_images_button and view_images_button.winfo_exists():
        view_images_button.config(state=tk.DISABLED)
    if download_link_button and download_link_button.winfo_exists():
        download_link_button.config(state=tk.DISABLED)
    if republish_mod_button and republish_mod_button.winfo_exists():
        republish_mod_button.config(state=tk.DISABLED)
    update_status("لم يتم تحديد أي مود أو أن التحديدات غير صالحة.")


def handle_select_all_mods():
    global mods_listbox, selected_mods_for_repair, loaded_mods_data_list, repair_mod_button, change_format_button, replace_mod_button, add_images_button, view_images_button, download_link_button, republish_mod_button
    if mods_listbox and mods_listbox.winfo_exists():
        mods_listbox.select_set(0, tk.END)
        # Manually update selection based on currently_visible_mod_data
        selected_mods_for_repair.clear()
        for item in currently_visible_mod_data: # Select all *visible* items
            selected_mods_for_repair.append(item)

        if selected_mods_for_repair:
            update_status(f"تم تحديد جميع المودات المعروضة حاليًا ({len(selected_mods_for_repair)} مود).")
            if repair_mod_button: repair_mod_button.config(state=tk.NORMAL)
            if change_format_button: change_format_button.config(state=tk.NORMAL)
            # Disable buttons that require a single mod selection when multiple mods are selected
            if len(selected_mods_for_repair) == 1:
                if replace_mod_button: replace_mod_button.config(state=tk.NORMAL)
                if add_images_button: add_images_button.config(state=tk.NORMAL)
                if view_images_button: view_images_button.config(state=tk.NORMAL)
                if download_link_button: download_link_button.config(state=tk.NORMAL)
                if republish_mod_button: republish_mod_button.config(state=tk.NORMAL)
            else:
                if replace_mod_button: replace_mod_button.config(state=tk.DISABLED)
                if add_images_button: add_images_button.config(state=tk.DISABLED)
                if view_images_button: view_images_button.config(state=tk.DISABLED)
                if download_link_button: download_link_button.config(state=tk.DISABLED)
                if republish_mod_button: republish_mod_button.config(state=tk.DISABLED)
        else:
            update_status("لا توجد مودات لتحديدها.")
            if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
            if change_format_button: change_format_button.config(state=tk.DISABLED)
            if replace_mod_button: replace_mod_button.config(state=tk.DISABLED)
            if add_images_button: add_images_button.config(state=tk.DISABLED)
            if view_images_button: view_images_button.config(state=tk.DISABLED)
            if download_link_button: download_link_button.config(state=tk.DISABLED)

def handle_deselect_all_mods():
    global mods_listbox, selected_mods_for_repair, repair_mod_button, change_format_button, replace_mod_button, add_images_button, view_images_button, download_link_button, republish_mod_button
    if mods_listbox and mods_listbox.winfo_exists():
        mods_listbox.selection_clear(0, tk.END)
        selected_mods_for_repair.clear()
        update_status("تم إلغاء تحديد جميع المودات.")
        if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
        if change_format_button: change_format_button.config(state=tk.DISABLED)
        if replace_mod_button: replace_mod_button.config(state=tk.DISABLED)
        if add_images_button: add_images_button.config(state=tk.DISABLED)
        if view_images_button: view_images_button.config(state=tk.DISABLED)
        if download_link_button: download_link_button.config(state=tk.DISABLED)
        if republish_mod_button: republish_mod_button.config(state=tk.DISABLED)


def handle_repair_mod_action():
    global selected_mods_for_repair, upload_after_repair_var, repair_mod_button, change_format_button
    if not selected_mods_for_repair:
        messagebox.showwarning("لم يتم التحديد", "الرجاء تحديد مود واحد أو أكثر من القائمة أولاً.")
        return

    perform_upload = upload_after_repair_var.get() if upload_after_repair_var else False

    num_selected = len(selected_mods_for_repair)
    mod_names_preview = ", ".join([mod['name'] for mod in selected_mods_for_repair[:3]])
    if num_selected > 3:
        mod_names_preview += f", ... و {num_selected - 3} مودات أخرى"

    confirm_action_message = f"سيتم محاولة إصلاح {num_selected} مودات محددة: ({mod_names_preview})."
    if perform_upload:
        confirm_action_message += "\nسيتم رفع كل مود ناجح إلى Supabase بامتداده الأصلي (أو .mcaddon إذا تم إصلاحه)"
        confirm_action_message += "، مما سيؤدي إلى حذف النسخة القديمة وتحديث الرابط في قاعدة البيانات لكل مود."
    else:
        confirm_action_message += "\nسيتم حفظ المودات المصلحة محليًا فقط."
    confirm_action_message += "\n\nهل أنت متأكد أنك تريد المتابعة؟"

    confirmation = messagebox.askyesno(f"تأكيد إصلاح {num_selected} مودات", confirm_action_message)
    if not confirmation:
        update_status("تم إلغاء عملية الإصلاح.")
        return

    if REPAIR_GEMINI_CLIENT_OK is False and not configure_repair_gemini_client():
        update_status("!!! تحذير: لم يتم تكوين Gemini بشكل صحيح. قد تكون جودة الإصلاح محدودة.")

    if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
    if change_format_button: change_format_button.config(state=tk.DISABLED)

    update_status(f"\n--- بدء عملية إصلاح لـ {num_selected} مودات ---")

    for mod_data in selected_mods_for_repair:
        mod_id = mod_data['id']
        mod_name = mod_data['name']
        original_mod_url = mod_data['download_url']
        original_file_path_in_storage = mod_data.get('file_path_in_storage')

        action_description = "والرفع" if perform_upload else "(محليًا فقط)"
        update_status(f"\n--- بدء إصلاح المود: {mod_name} (ID: {mod_id}) {action_description} ---")
        update_status(f"رابط/مسار التحميل الأصلي: {original_mod_url}")
        if original_file_path_in_storage:
            update_status(f"المسار المخزن الأصلي للملف (سيتم حذفه إذا نجح الرفع): {original_file_path_in_storage}")

        # Each mod repair runs in its own thread
        run_in_thread(repair_mod_and_optionally_upload_task,
                      mod_id,
                      mod_name,
                      original_mod_url,
                      original_file_path_in_storage,
                      perform_upload,
                      new_extension_for_repack=None, # Explicitly None for repair only
                      skip_gemini_processing=False) # Gemini processing enabled for repair
        time.sleep(0.1) # Small delay to allow thread to start and potentially avoid overwhelming system/API if many selected

    # Re-enable buttons after launching all threads. Individual threads will manage their own completion status.
    # A more sophisticated approach might track thread completion.
    # For now, we assume the user understands operations are backgrounded.
    # if repair_mod_button: repair_mod_button.config(state=tk.NORMAL) # This might be too soon
    # if change_format_button: change_format_button.config(state=tk.NORMAL)
    update_status(f"--- تم إطلاق مهام الإصلاح لـ {num_selected} مودات في الخلفية ---")


def handle_change_format_action():
    global selected_mods_for_repair, upload_after_repair_var, repair_mod_button, change_format_button
    if not selected_mods_for_repair:
        messagebox.showwarning("لم يتم التحديد", "الرجاء تحديد مود واحد أو أكثر من القائمة أولاً.")
        return

    perform_upload = upload_after_repair_var.get() if upload_after_repair_var else False

    # --- Extension Selection (once for all selected mods) ---
    extensions = [".mcaddon", ".mcpack", ".zip"]
    extension_prompt = "اختر الامتداد الجديد للملفات المحددة:\n"
    for i, ext in enumerate(extensions):
        extension_prompt += f"{i+1}. {ext}\n"
    extension_prompt += "\nأدخل رقم الاختيار (مثلاً 1 لـ .mcaddon):"

    choice_input = simpledialog.askstring("اختيار صيغة الملف (لجميع المودات المحددة)", extension_prompt, parent=window)

    if choice_input is None:
        update_status("تم إلغاء تغيير صيغة الملف.")
        return

    try:
        choice_index = int(choice_input.strip()) - 1
        if 0 <= choice_index < len(extensions):
            new_target_extension = extensions[choice_index]
        else:
            messagebox.showerror("اختيار غير صالح", "الرجاء إدخال رقم صالح من القائمة.")
            return
    except ValueError:
        messagebox.showerror("إدخال غير صالح", "الرجاء إدخال رقم.")
        return

    update_status(f"سيتم تغيير امتداد الملفات المحددة إلى: {new_target_extension}")

    # Ask if user wants to force format change even if it's the same format
    force_format_change = messagebox.askyesno(
        "تأكيد إجبار التغيير",
        "هل ترغب في إجبار تغيير الصيغة حتى لو كانت نفس الصيغة الحالية؟\n\n"
        "نعم: سيتم تحميل الملف وتحويله وتحديثه في قاعدة البيانات حتى لو كان بنفس الصيغة.\n"
        "لا: سيتم تخطي الملفات التي هي بالفعل بالصيغة المطلوبة."
    )

    num_selected = len(selected_mods_for_repair)
    mod_names_preview = ", ".join([mod['name'] for mod in selected_mods_for_repair[:3]])
    if num_selected > 3:
        mod_names_preview += f", ... و {num_selected - 3} مودات أخرى"

    confirm_action_message = f"سيتم محاولة تغيير صيغة {num_selected} مودات محددة ({mod_names_preview}) إلى '{new_target_extension}'."
    if perform_upload:
        confirm_action_message += f"\nسيتم رفع كل مود ناجح إلى Supabase بالامتداد الجديد '{new_target_extension}'"
        confirm_action_message += "، مما سيؤدي إلى حذف النسخة القديمة وتحديث الرابط في قاعدة البيانات لكل مود."
    else:
        confirm_action_message += f"\nسيتم حفظ المودات محليًا فقط بالامتداد الجديد '{new_target_extension}'."
    confirm_action_message += "\n\nهل أنت متأكد أنك تريد المتابعة؟"

    confirmation = messagebox.askyesno(f"تأكيد تغيير صيغة {num_selected} مودات", confirm_action_message)
    if not confirmation:
        update_status("تم إلغاء عملية تغيير الصيغة.")
        return

    if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
    if change_format_button: change_format_button.config(state=tk.DISABLED)

    update_status(f"\n--- بدء عملية تغيير الصيغة لـ {num_selected} مودات إلى {new_target_extension} (بشكل تسلسلي) ---")

    # Worker function to process mods sequentially
    def sequential_format_change_worker_task(mods_to_process, target_ext, upload_flag, force_format_change_flag=False):
        skipped_count_worker = 0
        processed_count_worker = 0
        force_format_change = force_format_change_flag  # استخدام القيمة المرسلة من المستخدم

        for mod_data_item in mods_to_process:
            mod_id = mod_data_item['id']
            mod_name = mod_data_item['name']
            original_mod_url = mod_data_item['download_url']
            original_file_path_in_storage = mod_data_item.get('file_path_in_storage')

            current_ext_worker = ""
            if original_mod_url:
                path_part_worker = original_mod_url.split('?')[0]
                _, current_ext_worker = os.path.splitext(path_part_worker)

            normalized_current_ext_worker = current_ext_worker.lower()
            normalized_target_ext_worker = target_ext.lower() # target_ext is already normalized

            # تحقق مما إذا كانت الصيغة الحالية هي نفس الصيغة المطلوبة
            same_extension = normalized_current_ext_worker == normalized_target_ext_worker

            if same_extension and not force_format_change:
                # تخطي المود إذا كانت الصيغة الحالية هي نفس الصيغة المطلوبة وميزة الإجبار غير مفعلة
                update_status(f"--- (تسلسلي) تخطي المود: {mod_name} (ID: {mod_id}) لأنه بالفعل بالصيغة المطلوبة ({target_ext}). ---")
                skipped_count_worker += 1
                continue

            # إذا كانت الصيغة الحالية هي نفس الصيغة المطلوبة ولكن ميزة الإجبار مفعلة
            if same_extension and force_format_change:
                update_status(f"--- (تسلسلي) المود: {mod_name} (ID: {mod_id}) بالفعل بالصيغة المطلوبة ({target_ext})، ولكن سيتم إعادة تحميله وتحديثه. ---")

            action_description_worker = "والرفع" if upload_flag else "(محليًا فقط)"
            update_status(f"\n--- (تسلسلي) بدء تغيير صيغة المود: {mod_name} (ID: {mod_id}) إلى {target_ext} {action_description_worker} ---")
            update_status(f"رابط/مسار التحميل الأصلي: {original_mod_url} (الصيغة الحالية: {current_ext_worker})")
            if original_file_path_in_storage:
                update_status(f"المسار المخزن الأصلي للملف (سيتم حذفه إذا نجح الرفع): {original_file_path_in_storage}")

            # Call the task directly for sequential processing
            repair_mod_and_optionally_upload_task(
                mod_id,
                mod_name,
                original_mod_url,
                original_file_path_in_storage,
                upload_flag, # perform_upload
                new_extension_for_repack=target_ext, # new_target_extension
                skip_gemini_processing=True  # Gemini processing SKIPPED for format change
            )
            processed_count_worker += 1
            update_status(f"--- (تسلسلي) اكتمل تغيير صيغة المود: {mod_name} (ID: {mod_id}) ---")
            # No time.sleep needed here as it's sequential within this worker

        final_status_message_worker = f"--- اكتملت جميع مهام تغيير الصيغة التسلسلية. تم معالجة {processed_count_worker} مود."
        if skipped_count_worker > 0:
            final_status_message_worker += f" تم تخطي {skipped_count_worker} مودات لأنها بالفعل بالصيغة المطلوبة."
        update_status(final_status_message_worker)

        # Re-enable buttons on the main GUI thread
        def _re_enable_buttons_after_sequential_task():
            if 'repair_mod_button' in globals() and repair_mod_button and repair_mod_button.winfo_exists():
                repair_mod_button.config(state=tk.NORMAL if selected_mods_for_repair else tk.DISABLED)
            if 'change_format_button' in globals() and change_format_button and change_format_button.winfo_exists():
                change_format_button.config(state=tk.NORMAL if selected_mods_for_repair else tk.DISABLED)

        if 'window' in globals() and window and window.winfo_exists():
            window.after(0, _re_enable_buttons_after_sequential_task)

    # Prepare arguments for the worker
    # Ensure new_target_extension is normalized before passing
    normalized_new_target_extension = new_target_extension.lower()
    if not normalized_new_target_extension.startswith('.'):
        normalized_new_target_extension = '.' + normalized_new_target_extension

    # Run the sequential worker in a single thread
    run_in_thread(sequential_format_change_worker_task,
                  list(selected_mods_for_repair), # Pass a copy of the list
                  normalized_new_target_extension,
                  perform_upload,
                  force_format_change)

    update_status(f"--- تم إطلاق مهمة تغيير الصيغة التسلسلية في الخلفية لـ {num_selected} مودات ---")
    # The old logic for final_status_message and re-enabling buttons if processed_count == 0 is now handled by the worker.


def copy_log_from_status():
    if not PYPERCLIP_AVAILABLE:
        messagebox.showwarning("غير متوفر", "مكتبة pyperclip غير مثبتة.")
        return
    try:
        log_content = status_text.get("1.0", tk.END).strip()
        if log_content: pyperclip.copy(log_content); update_status("تم نسخ السجل.")
        else: update_status("السجل فارغ.")
    except Exception as e: messagebox.showerror("خطأ", f"فشل نسخ السجل: {e}")


def handle_replace_mod_file():
    global selected_mods_for_repair, upload_after_repair_var, repair_mod_button, change_format_button, replace_mod_button

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لاستبداله.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']
    original_file_path_in_storage = mod_data.get('file_path_in_storage')

    # Get current extension
    current_ext = ""
    if original_mod_url:
        try:
            path_part = original_mod_url.split('?')[0]
            _, current_ext = os.path.splitext(path_part)
        except Exception:
            current_ext = ""

    # Ask user to select a new file
    file_types = [
        ("جميع ملفات المودات", "*.mcaddon;*.mcpack;*.zip"),
        ("ملفات MCADDON", "*.mcaddon"),
        ("ملفات MCPACK", "*.mcpack"),
        ("ملفات ZIP", "*.zip"),
        ("جميع الملفات", "*.*")
    ]

    new_file_path = filedialog.askopenfilename(
        title=f"اختر ملف جديد لاستبدال المود: {mod_name}",
        filetypes=file_types
    )

    if not new_file_path:
        update_status("تم إلغاء عملية استبدال الملف.")
        return

    # Get new file extension
    _, new_ext = os.path.splitext(new_file_path)
    new_ext = new_ext.lower()

    # Ask if user wants to keep the original extension or use the new file's extension
    if current_ext.lower() != new_ext:
        extension_options = [current_ext, new_ext]
        if current_ext.lower() not in ['.mcaddon', '.mcpack', '.zip']:
            extension_options.append('.mcaddon')  # Add default option if current is unknown
        if new_ext.lower() not in ['.mcaddon', '.mcpack', '.zip']:
            extension_options.append('.mcaddon')  # Add default option if new is unknown

        # Remove duplicates and ensure all start with dot
        extension_options = list(set([ext.lower() if ext.startswith('.') else '.' + ext.lower() for ext in extension_options]))

        extension_prompt = "اختر الامتداد النهائي للملف بعد الاستبدال:\n"
        for i, ext in enumerate(extension_options):
            extension_prompt += f"{i+1}. {ext}\n"

        choice_input = simpledialog.askstring(
            "اختيار صيغة الملف النهائية",
            extension_prompt,
            parent=window
        )

        if choice_input is None:
            update_status("تم إلغاء عملية استبدال الملف.")
            return

        try:
            choice_index = int(choice_input.strip()) - 1
            if 0 <= choice_index < len(extension_options):
                final_extension = extension_options[choice_index]
            else:
                messagebox.showerror("اختيار غير صالح", "الرجاء إدخال رقم صالح من القائمة.")
                return
        except ValueError:
            messagebox.showerror("إدخال غير صالح", "الرجاء إدخال رقم.")
            return
    else:
        final_extension = current_ext

    # Confirm upload
    perform_upload = upload_after_repair_var.get() if upload_after_repair_var else False

    confirm_message = f"سيتم استبدال ملف المود '{mod_name}' بالملف الجديد:\n{new_file_path}\n"
    confirm_message += f"وسيتم تغيير امتداده إلى: {final_extension}\n\n"

    if perform_upload:
        confirm_message += "سيتم رفع الملف الجديد إلى Supabase وتحديث رابط التحميل في قاعدة البيانات."
    else:
        confirm_message += "سيتم حفظ الملف الجديد محليًا فقط."

    confirmation = messagebox.askyesno("تأكيد استبدال الملف", confirm_message)
    if not confirmation:
        update_status("تم إلغاء عملية استبدال الملف.")
        return

    # Disable buttons during processing
    if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
    if change_format_button: change_format_button.config(state=tk.DISABLED)
    if replace_mod_button: replace_mod_button.config(state=tk.DISABLED)

    update_status(f"\n--- بدء عملية استبدال ملف المود: {mod_name} (ID: {mod_id}) ---")

    # Process the replacement in a separate thread
    run_in_thread(
        process_mod_replacement_task,
        mod_id,
        mod_name,
        original_mod_url,
        original_file_path_in_storage,
        new_file_path,
        final_extension,
        perform_upload
    )


def process_mod_replacement_task(mod_id, mod_name, original_mod_url, original_file_path_in_storage, new_file_path, target_extension, perform_upload):
    """Process the replacement of a mod file with a new one, optionally uploading to Supabase."""
    temp_extract_dir = f"temp_extract_replace_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"
    local_replaced_filepath = None

    try:
        # Step 1: Validate the new file is a valid ZIP
        update_status(f"الخطوة 1: التحقق من صحة الملف الجديد: {new_file_path}")

        try:
            with open(new_file_path, 'rb') as f:
                new_file_bytes = f.read()

            # Check if it's a valid ZIP file
            try:
                with zipfile.ZipFile(BytesIO(new_file_bytes), 'r') as zip_ref:
                    # Extract to temp directory to verify contents
                    if not os.path.exists(temp_extract_dir):
                        os.makedirs(temp_extract_dir)
                    zip_ref.extractall(temp_extract_dir)
                update_status("تم التحقق من الملف الجديد بنجاح. إنه ملف ZIP صالح.")
            except zipfile.BadZipFile:
                update_status("!!! خطأ: الملف الجديد ليس ملف ZIP صالح. لا يمكن المتابعة.")
                return
        except Exception as e:
            update_status(f"!!! خطأ أثناء قراءة الملف الجديد: {e}")
            return

        # Step 2: Prepare the new file with the target extension
        update_status("الخطوة 2: إعداد الملف الجديد بالامتداد المطلوب...")

        if not os.path.exists(LOCAL_REPAIRED_MODS_DIR):
            os.makedirs(LOCAL_REPAIRED_MODS_DIR)

        sanitized_mod_name = sanitize_filename(mod_name)
        new_filename_base = f"{sanitized_mod_name}_replaced_{int(time.time())}"

        if not target_extension.startswith('.'):
            target_extension = '.' + target_extension

        local_replaced_filename = f"{new_filename_base}{target_extension}"
        local_replaced_filepath = os.path.join(LOCAL_REPAIRED_MODS_DIR, local_replaced_filename)

        # Copy the file with the new extension
        shutil.copy2(new_file_path, local_replaced_filepath)
        update_status(f"تم إعداد الملف الجديد بنجاح: {local_replaced_filepath}")

        if not perform_upload:
            update_status(f"تم حفظ الملف المستبدل محليًا في: {os.path.abspath(local_replaced_filepath)}")
            messagebox.showinfo(
                "اكتمل الاستبدال المحلي",
                f"تم استبدال المود '{mod_name}' محليًا بنجاح.\n\nالملف المحفوظ: {os.path.abspath(local_replaced_filepath)}"
            )
            update_status(f"--- اكتملت عملية الاستبدال المحلي للمود: {mod_name} ---")
            return

        # Step 3: Upload to Supabase if requested
        if not STORAGE_CLIENT_OK or not app_db_client:
            update_status("!!! خطأ: عملاء Supabase غير متاحين للرفع أو تحديث قاعدة البيانات.")
            messagebox.showerror("خطأ Supabase", "عملاء Supabase غير مهيئين بشكل صحيح. لا يمكن المتابعة مع الرفع.")
            return

        update_status(f"الخطوة 3: رفع الملف الجديد '{local_replaced_filename}' إلى Supabase Storage...")
        new_file_path_on_supabase = local_replaced_filename

        try:
            with open(local_replaced_filepath, 'rb') as f:
                upload_response_dict = storage_client.storage.from_(MOD_BUCKET).upload(
                    path=new_file_path_on_supabase,
                    file=f,
                    file_options={"content-type": "application/octet-stream", "cache-control": "3600", "upsert": "false"}
                )
            update_status(f"تم إرسال طلب الرفع. استجابة أولية: {upload_response_dict}")
            update_status(f"تم رفع الملف '{new_file_path_on_supabase}' بنجاح إلى Supabase.")
        except Exception as e:
            update_status(f"!!! خطأ أثناء رفع الملف الجديد إلى Supabase Storage: {e}")
            error_message_detail = str(e)
            if hasattr(e, 'args') and e.args:
                error_message_detail = f"{e} - {e.args}"
            messagebox.showerror("فشل الرفع", f"فشل رفع الملف الجديد إلى Supabase Storage.\nالخطأ: {error_message_detail}")
            return

        # Step 4: Get public URL
        update_status("الخطوة 4: الحصول على الرابط العام للملف الجديد...")
        try:
            new_public_url = storage_client.storage.from_(MOD_BUCKET).get_public_url(new_file_path_on_supabase)
            update_status(f"الرابط العام الجديد: {new_public_url}")
        except Exception as e:
            update_status(f"!!! خطأ أثناء الحصول على الرابط العام للملف الجديد: {e}")
            messagebox.showerror("خطأ في الرابط", f"فشل الحصول على الرابط العام للملف الجديد.\nالخطأ: {e}")
            return

        # Step 5: Update database record
        update_status(f"الخطوة 5: تحديث سجل المود (ID: {mod_id}) في قاعدة البيانات بالرابط الجديد...")
        try:
            update_data = {"download_url": new_public_url}
            db_update_response = app_db_client.table(MODS_TABLE_NAME).update(update_data).eq("id", mod_id).execute()
            if db_update_response.data:
                update_status("تم تحديث سجل المود في قاعدة البيانات بنجاح.")
                # Update in-memory list
                global loaded_mods_data_list
                for i, mod_in_list in enumerate(loaded_mods_data_list):
                    if mod_in_list['id'] == mod_id:
                        loaded_mods_data_list[i]['download_url'] = new_public_url
                        update_status(f"تم تحديث بيانات المود '{mod_name}' (ID: {mod_id}) في القائمة المحملة محلياً.")
                        break
            else:
                error_msg = f"فشل تحديث سجل المود في قاعدة البيانات. لا توجد بيانات في الاستجابة."
                if hasattr(db_update_response, 'error') and db_update_response.error:
                    error_msg += f" تفاصيل الخطأ من Supabase: {db_update_response.error.message}"
                update_status(f"!!! {error_msg}")
                messagebox.showwarning("تحذير تحديث قاعدة البيانات", error_msg + "\nقد تحتاج إلى التحقق يدويًا.")
                return
        except Exception as e:
            update_status(f"!!! خطأ فادح أثناء تحديث سجل المود في قاعدة البيانات: {e}")
            messagebox.showerror("خطأ قاعدة البيانات", f"فشل تحديث سجل المود.\nالخطأ: {e}")
            return

        # Step 6: Delete old file if needed
        path_to_delete = None
        if original_file_path_in_storage:
            path_to_delete = original_file_path_in_storage
        elif original_mod_url:
            try:
                if MOD_BUCKET + "/" in original_mod_url:
                    path_part = original_mod_url.split(MOD_BUCKET + "/", 1)[1]
                    path_to_delete = path_part.split("?")[0]
                else:
                    update_status(f"!!! لا يمكن اشتقاق مسار الملف القديم من الرابط: {original_mod_url} للحذف.")
            except Exception as e_parse:
                update_status(f"!!! خطأ أثناء محاولة اشتقاق مسار الملف القديم: {e_parse}")

        if path_to_delete and path_to_delete != new_file_path_on_supabase:
            update_status(f"الخطوة 6: حذف الملف القديم من Supabase Storage (المسار: {path_to_delete})...")
            try:
                delete_response = storage_client.storage.from_(MOD_BUCKET).remove([path_to_delete])
                update_status(f"استجابة Supabase لطلب الحذف: {delete_response}")
                if delete_response and isinstance(delete_response, list) and delete_response[0].get('error') is None:
                    update_status(f"تم حذف الملف القديم '{path_to_delete}' بنجاح.")
                elif delete_response and isinstance(delete_response, list) and delete_response[0].get('error'):
                    update_status(f"!!! لم يتم حذف الملف القديم '{path_to_delete}'. خطأ من Supabase: {delete_response[0].get('message', 'Unknown error')}")
                elif not delete_response:
                    update_status(f"استجابة الحذف فارغة، قد يكون الملف '{path_to_delete}' لم يُعثر عليه أو تم حذفه مسبقًا.")
                else:
                    update_status(f"!!! استجابة غير متوقعة من Supabase عند محاولة حذف '{path_to_delete}'.")
            except Exception as e:
                update_status(f"!!! خطأ أثناء حذف الملف القديم '{path_to_delete}': {e}")
        elif path_to_delete == new_file_path_on_supabase:
            update_status("!!! تحذير: مسار الملف القديم هو نفسه مسار الملف الجديد. تم تخطي الحذف لمنع فقدان البيانات.")
        else:
            update_status("لم يتم توفير مسار صالح للملف القديم أو لا يمكن اشتقاقه. تم تخطي حذف الملف القديم.")

        # Final success message
        final_message = f"اكتملت عملية استبدال ملف المود '{mod_name}' بنجاح!\n"
        final_message += f"الرابط الجديد: {new_public_url}\n"
        final_message += f"الامتداد الجديد: {target_extension}"
        messagebox.showinfo("اكتملت العملية بنجاح", final_message)
        update_status(f"--- اكتملت عملية استبدال ملف المود: {mod_name} (ID: {mod_id}) ---")

    except Exception as e:
        update_status(f"!!! خطأ فادح وغير متوقع أثناء عملية استبدال الملف للمود {mod_name} (ID: {mod_id}): {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("خطأ فادح", f"حدث خطأ فادح أثناء معالجة المود '{mod_name}'.\nالخطأ: {e}")
    finally:
        # Clean up temp directory
        if os.path.exists(temp_extract_dir):
            try:
                shutil.rmtree(temp_extract_dir)
                update_status(f"تم تنظيف المجلد المؤقت: {temp_extract_dir}")
            except Exception as e_clean:
                update_status(f"!!! خطأ أثناء تنظيف المجلد المؤقت {temp_extract_dir}: {e_clean}")

        # Re-enable buttons
        if 'repair_mod_button' in globals() and repair_mod_button and repair_mod_button.winfo_exists():
            repair_mod_button.config(state=tk.NORMAL)
        if 'change_format_button' in globals() and change_format_button and change_format_button.winfo_exists():
            change_format_button.config(state=tk.NORMAL)
        if 'replace_mod_button' in globals() and replace_mod_button and replace_mod_button.winfo_exists():
            replace_mod_button.config(state=tk.NORMAL)


def repair_mod_and_optionally_upload_task(mod_id, mod_name, mod_url_or_path, original_file_path_in_storage_to_delete, perform_upload, new_extension_for_repack=None, skip_gemini_processing=False):
    temp_extract_dir = f"temp_extract_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"
    gemini_made_changes = False
    local_repaired_filepath = None

    try:
        mod_bytes = None
        update_status(f"الخطوة 1: تحميل ملف المود '{mod_name}' من الرابط '{mod_url_or_path}'...")
        mod_content_stream = BytesIO()
        total_downloaded = 0
        last_prog_update = time.time()
        try:
            response = requests.get(mod_url_or_path, stream=True, timeout=600)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            update_status(f"!!! خطأ فادح أثناء تحميل المود: {e}")
            return

        total_size = int(response.headers.get('content-length', 0))
        update_status(f"حجم الملف المتوقع: {format_size_simple(total_size)}" if total_size else "جاري حساب حجم الملف...")

        for chunk in response.iter_content(chunk_size=8192 * 4):
            if chunk:
                mod_content_stream.write(chunk)
                total_downloaded += len(chunk)
                if time.time() - last_prog_update > 0.5 or total_downloaded == total_size:
                    prog_percent = f" ({(total_downloaded/total_size*100):.1f}%)" if total_size else ""
                    update_status(f"جاري التحميل: {format_size_simple(total_downloaded)}{' / ' + format_size_simple(total_size) if total_size else ''}{prog_percent}")
                    last_prog_update = time.time()
        mod_bytes = mod_content_stream.getvalue()
        update_status(f"اكتمل تحميل الملف. الحجم الكلي: {format_size_simple(len(mod_bytes))}.")

        update_status(f"الخطوة 2: التحقق من ملف ZIP وفك الضغط في المجلد المؤقت: {temp_extract_dir}")
        if not os.path.exists(temp_extract_dir): os.makedirs(temp_extract_dir)
        try:
            with zipfile.ZipFile(BytesIO(mod_bytes), 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            update_status("تم فك ضغط المود بنجاح.")
        except zipfile.BadZipFile:
            update_status("!!! خطأ: الملف الذي تم تحميله ليس ملف ZIP صالحًا. لا يمكن المتابعة.")
            return

        # الخطوة 3: تحديد الملفات وإصلاحها باستخدام Gemini (إذا لم يتم تخطيها)
        if not skip_gemini_processing:
            update_status("الخطوة 3: تحديد وإصلاح ملفات JSON (باستخدام Gemini إذا تم تكوينه)...")
            if REPAIR_GEMINI_CLIENT_OK and repair_gemini_model:
                files_processed_by_gemini = 0
                for root, _, files in os.walk(temp_extract_dir):
                    for filename in files:
                        file_path_abs = os.path.join(root, filename)
                        file_path_in_archive = os.path.relpath(file_path_abs, temp_extract_dir)

                        if filename.lower() == "manifest.json":
                            update_status(f"معالجة ملف 'manifest.json' الرئيسي...")
                            try:
                                with open(file_path_abs, 'r', encoding='utf-8') as f: original_content = f.read()
                                prompt = (
                                    "You are an expert in Minecraft Bedrock Edition add-on development. "
                                    "Analyze and correct the following manifest.json content. "
                                    "Ensure it's valid JSON, follows the correct schema for its format_version, "
                                    "and has valid UUIDs for header and modules. If UUIDs are missing or invalid, generate new ones. "
                                    "Return *only* the corrected JSON object, without any explanations or markdown formatting. "
                                    "If the manifest appears to be for a resource pack, ensure it has a 'header' and a 'modules' section with type 'resources'. "
                                    "If it's for a behavior pack, ensure 'header' and 'modules' with type 'data' or 'script'.\n\n"
                                    f"```json\n{original_content}\n```"
                                )
                                update_status(f"إرسال '{file_path_in_archive}' إلى Gemini للإصلاح...")
                                gemini_response = repair_gemini_model.generate_content(prompt)
                                update_status(f"تم استلام الرد من Gemini بخصوص '{file_path_in_archive}'.")

                                repaired_content_text = gemini_response.text.strip()
                                if repaired_content_text.startswith("```json"): repaired_content_text = repaired_content_text[7:]
                                if repaired_content_text.endswith("```"): repaired_content_text = repaired_content_text[:-3]
                                repaired_content_text = repaired_content_text.strip()

                                json.loads(repaired_content_text) # Validate
                                if repaired_content_text != original_content:
                                    with open(file_path_abs, 'w', encoding='utf-8') as f: f.write(repaired_content_text)
                                    update_status(f"'{file_path_in_archive}' تم إصلاحه وحفظه بنجاح بواسطة Gemini.")
                                    gemini_made_changes = True
                                else:
                                    update_status(f"لم يقم Gemini بإجراء تغييرات على '{file_path_in_archive}'.")
                                files_processed_by_gemini += 1
                            except json.JSONDecodeError as e_json:
                                update_status(f"!!! رد Gemini لـ '{file_path_in_archive}' ليس JSON صالحًا: {e_json}. تم الإبقاء على الملف الأصلي.")
                                if hasattr(gemini_response, 'text'): update_status(f"الرد المستلم: {gemini_response.text[:200]}...")
                            except Exception as e_gemini:
                                update_status(f"!!! خطأ أثناء معالجة '{file_path_in_archive}' بواسطة Gemini: {e_gemini}. تم الإبقاء على الملف الأصلي.")

                        elif filename.lower().endswith(".json"):
                            update_status(f"معالجة ملف JSON إضافي: '{file_path_in_archive}'...")
                            try:
                                with open(file_path_abs, 'r', encoding='utf-8') as f: original_content = f.read()
                                if not (original_content.strip().startswith("{") and original_content.strip().endswith("}")) and \
                                   not (original_content.strip().startswith("[") and original_content.strip().endswith("]")):
                                    update_status(f"تخطي '{file_path_in_archive}', لا يبدو كملف JSON قياسي.")
                                    continue

                                prompt = (
                                    "You are an expert in Minecraft Bedrock Edition add-on development. "
                                    f"The following JSON content is from the file '{file_path_in_archive}'. "
                                    "Analyze and correct this JSON content. Ensure it's valid JSON and adheres to "
                                    "common Minecraft Bedrock Addon schemas for this type of file if discernible from its path/name "
                                    "(e.g., entity, block, item, animation, controller, UI, etc.). "
                                    "Return *only* the corrected JSON object, without any explanations or markdown formatting. "
                                    "If the file content is not JSON or you cannot reliably correct it, "
                                    "return the original content unchanged.\n\n"
                                    f"```json\n{original_content}\n```"
                                )
                                update_status(f"إرسال '{file_path_in_archive}' إلى Gemini للإصلاح...")
                                gemini_response = repair_gemini_model.generate_content(prompt)
                                update_status(f"تم استلام الرد من Gemini بخصوص '{file_path_in_archive}'.")

                                repaired_content_text = gemini_response.text.strip()
                                if repaired_content_text.startswith("```json"): repaired_content_text = repaired_content_text[7:]
                                if repaired_content_text.endswith("```"): repaired_content_text = repaired_content_text[:-3]
                                repaired_content_text = repaired_content_text.strip()

                                if not repaired_content_text:
                                    update_status(f"أعاد Gemini ردًا فارغًا لـ '{file_path_in_archive}'. تم الإبقاء على الملف الأصلي.")
                                    continue

                                json.loads(repaired_content_text) # Validate
                                if repaired_content_text != original_content:
                                    with open(file_path_abs, 'w', encoding='utf-8') as f: f.write(repaired_content_text)
                                    update_status(f"'{file_path_in_archive}' تم إصلاحه وحفظه بنجاح بواسطة Gemini.")
                                    gemini_made_changes = True
                                else:
                                    update_status(f"لم يقم Gemini بإجراء تغييرات على '{file_path_in_archive}'.")
                                files_processed_by_gemini += 1
                            except json.JSONDecodeError as e_json:
                                update_status(f"!!! رد Gemini لـ '{file_path_in_archive}' ليس JSON صالحًا: {e_json}. تم الإبقاء على الملف الأصلي.")
                                if hasattr(gemini_response, 'text'): update_status(f"الرد المستلم: {gemini_response.text[:200]}...")
                            except Exception as e_gemini:
                                update_status(f"!!! خطأ أثناء معالجة '{file_path_in_archive}' بواسطة Gemini: {e_gemini}. تم الإبقاء على الملف الأصلي.")
                if files_processed_by_gemini == 0:
                     update_status("لم يتم العثور على ملفات JSON لمعالجتها بواسطة Gemini (أو لم يتم تكوينه).")
            else:
                update_status("لم يتم تكوين Gemini أو أن النموذج غير متاح. تخطي إصلاح ملفات JSON بواسطة AI.")
        else:
            update_status("تم تخطي خطوة الإصلاح بواسطة Gemini بناءً على الطلب (تغيير الصيغة فقط).")

        # الخطوة 4: إعادة ضغط المود
        update_status("الخطوة 4: إعادة ضغط المود...")
        if not os.path.exists(LOCAL_REPAIRED_MODS_DIR): os.makedirs(LOCAL_REPAIRED_MODS_DIR)

        repaired_filename_base = sanitize_filename(mod_name)

        target_extension = new_extension_for_repack
        if not target_extension:
            _, original_ext = os.path.splitext(mod_url_or_path.split('?')[0])
            if original_ext.lower() in [".mcaddon", ".mcpack"]:
                target_extension = original_ext
            else:
                target_extension = ".mcaddon"
                update_status(f"لم يتم تحديد امتداد جديد أو أن الامتداد الأصلي غير معروف، سيتم استخدام '{target_extension}'.")

        if not target_extension.startswith("."):
            target_extension = "." + target_extension

        suffix = "_retyped" if new_extension_for_repack else "_repaired"
        local_repaired_filename = f"{repaired_filename_base}{suffix}_{int(time.time())}{target_extension}"

        local_repaired_filepath = os.path.join(LOCAL_REPAIRED_MODS_DIR, local_repaired_filename)

        with zipfile.ZipFile(local_repaired_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(temp_extract_dir):
                for file_in_dir in files:
                    file_path_abs = os.path.join(root, file_in_dir)
                    archive_name = os.path.relpath(file_path_abs, temp_extract_dir)
                    zipf.write(file_path_abs, archive_name)

        repacked_size_bytes = os.path.getsize(local_repaired_filepath)
        update_status(f"تم إعادة ضغط المود بنجاح كـ '{local_repaired_filename}' (الحجم: {format_size_simple(repacked_size_bytes)}).")
        update_status(f"المود المُصلح محفوظ محليًا في: {os.path.abspath(local_repaired_filepath)}")

        if not perform_upload:
            summary_action = "تغيير صيغة" if skip_gemini_processing else "إصلاح"
            summary_message = f"اكتملت عملية {summary_action} المود '{mod_name}' محليًا.\n"
            if not skip_gemini_processing: # Only mention Gemini if it was supposed to run
                summary_message += "تم إجراء تغييرات بواسطة Gemini." if gemini_made_changes else "لم يتم إجراء تغييرات بواسطة Gemini أو لم يتطلب الأمر."
            summary_message += f"\n\nالملف المحفوظ: {os.path.abspath(local_repaired_filepath)}"
            messagebox.showinfo(f"اكتمل {summary_action} المحلي", summary_message)
            update_status(f"--- اكتملت عملية {summary_action} المحلي للمود: {mod_name} ---")
            return

        if not STORAGE_CLIENT_OK or not app_db_client:
            update_status("!!! خطأ: عملاء Supabase غير متاحين للرفع أو تحديث قاعدة البيانات.")
            messagebox.showerror("خطأ Supabase", "عملاء Supabase غير مهيئين بشكل صحيح. لا يمكن المتابعة مع الرفع.")
            return

        update_status(f"الخطوة 5: رفع المود الجديد '{local_repaired_filename}' إلى Supabase Storage...")
        new_file_path_on_supabase = local_repaired_filename

        try:
            with open(local_repaired_filepath, 'rb') as f:
                upload_response_dict = storage_client.storage.from_(MOD_BUCKET).upload(
                    path=new_file_path_on_supabase,
                    file=f,
                    file_options={"content-type": "application/vnd.microsoft.appx", "cache-control": "3600", "upsert": "false"}
                )
            update_status(f"تم إرسال طلب الرفع. استجابة أولية: {upload_response_dict}")
            update_status(f"تم رفع الملف '{new_file_path_on_supabase}' بنجاح إلى Supabase.")
        except Exception as e:
            update_status(f"!!! خطأ أثناء رفع المود الجديد إلى Supabase Storage: {e}")
            error_message_detail = str(e)
            if hasattr(e, 'args') and e.args: error_message_detail = f"{e} - {e.args}"
            if "supabase" in str(e).lower() or "storage" in str(e).lower() or hasattr(e, 'code') or hasattr(e, 'details'):
                if hasattr(e, 'message') and e.message: error_message_detail = e.message
                elif e.args and isinstance(e.args[0], dict) and 'message' in e.args[0]: error_message_detail = e.args[0]['message']
            messagebox.showerror("فشل الرفع", f"فشل رفع المود الجديد إلى Supabase Storage.\nالخطأ: {error_message_detail}")
            return

        update_status("الخطوة 6: الحصول على الرابط العام للمود الجديد...")
        try:
            new_public_url = storage_client.storage.from_(MOD_BUCKET).get_public_url(new_file_path_on_supabase)
            update_status(f"الرابط العام الجديد: {new_public_url}")
        except Exception as e:
            update_status(f"!!! خطأ أثناء الحصول على الرابط العام للمود الجديد: {e}")
            messagebox.showerror("خطأ في الرابط", f"فشل الحصول على الرابط العام للمود الجديد.\nالخطأ: {e}")
            return

        update_status(f"الخطوة 7: تحديث سجل المود (ID: {mod_id}) في قاعدة البيانات بالرابط الجديد...")
        try:
            update_data = {"download_url": new_public_url}
            db_update_response = app_db_client.table(MODS_TABLE_NAME).update(update_data).eq("id", mod_id).execute()
            if db_update_response.data:
                update_status("تم تحديث سجل المود في قاعدة البيانات بنجاح.")
                # NEW: Update in-memory list to prevent stale data issues for subsequent operations
                global loaded_mods_data_list # Ensure we are referencing the global list
                for i, mod_in_list in enumerate(loaded_mods_data_list):
                    if mod_in_list['id'] == mod_id:
                        loaded_mods_data_list[i]['download_url'] = new_public_url
                        # If file_path_in_storage was a field and being tracked, it should also be updated here:
                        # e.g., loaded_mods_data_list[i]['file_path_in_storage'] = new_file_path_on_supabase
                        update_status(f"تم تحديث بيانات المود '{mod_name}' (ID: {mod_id}) في القائمة المحملة محلياً.")
                        break
            else:
                error_msg = f"فشل تحديث سجل المود في قاعدة البيانات. لا توجد بيانات في الاستجابة."
                if hasattr(db_update_response, 'error') and db_update_response.error:
                    error_msg += f" تفاصيل الخطأ من Supabase: {db_update_response.error.message}"
                update_status(f"!!! {error_msg}")
                messagebox.showwarning("تحذير تحديث قاعدة البيانات", error_msg + "\nقد تحتاج إلى التحقق يدويًا.")
                return
        except Exception as e:
            update_status(f"!!! خطأ فادح أثناء تحديث سجل المود في قاعدة البيانات: {e}")
            messagebox.showerror("خطأ قاعدة البيانات", f"فشل تحديث سجل المود.\nالخطأ: {e}")
            return

        path_to_delete = None
        if original_file_path_in_storage_to_delete:
            path_to_delete = original_file_path_in_storage_to_delete
        elif mod_url_or_path:
            try:
                # طريقة 1: استخراج المسار من الرابط باستخدام MOD_BUCKET
                if MOD_BUCKET + "/" in mod_url_or_path:
                    path_part = mod_url_or_path.split(MOD_BUCKET + "/", 1)[1]
                    path_to_delete = path_part.split("?")[0]
                    update_status(f"تم استخراج مسار الملف القديم (الطريقة 1): {path_to_delete}")
                # طريقة 2: استخراج اسم الملف من نهاية الرابط
                else:
                    # استخراج اسم الملف من الرابط
                    url_path = urlparse(mod_url_or_path).path
                    filename = os.path.basename(url_path)
                    if filename:
                        path_to_delete = filename.split("?")[0]
                        update_status(f"تم استخراج مسار الملف القديم (الطريقة 2): {path_to_delete}")
                    else:
                        update_status(f"!!! لا يمكن استخراج اسم الملف من الرابط: {mod_url_or_path}")

                # طريقة 3: البحث عن الملف في قائمة الملفات في Supabase
                if not path_to_delete or path_to_delete == "":
                    update_status("محاولة البحث عن الملف في قائمة ملفات Supabase...")
                    try:
                        # الحصول على قائمة الملفات في المجلد
                        file_list = storage_client.storage.from_(MOD_BUCKET).list()
                        update_status(f"تم العثور على {len(file_list)} ملف في المجلد")

                        # استخراج اسم الملف من الرابط بأي طريقة ممكنة
                        url_filename = os.path.basename(urlparse(mod_url_or_path).path).split("?")[0]
                        mod_name_sanitized = sanitize_filename(mod_name).lower()

                        # البحث عن الملف في القائمة
                        for file_info in file_list:
                            file_name = file_info.get('name', '')
                            # البحث باستخدام اسم الملف من الرابط
                            if url_filename and url_filename in file_name:
                                path_to_delete = file_name
                                update_status(f"تم العثور على الملف القديم باستخدام اسم الملف من الرابط: {path_to_delete}")
                                break
                            # البحث باستخدام اسم المود
                            elif mod_name_sanitized and mod_name_sanitized in file_name.lower():
                                path_to_delete = file_name
                                update_status(f"تم العثور على الملف القديم باستخدام اسم المود: {path_to_delete}")
                                break
                    except Exception as e_list:
                        update_status(f"!!! خطأ أثناء محاولة الحصول على قائمة الملفات: {e_list}")

                if not path_to_delete or path_to_delete == "":
                    update_status(f"!!! لا يمكن اشتقاق مسار الملف القديم من الرابط: {mod_url_or_path} للحذف.")
            except Exception as e_parse:
                update_status(f"!!! خطأ أثناء محاولة اشتقاق مسار الملف القديم: {e_parse}")

        if path_to_delete and path_to_delete != new_file_path_on_supabase:
            update_status(f"الخطوة 8: حذف الملف القديم من Supabase Storage (المسار: {path_to_delete})...")
            try:
                # محاولة حذف الملف باستخدام المسار المستخرج
                delete_response = storage_client.storage.from_(MOD_BUCKET).remove([path_to_delete])
                update_status(f"استجابة Supabase لطلب الحذف: {delete_response}")

                # التحقق من نجاح عملية الحذف
                if delete_response and isinstance(delete_response, list) and delete_response[0].get('error') is None:
                    update_status(f"تم حذف الملف القديم '{path_to_delete}' بنجاح.")
                elif delete_response and isinstance(delete_response, list) and delete_response[0].get('error'):
                    error_msg = delete_response[0].get('message', 'Unknown error')
                    update_status(f"!!! لم يتم حذف الملف القديم '{path_to_delete}'. خطأ من Supabase: {error_msg}")

                    # إذا كان الخطأ هو عدم وجود الملف، نحاول البحث عنه بطريقة أخرى
                    if "not found" in str(error_msg).lower() or "not exist" in str(error_msg).lower():
                        update_status("محاولة البحث عن الملف القديم بطريقة أخرى...")
                        try:
                            # الحصول على قائمة الملفات في المجلد
                            file_list = storage_client.storage.from_(MOD_BUCKET).list()

                            # البحث عن الملفات التي تحتوي على اسم المود
                            mod_name_sanitized = sanitize_filename(mod_name).lower()
                            found_files = []

                            for file_info in file_list:
                                file_name = file_info.get('name', '')
                                if mod_name_sanitized in file_name.lower() and file_name != new_file_path_on_supabase:
                                    found_files.append(file_name)

                            if found_files:
                                update_status(f"تم العثور على {len(found_files)} ملفات محتملة للحذف: {found_files}")

                                # حذف جميع الملفات المحتملة
                                for file_to_delete in found_files:
                                    try:
                                        delete_resp = storage_client.storage.from_(MOD_BUCKET).remove([file_to_delete])
                                        if delete_resp and isinstance(delete_resp, list) and delete_resp[0].get('error') is None:
                                            update_status(f"تم حذف الملف '{file_to_delete}' بنجاح.")
                                        else:
                                            update_status(f"فشل حذف الملف '{file_to_delete}': {delete_resp}")
                                    except Exception as e_del:
                                        update_status(f"خطأ أثناء حذف الملف '{file_to_delete}': {e_del}")
                            else:
                                update_status("لم يتم العثور على ملفات محتملة للحذف.")
                        except Exception as e_list:
                            update_status(f"خطأ أثناء البحث عن الملفات: {e_list}")
                elif not delete_response:
                    update_status(f"استجابة الحذف فارغة، قد يكون الملف '{path_to_delete}' لم يُعثر عليه أو تم حذفه مسبقًا.")

                    # محاولة البحث عن الملف بطريقة أخرى
                    update_status("محاولة البحث عن الملف القديم بطريقة أخرى...")
                    try:
                        # الحصول على قائمة الملفات في المجلد
                        file_list = storage_client.storage.from_(MOD_BUCKET).list()

                        # البحث عن الملفات التي تحتوي على اسم المود
                        mod_name_sanitized = sanitize_filename(mod_name).lower()
                        found_files = []

                        for file_info in file_list:
                            file_name = file_info.get('name', '')
                            if mod_name_sanitized in file_name.lower() and file_name != new_file_path_on_supabase:
                                found_files.append(file_name)

                        if found_files:
                            update_status(f"تم العثور على {len(found_files)} ملفات محتملة للحذف: {found_files}")

                            # حذف جميع الملفات المحتملة
                            for file_to_delete in found_files:
                                try:
                                    delete_resp = storage_client.storage.from_(MOD_BUCKET).remove([file_to_delete])
                                    if delete_resp and isinstance(delete_resp, list) and delete_resp[0].get('error') is None:
                                        update_status(f"تم حذف الملف '{file_to_delete}' بنجاح.")
                                    else:
                                        update_status(f"فشل حذف الملف '{file_to_delete}': {delete_resp}")
                                except Exception as e_del:
                                    update_status(f"خطأ أثناء حذف الملف '{file_to_delete}': {e_del}")
                        else:
                            update_status("لم يتم العثور على ملفات محتملة للحذف.")
                    except Exception as e_list:
                        update_status(f"خطأ أثناء البحث عن الملفات: {e_list}")
                else:
                    update_status(f"!!! استجابة غير متوقعة من Supabase عند محاولة حذف '{path_to_delete}'.")
            except Exception as e:
                update_status(f"!!! خطأ أثناء حذف الملف القديم '{path_to_delete}': {e}")

                # محاولة البحث عن الملف بطريقة أخرى في حالة الخطأ
                update_status("محاولة البحث عن الملف القديم بطريقة أخرى بعد الخطأ...")
                try:
                    # الحصول على قائمة الملفات في المجلد
                    file_list = storage_client.storage.from_(MOD_BUCKET).list()

                    # البحث عن الملفات التي تحتوي على اسم المود
                    mod_name_sanitized = sanitize_filename(mod_name).lower()
                    found_files = []

                    for file_info in file_list:
                        file_name = file_info.get('name', '')
                        if mod_name_sanitized in file_name.lower() and file_name != new_file_path_on_supabase:
                            found_files.append(file_name)

                    if found_files:
                        update_status(f"تم العثور على {len(found_files)} ملفات محتملة للحذف: {found_files}")

                        # حذف جميع الملفات المحتملة
                        for file_to_delete in found_files:
                            try:
                                delete_resp = storage_client.storage.from_(MOD_BUCKET).remove([file_to_delete])
                                if delete_resp and isinstance(delete_resp, list) and delete_resp[0].get('error') is None:
                                    update_status(f"تم حذف الملف '{file_to_delete}' بنجاح.")
                                else:
                                    update_status(f"فشل حذف الملف '{file_to_delete}': {delete_resp}")
                            except Exception as e_del:
                                update_status(f"خطأ أثناء حذف الملف '{file_to_delete}': {e_del}")
                    else:
                        update_status("لم يتم العثور على ملفات محتملة للحذف.")
                except Exception as e_list:
                    update_status(f"خطأ أثناء البحث عن الملفات: {e_list}")
        elif path_to_delete == new_file_path_on_supabase:
            update_status("!!! تحذير: مسار الملف القديم هو نفسه مسار الملف الجديد. تم تخطي الحذف لمنع فقدان البيانات.")
        else:
            update_status("لم يتم توفير مسار صالح للملف القديم أو لا يمكن اشتقاقه. محاولة البحث عن الملفات القديمة...")

            # محاولة البحث عن الملفات القديمة باستخدام اسم المود
            try:
                # الحصول على قائمة الملفات في المجلد
                file_list = storage_client.storage.from_(MOD_BUCKET).list()

                # البحث عن الملفات التي تحتوي على اسم المود
                mod_name_sanitized = sanitize_filename(mod_name).lower()
                found_files = []

                for file_info in file_list:
                    file_name = file_info.get('name', '')
                    if mod_name_sanitized in file_name.lower() and file_name != new_file_path_on_supabase:
                        found_files.append(file_name)

                if found_files:
                    update_status(f"تم العثور على {len(found_files)} ملفات محتملة للحذف: {found_files}")

                    # حذف جميع الملفات المحتملة
                    for file_to_delete in found_files:
                        try:
                            delete_resp = storage_client.storage.from_(MOD_BUCKET).remove([file_to_delete])
                            if delete_resp and isinstance(delete_resp, list) and delete_resp[0].get('error') is None:
                                update_status(f"تم حذف الملف '{file_to_delete}' بنجاح.")
                            else:
                                update_status(f"فشل حذف الملف '{file_to_delete}': {delete_resp}")
                        except Exception as e_del:
                            update_status(f"خطأ أثناء حذف الملف '{file_to_delete}': {e_del}")
                else:
                    update_status("لم يتم العثور على ملفات محتملة للحذف.")
            except Exception as e_list:
                update_status(f"خطأ أثناء البحث عن الملفات: {e_list}")

        summary_action = "تغيير صيغة ورفع" if skip_gemini_processing else "إصلاح ورفع"
        final_message = f"اكتملت عملية {summary_action} المود '{mod_name}' بنجاح!\n"
        final_message += f"الرابط الجديد: {new_public_url}\n"
        if not skip_gemini_processing:
            final_message += "تم إجراء تغييرات بواسطة Gemini." if gemini_made_changes else "لم يتم إجراء تغييرات بواسطة Gemini أو لم يتطلب الأمر."
        messagebox.showinfo("اكتملت العملية بنجاح", final_message)
        update_status(f"--- اكتملت عملية {summary_action} للمود: {mod_name} (ID: {mod_id}) ---")

    except Exception as e:
        update_status(f"!!! خطأ فادح وغير متوقع أثناء عملية الإصلاح للمود {mod_name} (ID: {mod_id}): {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("خطأ فادح", f"حدث خطأ فادح أثناء معالجة المود '{mod_name}'.\nالخطأ: {e}")
    finally:
        if os.path.exists(temp_extract_dir):
            try:
                shutil.rmtree(temp_extract_dir)
                update_status(f"تم تنظيف المجلد المؤقت: {temp_extract_dir}")
            except Exception as e_clean:
                update_status(f"!!! خطأ أثناء تنظيف المجلد المؤقت {temp_extract_dir}: {e_clean}")

        if 'repair_mod_button' in globals() and repair_mod_button and repair_mod_button.winfo_exists():
            repair_mod_button.config(state=tk.NORMAL)
        if 'change_format_button' in globals() and change_format_button and change_format_button.winfo_exists():
            change_format_button.config(state=tk.NORMAL)


def sanitize_filename(filename):
    if not filename: return "default_filename"
    name_part, ext_part = os.path.splitext(filename)
    name_part = re.sub(r'[^\w.\-]+', '_', name_part)
    name_part = re.sub(r'_+', '_', name_part)
    name_part = name_part.strip('_')
    name_part = name_part[:100]
    if not name_part: name_part = "sanitized_file"
    return name_part + ext_part

def format_size_simple(size_bytes):
    if size_bytes < 1024: return f"{size_bytes} B"
    elif size_bytes < 1024*1024: return f"{size_bytes/1024:.2f} KB"
    else: return f"{size_bytes/(1024*1024):.2f} MB"

def show_mods_with_zero_downloads():
    """عرض المودات التي لم تحصل على أي تحميل"""
    global loaded_mods_data_list, mods_listbox, currently_visible_mod_data, view_images_button, download_link_button

    # تصفية المودات التي لم تحصل على أي تحميل
    zero_downloads_mods = []
    for mod in loaded_mods_data_list:
        # التحقق من أن عدد التحميلات 0 أو غير موجود
        downloads = mod.get('downloads', 0)
        if downloads == 0 or downloads is None:
            zero_downloads_mods.append(mod)

    # تحديث القائمة بالمودات المصفاة
    mods_listbox.delete(0, tk.END)
    currently_visible_mod_data.clear()

    for mod_item in zero_downloads_mods:
        mod_name = mod_item['name']
        mod_id = mod_item['id']
        download_url = mod_item.get('download_url', '')
        downloads = mod_item.get('downloads', 0)

        file_extension = ""
        if download_url:
            try:
                path_part = download_url.split('?')[0]
                _, file_extension = os.path.splitext(path_part)
            except Exception:
                file_extension = ""

        # Format the display text with extension and icon
        extension_lower = file_extension.lower()
        if extension_lower:
            if extension_lower == '.zip':
                format_icon = "📦" # Box icon for ZIP
            elif extension_lower == '.mcaddon':
                format_icon = "🧩" # Puzzle piece for MCADDON
            elif extension_lower == '.mcpack':
                format_icon = "📚" # Books for MCPACK
            else:
                format_icon = "📄" # Generic document for other formats

            display_text = f"ID: {mod_id} - {format_icon} {mod_name} ({extension_lower}) - التحميلات: {downloads}"
        else:
            display_text = f"ID: {mod_id} - {mod_name} - التحميلات: {downloads}"

        mods_listbox.insert(tk.END, display_text)
        currently_visible_mod_data.append(mod_item)

    update_status(f"تم العثور على {len(zero_downloads_mods)} مود بدون تحميلات")
    handle_deselect_all_mods() # Clear selections and disable buttons

def search_mods(search_term):
    """البحث عن المودات باستخدام مصطلح البحث"""
    global loaded_mods_data_list, mods_listbox

    if not search_term:
        # إذا كان مصطلح البحث فارغًا، أعد تحميل جميع المودات
        populate_mods_listbox("All")
        return

    # تحويل مصطلح البحث إلى أحرف صغيرة للمقارنة غير الحساسة لحالة الأحرف
    search_term = search_term.lower()

    # تصفية المودات التي تطابق مصطلح البحث
    filtered_mods = []
    for mod in loaded_mods_data_list:
        # البحث في اسم المود والوصف والفئة
        if (search_term in mod.get('name', '').lower() or
            search_term in mod.get('description', '').lower() or
            search_term in mod.get('category', '').lower()):
            filtered_mods.append(mod)

    # تحديث القائمة بالمودات المصفاة
    update_mods_listbox(filtered_mods)

    # تحديث حالة التطبيق
    update_status(f"تم العثور على {len(filtered_mods)} مود يطابق مصطلح البحث: '{search_term}'")

def sort_mods_by_date():
    """ترتيب المودات من الأحدث إلى الأقدم"""
    global loaded_mods_data_list, mods_listbox

    # نسخة من القائمة للترتيب
    sorted_mods = loaded_mods_data_list.copy()

    # ترتيب المودات حسب تاريخ الإنشاء أو التحديث (من الأحدث إلى الأقدم)
    sorted_mods.sort(key=lambda x: x.get('created_at', '1970-01-01T00:00:00')
                    if not x.get('updated_at') else x.get('updated_at', '1970-01-01T00:00:00'),
                    reverse=True)

    # تحديث القائمة بالمودات المرتبة
    update_mods_listbox(sorted_mods)

    # تحديث حالة التطبيق
    update_status(f"تم ترتيب {len(sorted_mods)} مود من الأحدث إلى الأقدم")

def update_button_states():
    """تحديث حالة الأزرار بناءً على المودات المحددة"""
    global selected_mods_for_repair, repair_mod_button, change_format_button, replace_mod_button, add_images_button, view_images_button

    # تحقق من وجود الأزرار قبل محاولة تحديثها
    if not all([repair_mod_button, change_format_button, replace_mod_button, add_images_button]):
        return

    # تعطيل جميع الأزرار إذا لم يتم تحديد أي مود
    if not selected_mods_for_repair:
        repair_mod_button.config(state=tk.DISABLED)
        change_format_button.config(state=tk.DISABLED)
        replace_mod_button.config(state=tk.DISABLED)
        add_images_button.config(state=tk.DISABLED)
        if 'view_images_button' in globals() and view_images_button:
            view_images_button.config(state=tk.DISABLED)
        return

    # تمكين زر إصلاح المود إذا تم تحديد مود واحد على الأقل
    repair_mod_button.config(state=tk.NORMAL)

    # تمكين أزرار تغيير الصيغة واستبدال الملف وإضافة الصور وعرض الصور فقط إذا تم تحديد مود واحد
    if len(selected_mods_for_repair) == 1:
        change_format_button.config(state=tk.NORMAL)
        replace_mod_button.config(state=tk.NORMAL)
        add_images_button.config(state=tk.NORMAL)
        if 'view_images_button' in globals() and view_images_button:
            view_images_button.config(state=tk.NORMAL)
    else:
        change_format_button.config(state=tk.DISABLED)
        replace_mod_button.config(state=tk.DISABLED)
        add_images_button.config(state=tk.DISABLED)
        if 'view_images_button' in globals() and view_images_button:
            view_images_button.config(state=tk.DISABLED)

def update_mods_listbox(mods_list):
    """تحديث قائمة المودات في واجهة المستخدم"""
    global mods_listbox, selected_mods_for_repair, loaded_mods_data_list

    # تحديث المتغير العام للمودات المرئية حاليًا
    global currently_visible_mod_data
    currently_visible_mod_data = mods_list

    # مسح القائمة الحالية
    mods_listbox.delete(0, tk.END)

    # إعادة تعيين المودات المحددة
    selected_mods_for_repair = []

    # إضافة المودات المصفاة إلى القائمة
    for mod in mods_list:
        mod_name = mod.get('name', 'بدون اسم')
        mod_category = mod.get('category', 'بدون فئة')
        display_text = f"{mod_name} ({mod_category})"
        mods_listbox.insert(tk.END, display_text)

    # تحديث حالة الأزرار
    update_button_states()

def compress_image(image_data, compression_level='normal'):
    """Compress an image based on the specified compression level.

    Args:
        image_data: Image data as bytes
        compression_level: 'normal', 'medium', or 'high'

    Returns:
        Tuple of (compressed_bytes, format, content_type, original_size, compressed_size)
    """
    from PIL import Image
    from io import BytesIO

    original_size = len(image_data)

    try:
        # Open the image
        img = Image.open(BytesIO(image_data))

        # Check if it's a GIF and animated
        if img.format == 'GIF' and getattr(img, 'is_animated', False):
            # Directly return the result of compress_animated_gif
            return compress_animated_gif(image_data, compression_level)

        # Define compression parameters based on level
        if compression_level == 'high':
            # High compression (smallest file size, lowest quality)
            jpeg_quality = 60
            png_compression = 9
            webp_quality = 60
            resize_factor = 0.8  # Reduce dimensions to 80%
        elif compression_level == 'medium':
            # Medium compression
            jpeg_quality = 75
            png_compression = 7
            webp_quality = 75
            resize_factor = 0.9  # Reduce dimensions to 90%
        else:  # 'normal' or any other value
            # Normal compression (better quality, larger file size)
            jpeg_quality = 85
            png_compression = 6
            webp_quality = 85
            resize_factor = 1.0  # No resize

        # Resize image if needed
        if resize_factor < 1.0:
            new_width = int(img.width * resize_factor)
            new_height = int(img.height * resize_factor)
            img = img.resize((new_width, new_height), Image.LANCZOS)

        # Determine best format based on image content
        img_format = img.format if img.format else 'JPEG'
        if img_format.upper() in ['PNG', 'WEBP', 'JPEG', 'JPG']:
            img_format_save = img_format.upper()
        else:
            # Default to JPEG for most images
            img_format_save = 'JPEG'

        # Prepare compression parameters
        output_buffer = BytesIO()
        save_kwargs = {'format': img_format_save}

        if img_format_save == 'JPEG':
            save_kwargs['quality'] = jpeg_quality
            save_kwargs['optimize'] = True
        elif img_format_save == 'PNG':
            save_kwargs['optimize'] = True
            save_kwargs['compress_level'] = png_compression
        elif img_format_save == 'WEBP':
            save_kwargs['quality'] = webp_quality

        # Save compressed image
        img.save(output_buffer, **save_kwargs)
        compressed_bytes = output_buffer.getvalue()
        compressed_size = len(compressed_bytes)

        # Return compressed image data and metadata
        content_type = f'image/{img_format_save.lower()}'
        return compressed_bytes, img_format_save.lower(), content_type, original_size, compressed_size

    except Exception as e:
        update_status(f"خطأ في ضغط الصورة: {e}")
        # Return original image if compression fails
        return image_data, None, None, original_size, original_size

def compress_animated_gif(gif_data, compression_level='normal'):
    """Compress an animated GIF while preserving animation.

    Args:
        gif_data: GIF image data as bytes
        compression_level: 'normal', 'medium', or 'high'

    Returns:
        Tuple of (compressed_bytes, format, content_type, original_size, compressed_size)
    """
    from PIL import Image
    from io import BytesIO

    original_size = len(gif_data)

    try:
        # Open the GIF
        gif = Image.open(BytesIO(gif_data))

        # Check if it's actually a GIF and has multiple frames
        if gif.format != 'GIF' or not getattr(gif, 'is_animated', False):
            # Not an animated GIF, use regular compression
            update_status("GIF غير متحرك، سيتم استخدام الضغط العادي.")
            return compress_image(gif_data, compression_level)

        # Define compression parameters based on level
        if compression_level == 'high':
            resize_factor = 0.7  # Reduce dimensions to 70%
            colors = 128  # Reduce color palette
        elif compression_level == 'medium':
            resize_factor = 0.8  # Reduce dimensions to 80%
            colors = 192  # Reduce color palette
        else:  # 'normal' or any other value
            resize_factor = 0.9  # Reduce dimensions to 90%
            colors = 256  # Keep full color palette

        update_status(f"ضغط GIF متحرك بمستوى {compression_level} (عامل التحجيم: {resize_factor}, الألوان: {colors})")

        # Get original dimensions
        original_width, original_height = gif.size

        # Calculate new dimensions
        new_width = int(original_width * resize_factor)
        new_height = int(original_height * resize_factor)

        # Create a new GIF with the same parameters
        frames = []
        durations = []

        # Process each frame
        try:
            i = 0
            while True:
                gif.seek(i)

                # Get frame duration
                duration = gif.info.get('duration', 100)  # Default to 100ms if not specified
                durations.append(duration)

                # Resize frame
                frame = gif.copy()
                if resize_factor < 1.0:
                    frame = frame.resize((new_width, new_height), Image.LANCZOS)

                # Convert to P mode with optimized palette if needed
                if frame.mode != 'P':
                    frame = frame.convert('P', palette=Image.ADAPTIVE, colors=colors)

                frames.append(frame)
                i += 1
        except EOFError:
            # End of frames
            pass

        # Save the compressed GIF
        output_buffer = BytesIO()

        # Save the first frame
        frames[0].save(
            output_buffer,
            format='GIF',
            save_all=True,
            append_images=frames[1:],
            optimize=True,
            duration=durations,
            loop=0,  # Loop forever
            disposal=2  # Restore to background color
        )

        compressed_bytes = output_buffer.getvalue()
        compressed_size = len(compressed_bytes)

        # Return compressed GIF data
        update_status(f"تم ضغط GIF متحرك: الحجم الأصلي: {format_size_simple(original_size)}, الحجم المضغوط: {format_size_simple(compressed_size)}")
        return compressed_bytes, 'gif', 'image/gif', original_size, compressed_size

    except Exception as e:
        update_status(f"خطأ في ضغط GIF متحرك: {e}")
        # Return original image if compression fails
        return gif_data, 'gif', 'image/gif', original_size, original_size

# --- Main GUI Setup ---
def handle_open_mod_download_link():
    """فتح رابط تحميل المود المحدد في المتصفح"""
    global selected_mods_for_repair

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لفتح رابط التحميل الخاص به.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_name = mod_data['name']
    download_url = mod_data.get('download_url', '')

    if not download_url:
        messagebox.showwarning("رابط غير متوفر", f"المود '{mod_name}' ليس له رابط تحميل متوفر.")
        return

    try:
        # فتح الرابط في المتصفح الافتراضي
        update_status(f"جاري فتح رابط تحميل المود '{mod_name}' في المتصفح...")
        webbrowser.open(download_url)
        update_status(f"تم فتح رابط التحميل: {download_url}")
    except Exception as e:
        update_status(f"!!! خطأ أثناء فتح رابط التحميل: {e}")
        messagebox.showerror("خطأ", f"فشل فتح رابط التحميل: {e}")

def handle_republish_mod():
    """إعادة نشر المود المحدد من الصفر"""
    global selected_mods_for_repair, upload_after_repair_var, repair_mod_button, change_format_button, republish_mod_button

    if not selected_mods_for_repair or len(selected_mods_for_repair) != 1:
        messagebox.showwarning("تحديد غير صالح", "الرجاء تحديد مود واحد فقط لإعادة نشره.")
        return

    mod_data = selected_mods_for_repair[0]
    mod_id = mod_data['id']
    mod_name = mod_data['name']
    original_mod_url = mod_data['download_url']
    original_file_path_in_storage = mod_data.get('file_path_in_storage')

    # التحقق من صيغة الملف الحالية
    current_ext = ""
    if original_mod_url:
        try:
            path_part = original_mod_url.split('?')[0]
            _, current_ext = os.path.splitext(path_part)
        except Exception:
            current_ext = ""

    # اختيار الصيغة المناسبة للنشر
    valid_extensions = ['.mcaddon', '.mcpack']
    target_extension = ""

    if current_ext.lower() in valid_extensions:
        target_extension = current_ext
    else:
        # اختيار صيغة افتراضية إذا كانت الصيغة الحالية غير صالحة
        extension_prompt = "اختر الصيغة المناسبة لإعادة نشر المود:\n"
        extension_prompt += "1. .mcaddon (للمودات الكاملة)\n"
        extension_prompt += "2. .mcpack (لحزم الموارد أو السلوك)"

        choice_input = simpledialog.askstring(
            "اختيار صيغة المود",
            extension_prompt,
            parent=window
        )

        if choice_input is None:
            update_status("تم إلغاء عملية إعادة النشر.")
            return

        try:
            choice_index = int(choice_input.strip()) - 1
            if 0 <= choice_index < len(valid_extensions):
                target_extension = valid_extensions[choice_index]
            else:
                messagebox.showerror("اختيار غير صالح", "الرجاء إدخال رقم صالح من القائمة.")
                return
        except ValueError:
            messagebox.showerror("إدخال غير صالح", "الرجاء إدخال رقم.")
            return

    # تأكيد العملية
    confirm_message = f"سيتم إعادة نشر المود '{mod_name}' بالخطوات التالية:\n\n"
    confirm_message += "1. تحميل الملف الحالي\n"
    confirm_message += "2. حذف المود من قاعدة البيانات\n"
    confirm_message += "3. حذف جميع الملفات المرتبطة به من التخزين\n"
    confirm_message += f"4. إعادة نشر المود بصيغة {target_extension}\n\n"
    confirm_message += "هل أنت متأكد من المتابعة؟"

    confirmation = messagebox.askyesno("تأكيد إعادة النشر", confirm_message)
    if not confirmation:
        update_status("تم إلغاء عملية إعادة النشر.")
        return

    # تعطيل الأزرار أثناء المعالجة
    if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
    if change_format_button: change_format_button.config(state=tk.DISABLED)
    if republish_mod_button: republish_mod_button.config(state=tk.DISABLED)

    update_status(f"\n--- بدء عملية إعادة نشر المود: {mod_name} (ID: {mod_id}) ---")

    # تنفيذ العملية في خلفية
    run_in_thread(
        republish_mod_task,
        mod_id,
        mod_name,
        original_mod_url,
        original_file_path_in_storage,
        target_extension
    )

def republish_mod_task(mod_id, mod_name, mod_url, original_file_path_in_storage, target_extension):
    """مهمة إعادة نشر المود"""
    temp_extract_dir = f"temp_republish_{sanitize_filename(mod_name)}_{mod_id}_{int(time.time())}"
    local_repaired_filepath = None

    try:
        # الخطوة 1: تحميل ملف المود
        update_status(f"الخطوة 1: تحميل ملف المود '{mod_name}' من الرابط '{mod_url}'...")
        mod_content_stream = BytesIO()
        total_downloaded = 0
        last_prog_update = time.time()

        try:
            response = requests.get(mod_url, stream=True, timeout=600)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            update_status(f"!!! خطأ فادح أثناء تحميل المود: {e}")
            _re_enable_buttons_after_republish()
            return

        total_size = int(response.headers.get('content-length', 0))
        update_status(f"حجم الملف المتوقع: {format_size_simple(total_size)}" if total_size else "جاري حساب حجم الملف...")

        for chunk in response.iter_content(chunk_size=8192 * 4):
            if chunk:
                mod_content_stream.write(chunk)
                total_downloaded += len(chunk)
                if time.time() - last_prog_update > 0.5 or total_downloaded == total_size:
                    prog_percent = f" ({(total_downloaded/total_size*100):.1f}%)" if total_size else ""
                    update_status(f"جاري التحميل: {format_size_simple(total_downloaded)}{' / ' + format_size_simple(total_size) if total_size else ''}{prog_percent}")
                    last_prog_update = time.time()

        mod_bytes = mod_content_stream.getvalue()
        update_status(f"اكتمل تحميل الملف. الحجم الكلي: {format_size_simple(len(mod_bytes))}.")

        # الخطوة 2: التحقق من ملف ZIP وفك الضغط
        update_status(f"الخطوة 2: التحقق من ملف ZIP وفك الضغط في المجلد المؤقت: {temp_extract_dir}")
        if not os.path.exists(temp_extract_dir): os.makedirs(temp_extract_dir)

        try:
            with zipfile.ZipFile(BytesIO(mod_bytes), 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            update_status("تم فك ضغط المود بنجاح.")
        except zipfile.BadZipFile:
            update_status("!!! خطأ: الملف الذي تم تحميله ليس ملف ZIP صالحًا. لا يمكن المتابعة.")
            _re_enable_buttons_after_republish()
            return

        # الخطوة 3: حذف المود من قاعدة البيانات
        update_status(f"الخطوة 3: حذف المود (ID: {mod_id}) من قاعدة البيانات...")
        try:
            delete_response = app_db_client.table(MODS_TABLE_NAME).delete().eq("id", mod_id).execute()
            if delete_response.data:
                update_status(f"تم حذف المود '{mod_name}' (ID: {mod_id}) من قاعدة البيانات بنجاح.")
            else:
                update_status(f"!!! تحذير: لم يتم العثور على المود (ID: {mod_id}) في قاعدة البيانات أو حدث خطأ أثناء الحذف.")
        except Exception as e:
            update_status(f"!!! خطأ أثناء حذف المود من قاعدة البيانات: {e}")
            # نستمر في العملية حتى لو فشل الحذف

        # الخطوة 4: حذف جميع الملفات المرتبطة بالمود من التخزين
        update_status("الخطوة 4: حذف جميع الملفات المرتبطة بالمود من التخزين...")

        # محاولة حذف الملف الرئيسي
        path_to_delete = None
        if original_file_path_in_storage:
            path_to_delete = original_file_path_in_storage
        elif mod_url:
            try:
                if MOD_BUCKET + "/" in mod_url:
                    path_part = mod_url.split(MOD_BUCKET + "/", 1)[1]
                    path_to_delete = path_part.split("?")[0]
                else:
                    url_path = urlparse(mod_url).path
                    filename = os.path.basename(url_path)
                    if filename:
                        path_to_delete = filename.split("?")[0]
            except Exception as e_parse:
                update_status(f"!!! خطأ أثناء محاولة اشتقاق مسار الملف القديم: {e_parse}")

        if path_to_delete:
            try:
                delete_response = storage_client.storage.from_(MOD_BUCKET).remove([path_to_delete])
                update_status(f"محاولة حذف الملف الرئيسي: {path_to_delete}")
                if delete_response and isinstance(delete_response, list) and delete_response[0].get('error') is None:
                    update_status(f"تم حذف الملف الرئيسي '{path_to_delete}' بنجاح.")
                else:
                    update_status(f"!!! تحذير: فشل حذف الملف الرئيسي '{path_to_delete}' أو لم يتم العثور عليه.")
            except Exception as e:
                update_status(f"!!! خطأ أثناء حذف الملف الرئيسي: {e}")

        # البحث عن جميع الملفات المرتبطة بالمود وحذفها
        try:
            # الحصول على قائمة الملفات في المجلد
            file_list = storage_client.storage.from_(MOD_BUCKET).list()
            update_status(f"تم العثور على {len(file_list)} ملف في المجلد")

            # البحث عن الملفات التي تحتوي على اسم المود
            mod_name_sanitized = sanitize_filename(mod_name).lower()
            found_files = []

            for file_info in file_list:
                file_name = file_info.get('name', '')
                if mod_name_sanitized in file_name.lower():
                    found_files.append(file_name)

            if found_files:
                update_status(f"تم العثور على {len(found_files)} ملفات محتملة للحذف: {found_files}")

                # حذف جميع الملفات المحتملة
                for file_to_delete in found_files:
                    try:
                        delete_resp = storage_client.storage.from_(MOD_BUCKET).remove([file_to_delete])
                        if delete_resp and isinstance(delete_resp, list) and delete_resp[0].get('error') is None:
                            update_status(f"تم حذف الملف '{file_to_delete}' بنجاح.")
                        else:
                            update_status(f"فشل حذف الملف '{file_to_delete}': {delete_resp}")
                    except Exception as e_del:
                        update_status(f"خطأ أثناء حذف الملف '{file_to_delete}': {e_del}")
            else:
                update_status("لم يتم العثور على ملفات إضافية محتملة للحذف.")
        except Exception as e_list:
            update_status(f"خطأ أثناء البحث عن الملفات: {e_list}")

        # الخطوة 5: إعادة ضغط المود بالصيغة المطلوبة
        update_status(f"الخطوة 5: إعادة ضغط المود بصيغة {target_extension}...")
        if not os.path.exists(LOCAL_REPAIRED_MODS_DIR): os.makedirs(LOCAL_REPAIRED_MODS_DIR)

        repaired_filename_base = sanitize_filename(mod_name)
        local_repaired_filename = f"{repaired_filename_base}_republished_{int(time.time())}{target_extension}"
        local_repaired_filepath = os.path.join(LOCAL_REPAIRED_MODS_DIR, local_repaired_filename)

        with zipfile.ZipFile(local_repaired_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(temp_extract_dir):
                for file_in_dir in files:
                    file_path_abs = os.path.join(root, file_in_dir)
                    archive_name = os.path.relpath(file_path_abs, temp_extract_dir)
                    zipf.write(file_path_abs, archive_name)

        repacked_size_bytes = os.path.getsize(local_repaired_filepath)
        update_status(f"تم إعادة ضغط المود بنجاح كـ '{local_repaired_filename}' (الحجم: {format_size_simple(repacked_size_bytes)}).")
        update_status(f"المود المُعاد نشره محفوظ محليًا في: {os.path.abspath(local_repaired_filepath)}")

        # الخطوة 6: رفع المود الجديد إلى Supabase
        update_status(f"الخطوة 6: رفع المود الجديد '{local_repaired_filename}' إلى Supabase Storage...")
        new_file_path_on_supabase = local_repaired_filename

        try:
            with open(local_repaired_filepath, 'rb') as f:
                upload_response_dict = storage_client.storage.from_(MOD_BUCKET).upload(
                    path=new_file_path_on_supabase,
                    file=f,
                    file_options={"content-type": "application/vnd.microsoft.appx", "cache-control": "3600", "upsert": "false"}
                )
            update_status(f"تم إرسال طلب الرفع. استجابة أولية: {upload_response_dict}")
            update_status(f"تم رفع الملف '{new_file_path_on_supabase}' بنجاح إلى Supabase.")
        except Exception as e:
            update_status(f"!!! خطأ أثناء رفع المود الجديد إلى Supabase Storage: {e}")
            error_message_detail = str(e)
            if hasattr(e, 'args') and e.args: error_message_detail = f"{e} - {e.args}"
            messagebox.showerror("فشل الرفع", f"فشل رفع المود الجديد إلى Supabase Storage.\nالخطأ: {error_message_detail}")
            _re_enable_buttons_after_republish()
            return

        # الخطوة 7: الحصول على الرابط العام للمود الجديد
        update_status("الخطوة 7: الحصول على الرابط العام للمود الجديد...")
        try:
            new_public_url = storage_client.storage.from_(MOD_BUCKET).get_public_url(new_file_path_on_supabase)
            update_status(f"الرابط العام الجديد: {new_public_url}")
        except Exception as e:
            update_status(f"!!! خطأ أثناء الحصول على الرابط العام للمود الجديد: {e}")
            messagebox.showerror("خطأ في الرابط", f"فشل الحصول على الرابط العام للمود الجديد.\nالخطأ: {e}")
            _re_enable_buttons_after_republish()
            return

        # الخطوة 8: إعادة إنشاء سجل المود في قاعدة البيانات
        update_status("الخطوة 8: إعادة إنشاء سجل المود في قاعدة البيانات...")
        try:
            # استخدام نفس معرف المود السابق للحفاظ على التوافق
            new_mod_data = {
                "id": mod_id,
                "name": mod_name,
                "download_url": new_public_url,
                # إعادة تعيين عدادات التحميلات والإعجابات
                "downloads": 0,
                "likes": 0,
                "clicks": 0
            }

            # إذا كانت هناك بيانات إضافية في المود الأصلي، يمكن استخدامها هنا
            if 'description' in selected_mods_for_repair[0]:
                new_mod_data['description'] = selected_mods_for_repair[0]['description']
            if 'category' in selected_mods_for_repair[0]:
                new_mod_data['category'] = selected_mods_for_repair[0]['category']
            if 'image_urls' in selected_mods_for_repair[0]:
                new_mod_data['image_urls'] = selected_mods_for_repair[0]['image_urls']
            if 'version' in selected_mods_for_repair[0]:
                new_mod_data['version'] = selected_mods_for_repair[0]['version']
            if 'size' in selected_mods_for_repair[0]:
                new_mod_data['size'] = format_size_simple(repacked_size_bytes)

            db_insert_response = app_db_client.table(MODS_TABLE_NAME).insert(new_mod_data).execute()

            if db_insert_response.data:
                update_status("تم إعادة إنشاء سجل المود في قاعدة البيانات بنجاح.")

                # تحديث القائمة المحلية
                global loaded_mods_data_list
                for i, mod_in_list in enumerate(loaded_mods_data_list):
                    if mod_in_list['id'] == mod_id:
                        loaded_mods_data_list[i] = new_mod_data
                        update_status(f"تم تحديث بيانات المود '{mod_name}' (ID: {mod_id}) في القائمة المحملة محلياً.")
                        break
            else:
                error_msg = f"فشل إعادة إنشاء سجل المود في قاعدة البيانات. لا توجد بيانات في الاستجابة."
                if hasattr(db_insert_response, 'error') and db_insert_response.error:
                    error_msg += f" تفاصيل الخطأ من Supabase: {db_insert_response.error.message}"
                update_status(f"!!! {error_msg}")
                messagebox.showwarning("تحذير تحديث قاعدة البيانات", error_msg + "\nقد تحتاج إلى التحقق يدويًا.")
                _re_enable_buttons_after_republish()
                return
        except Exception as e:
            update_status(f"!!! خطأ فادح أثناء إعادة إنشاء سجل المود في قاعدة البيانات: {e}")
            messagebox.showerror("خطأ قاعدة البيانات", f"فشل إعادة إنشاء سجل المود.\nالخطأ: {e}")
            _re_enable_buttons_after_republish()
            return

        # رسالة النجاح النهائية
        final_message = f"اكتملت عملية إعادة نشر المود '{mod_name}' بنجاح!\n"
        final_message += f"الرابط الجديد: {new_public_url}\n"
        final_message += f"الصيغة الجديدة: {target_extension}\n"
        final_message += "تم إعادة تعيين عدادات التحميلات والإعجابات."
        messagebox.showinfo("اكتملت العملية بنجاح", final_message)
        update_status(f"--- اكتملت عملية إعادة نشر المود: {mod_name} (ID: {mod_id}) ---")

    except Exception as e:
        update_status(f"!!! خطأ فادح وغير متوقع أثناء عملية إعادة النشر للمود {mod_name} (ID: {mod_id}): {e}")
        import traceback
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("خطأ فادح", f"حدث خطأ فادح أثناء معالجة المود '{mod_name}'.\nالخطأ: {e}")
    finally:
        # تنظيف المجلد المؤقت
        if os.path.exists(temp_extract_dir):
            try:
                shutil.rmtree(temp_extract_dir)
                update_status(f"تم تنظيف المجلد المؤقت: {temp_extract_dir}")
            except Exception as e_clean:
                update_status(f"!!! خطأ أثناء تنظيف المجلد المؤقت {temp_extract_dir}: {e_clean}")

        # إعادة تفعيل الأزرار
        _re_enable_buttons_after_republish()

def _re_enable_buttons_after_republish():
    """إعادة تفعيل الأزرار بعد عملية إعادة النشر"""
    if 'repair_mod_button' in globals() and repair_mod_button and repair_mod_button.winfo_exists():
        repair_mod_button.config(state=tk.NORMAL if selected_mods_for_repair else tk.DISABLED)
    if 'change_format_button' in globals() and change_format_button and change_format_button.winfo_exists():
        change_format_button.config(state=tk.NORMAL if selected_mods_for_repair else tk.DISABLED)
    if 'republish_mod_button' in globals() and republish_mod_button and republish_mod_button.winfo_exists():
        republish_mod_button.config(state=tk.NORMAL if selected_mods_for_repair and len(selected_mods_for_repair) == 1 else tk.DISABLED)

def main_gui():
    global window, mods_listbox, status_text, load_mods_button, repair_mod_button, change_format_button, replace_mod_button, add_images_button, upload_after_repair_var, select_all_button, deselect_all_button, download_link_button, view_images_button, republish_mod_button

    window = tk.Tk()
    window.title("أداة إصلاح وتغيير صيغة المودات")
    window.geometry("1000x700")  # Increased width to accommodate new button

    top_controls_frame = ttk.Frame(window, padding="10")
    top_controls_frame.pack(fill=tk.X)

    load_mods_button = ttk.Button(top_controls_frame, text="تحميل قائمة المودات", command=handle_load_published_mods)
    load_mods_button.pack(side=tk.LEFT, padx=2)

    select_all_button = ttk.Button(top_controls_frame, text="تحديد الكل", command=handle_select_all_mods)
    select_all_button.pack(side=tk.LEFT, padx=2)

    deselect_all_button = ttk.Button(top_controls_frame, text="إلغاء تحديد الكل", command=handle_deselect_all_mods)
    deselect_all_button.pack(side=tk.LEFT, padx=2)

    repair_mod_button = ttk.Button(top_controls_frame, text="إصلاح المحدد", command=handle_repair_mod_action, state=tk.DISABLED)
    repair_mod_button.pack(side=tk.LEFT, padx=2)

    change_format_button = ttk.Button(top_controls_frame, text="تغيير صيغة المحدد", command=handle_change_format_action, state=tk.DISABLED)
    change_format_button.pack(side=tk.LEFT, padx=2)

    replace_mod_button = ttk.Button(top_controls_frame, text="استبدال ملف المود", command=handle_replace_mod_file, state=tk.DISABLED)
    replace_mod_button.pack(side=tk.LEFT, padx=2)

    add_images_button = ttk.Button(top_controls_frame, text="إضافة صور للمود", command=handle_add_images_to_mod, state=tk.DISABLED)
    add_images_button.pack(side=tk.LEFT, padx=2)

    view_images_button = ttk.Button(top_controls_frame, text="عرض صور المود", command=handle_view_mod_images, state=tk.DISABLED)
    view_images_button.pack(side=tk.LEFT, padx=2)

    download_link_button = ttk.Button(top_controls_frame, text="فتح رابط التحميل", command=handle_open_mod_download_link, state=tk.DISABLED)
    download_link_button.pack(side=tk.LEFT, padx=2)

    republish_mod_button = ttk.Button(top_controls_frame, text="إعادة نشر المود", command=handle_republish_mod, state=tk.DISABLED)
    republish_mod_button.pack(side=tk.LEFT, padx=2)

    upload_after_repair_var = tk.BooleanVar(value=True)
    upload_checkbox = ttk.Checkbutton(top_controls_frame, text="رفع إلى Supabase بعد المعالجة (يحذف القديم)", variable=upload_after_repair_var)
    upload_checkbox.pack(side=tk.LEFT, padx=5)

    # --- Filter and Search Frame ---
    filter_frame = ttk.Frame(window, padding="10")
    filter_frame.pack(fill=tk.X)

    # إضافة مربع البحث
    search_frame = ttk.Frame(filter_frame)
    search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

    ttk.Label(search_frame, text="بحث:").pack(side=tk.LEFT, padx=(0, 5))
    search_entry = ttk.Entry(search_frame, width=30)
    search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

    search_button = ttk.Button(search_frame, text="بحث",
                              command=lambda: search_mods(search_entry.get()))
    search_button.pack(side=tk.LEFT)

    # إضافة زر الترتيب من الأحدث إلى الأقدم
    sort_button = ttk.Button(filter_frame, text="ترتيب من الأحدث إلى الأقدم",
                            command=sort_mods_by_date)
    sort_button.pack(side=tk.RIGHT, padx=5)

    ttk.Label(filter_frame, text="تصفية حسب الفئة:").pack(side=tk.LEFT, padx=(0,5))

    all_button = ttk.Button(filter_frame, text="الكل", command=lambda: populate_mods_listbox("All"))
    all_button.pack(side=tk.LEFT, padx=2)

    # Assuming category names like 'Shaders', 'Packs', 'Addons'. Adjust if different.
    shaders_button = ttk.Button(filter_frame, text="شادر", command=lambda: populate_mods_listbox("Shaders")) # Or "Shader"
    shaders_button.pack(side=tk.LEFT, padx=2)

    packs_button = ttk.Button(filter_frame, text="باكات", command=lambda: populate_mods_listbox("Packs")) # Or "Texture Packs"
    packs_button.pack(side=tk.LEFT, padx=2)

    addons_button = ttk.Button(filter_frame, text="إضافات", command=lambda: populate_mods_listbox("Addons")) # Or "Addon"
    addons_button.pack(side=tk.LEFT, padx=2)

    # Add button for mods with zero downloads
    zero_downloads_button = ttk.Button(filter_frame, text="بدون تحميلات", command=show_mods_with_zero_downloads)
    zero_downloads_button.pack(side=tk.LEFT, padx=2)

    # You can add more buttons for other categories if needed

    list_frame = ttk.LabelFrame(window, text="المودات المنشورة (اختر مودًا أو أكثر)", padding="10") # Updated text
    list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    mods_listbox_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL)
    mods_listbox = tk.Listbox(list_frame, yscrollcommand=mods_listbox_scrollbar.set, exportselection=False, height=10, selectmode=tk.EXTENDED) # Added selectmode=tk.EXTENDED
    mods_listbox_scrollbar.config(command=mods_listbox.yview)
    mods_listbox_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    mods_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    mods_listbox.bind('<<ListboxSelect>>', on_mod_select)

    status_frame = ttk.LabelFrame(window, text="سجل الحالة", padding="10")
    status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    status_text = scrolledtext.ScrolledText(status_frame, wrap=tk.WORD, height=15, state=tk.DISABLED, font=("Segoe UI", 9))
    status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0,5))
    copy_log_button = ttk.Button(status_frame, text="نسخ\nالسجل", command=copy_log_from_status)
    copy_log_button.pack(side=tk.RIGHT, fill=tk.Y, pady=2)
    if not PYPERCLIP_AVAILABLE:
        copy_log_button.config(state=tk.DISABLED)
        update_status("زر نسخ السجل معطل لعدم توفر pyperclip.")

    if not os.path.exists(LOCAL_REPAIRED_MODS_DIR):
        try: os.makedirs(LOCAL_REPAIRED_MODS_DIR)
        except OSError as e: print(f"Could not create directory for repaired mods: {LOCAL_REPAIRED_MODS_DIR} - {e}")

    update_status("أداة الإصلاح جاهزة.")
    if not configure_repair_gemini_client() and GEMINI_AVAILABLE:
        update_status("!!! تحذير: فشل التكوين الأولي لـ Gemini. قد يتم تعطيل ميزات الإصلاح الذكي.")

    if not APP_DB_CLIENT_OK:
        update_status("!!! تحذير: فشل الاتصال بقاعدة بيانات التطبيق. تحميل المودات وتحديثها سيكون معطلاً.")
        load_mods_button.config(state=tk.DISABLED)
        if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
        if change_format_button: change_format_button.config(state=tk.DISABLED)
        upload_checkbox.config(state=tk.DISABLED)
    if not STORAGE_CLIENT_OK:
        update_status("!!! تحذير: فشل الاتصال بـ Supabase Storage. تحميل المودات الأصلية والرفع سيكون معطلاً.")
        if repair_mod_button: repair_mod_button.config(state=tk.DISABLED)
        if change_format_button: change_format_button.config(state=tk.DISABLED)
        upload_checkbox.config(state=tk.DISABLED)

    window.mainloop()

if __name__ == "__main__":
    if REPAIR_GEMINI_API_KEY == "YOUR_GEMINI_API_KEY" and GEMINI_AVAILABLE:
        print("******************************************************************************")
        print("!!! تنبيه هام: يرجى تعديل متغير REPAIR_GEMINI_API_KEY في الكود !!!")
        print("!!! ووضع مفتاح Google AI Gemini API الخاص بك لتفعيل ميزات الإصلاح الذكي. !!!")
        print("******************************************************************************")
    main_gui()
