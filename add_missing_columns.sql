-- إضافة الأعمدة المفقودة لجدول mods
-- هذا الملف يضيف العمود المفقود additional_image_urls

-- إضافة عمود additional_image_urls إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'additional_image_urls'
    ) THEN
        ALTER TABLE mods ADD COLUMN additional_image_urls JSONB DEFAULT '[]'::jsonb;
        COMMENT ON COLUMN mods.additional_image_urls IS 'Array of additional image URLs for the mod (separate from primary image)';
    END IF;
END $$;

-- إضافة عمود primary_image_url إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'primary_image_url'
    ) THEN
        ALTER TABLE mods ADD COLUMN primary_image_url TEXT;
        COMMENT ON COLUMN mods.primary_image_url IS 'Primary/main image URL for the mod';
    END IF;
END $$;

-- تحديث الفهارس
CREATE INDEX IF NOT EXISTS idx_mods_primary_image ON mods(primary_image_url);
CREATE INDEX IF NOT EXISTS idx_mods_additional_images ON mods USING GIN(additional_image_urls);

-- إظهار رسالة نجاح
DO $$ 
BEGIN
    RAISE NOTICE 'تم إضافة الأعمدة المفقودة بنجاح!';
END $$;
