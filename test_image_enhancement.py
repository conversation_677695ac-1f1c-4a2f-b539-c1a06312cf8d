#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تحسين جودة الصور
"""

import sys
import os
from io import BytesIO
from PIL import Image, ImageDraw

# إضافة مسار الملف الحالي لاستيراد الدوال
sys.path.append(os.path.dirname(__file__))

def create_test_image():
    """إنشاء صورة اختبار"""
    # إنشاء صورة ملونة مع نص
    img = Image.new('RGB', (200, 150), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # رسم بعض الأشكال
    draw.rectangle([20, 20, 80, 80], fill='red', outline='black')
    draw.ellipse([100, 30, 160, 90], fill='green', outline='black')
    draw.line([20, 100, 180, 120], fill='blue', width=3)
    
    # تحويل إلى bytes
    buffer = BytesIO()
    img.save(buffer, format='JPEG', quality=80)
    return buffer.getvalue()

def test_local_enhancement():
    """اختبار نظام التحسين المحلي"""
    print("=== اختبار نظام التحسين المحلي ===")
    
    try:
        # استيراد الدالة من الملف الرئيسي
        from mod_processor_broken_final import local_image_enhancement
        
        # إنشاء صورة اختبار
        test_image_bytes = create_test_image()
        original_size = len(test_image_bytes)
        print(f"📏 حجم الصورة الأصلية: {original_size} بايت")
        
        # اختبار التحسين التلقائي
        print("\n🔄 اختبار التحسين التلقائي...")
        enhanced_auto = local_image_enhancement("auto_enhance", test_image_bytes, "image/jpeg")
        if enhanced_auto:
            enhanced_size = len(enhanced_auto)
            print(f"✅ نجح التحسين التلقائي. الحجم الجديد: {enhanced_size} بايت")
            
            # حفظ الصورة المحسنة للمراجعة
            with open('test_auto_enhanced.jpg', 'wb') as f:
                f.write(enhanced_auto)
            print("💾 تم حفظ الصورة المحسنة في: test_auto_enhanced.jpg")
        else:
            print("❌ فشل التحسين التلقائي")
        
        # اختبار الدقة الفائقة
        print("\n🔄 اختبار الدقة الفائقة...")
        enhanced_super = local_image_enhancement("super_resolution", test_image_bytes, "image/jpeg")
        if enhanced_super:
            enhanced_size = len(enhanced_super)
            print(f"✅ نجح تحسين الدقة الفائقة. الحجم الجديد: {enhanced_size} بايت")
            
            # حفظ الصورة المحسنة للمراجعة
            with open('test_super_resolution.jpg', 'wb') as f:
                f.write(enhanced_super)
            print("💾 تم حفظ الصورة المحسنة في: test_super_resolution.jpg")
        else:
            print("❌ فشل تحسين الدقة الفائقة")
        
        # حفظ الصورة الأصلية للمقارنة
        with open('test_original.jpg', 'wb') as f:
            f.write(test_image_bytes)
        print("💾 تم حفظ الصورة الأصلية في: test_original.jpg")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_image_formats():
    """اختبار تنسيقات مختلفة من الصور"""
    print("\n=== اختبار تنسيقات مختلفة ===")
    
    try:
        from mod_processor_broken_final import local_image_enhancement
        
        # إنشاء صورة أساسية
        img = Image.new('RGB', (100, 100), color='purple')
        
        formats_to_test = [
            ('JPEG', 'image/jpeg'),
            ('PNG', 'image/png'),
            ('WEBP', 'image/webp')
        ]
        
        for format_name, content_type in formats_to_test:
            print(f"\n🔄 اختبار تنسيق {format_name}...")
            
            # تحويل الصورة إلى التنسيق المطلوب
            buffer = BytesIO()
            if format_name == 'WEBP':
                try:
                    img.save(buffer, format=format_name, quality=80)
                except Exception:
                    print(f"⚠️ تنسيق {format_name} غير مدعوم، سيتم تخطيه")
                    continue
            else:
                img.save(buffer, format=format_name)
            
            image_bytes = buffer.getvalue()
            
            # اختبار التحسين
            enhanced = local_image_enhancement("auto_enhance", image_bytes, content_type)
            
            if enhanced and len(enhanced) > 0:
                print(f"✅ نجح تحسين {format_name}")
                
                # حفظ للمراجعة
                extension = format_name.lower()
                if extension == 'jpeg':
                    extension = 'jpg'
                
                filename = f"test_enhanced_{extension}.{extension}"
                with open(filename, 'wb') as f:
                    f.write(enhanced)
                print(f"💾 تم حفظ في: {filename}")
            else:
                print(f"❌ فشل تحسين {format_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التنسيقات: {e}")
        return False

def main():
    print("اختبار شامل لنظام تحسين جودة الصور")
    print("=" * 50)
    
    # اختبار النظام الأساسي
    basic_test = test_local_enhancement()
    
    # اختبار التنسيقات المختلفة
    format_test = test_image_formats()
    
    print("\n" + "=" * 50)
    print("ملخص النتائج:")
    print("=" * 50)
    
    if basic_test:
        print("✅ نظام التحسين المحلي يعمل بشكل صحيح")
    else:
        print("❌ نظام التحسين المحلي لا يعمل")
    
    if format_test:
        print("✅ دعم التنسيقات المختلفة يعمل")
    else:
        print("❌ مشكلة في دعم التنسيقات")
    
    if basic_test and format_test:
        print("\n🎉 نظام تحسين الصور جاهز للاستخدام!")
        print("📋 الميزات المتاحة:")
        print("   • تحسين تلقائي (تحسين التباين والألوان والحدة)")
        print("   • دقة فائقة (تكبير مع تحسين الجودة)")
        print("   • دعم JPEG, PNG, WEBP")
    else:
        print("\n⚠️ يحتاج النظام إلى إصلاح")
    
    print(f"\n📁 تم إنشاء ملفات اختبار في: {os.getcwd()}")

if __name__ == "__main__":
    main()
