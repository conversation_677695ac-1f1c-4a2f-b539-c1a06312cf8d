# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل للتطبيق بعد إزالة Supabase
"""

import os
import json
import subprocess
import sys

def test_application_startup():
    """اختبار بدء تشغيل التطبيق"""
    print("🚀 اختبار بدء تشغيل التطبيق...")
    
    try:
        # تشغيل التطبيق لمدة قصيرة للتحقق من عدم وجود أخطاء
        result = subprocess.run([
            sys.executable, 
            "mod_processor_broken_final.py"
        ], 
        capture_output=True, 
        text=True, 
        timeout=10,
        encoding='utf-8'
        )
        
        output = result.stdout
        
        # التحقق من عدم وجود أخطاء Supabase
        if "supabase" in output.lower() or "bucket creation" in output.lower():
            print("❌ لا تزال هناك مراجع لـ Supabase")
            return False
        
        # التحقق من نجاح تحميل المكونات الأساسية
        success_indicators = [
            "Firebase integration module loaded successfully",
            "Gemini AI يعمل بشكل صحيح" in output or "تم تكوين Gemini بنجاح" in output,
            "Firebase Storage" in output
        ]
        
        passed_checks = sum(1 for indicator in success_indicators if 
                          (isinstance(indicator, str) and indicator in output) or 
                          (isinstance(indicator, bool) and indicator))
        
        print(f"✅ نجح {passed_checks}/{len(success_indicators)} من فحوصات البدء")
        
        if passed_checks >= 2:  # على الأقل 2 من 3 فحوصات
            print("✅ التطبيق يبدأ بنجاح")
            return True
        else:
            print("❌ فشل في بدء التطبيق بشكل صحيح")
            print("📋 مخرجات التطبيق:")
            print(output[:500] + "..." if len(output) > 500 else output)
            return False
            
    except subprocess.TimeoutExpired:
        print("✅ التطبيق بدأ بنجاح (انتهت مهلة الاختبار)")
        return True
    except Exception as e:
        print(f"❌ خطأ في اختبار بدء التطبيق: {e}")
        return False

def test_firebase_configuration():
    """اختبار إعدادات Firebase"""
    print("\n🔥 اختبار إعدادات Firebase...")
    
    # التحقق من ملفات الإعدادات
    required_files = [
        "firebase-service-account.json",
        "firebase_config.json",
        "api_keys.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {missing_files}")
        return False
    
    print("✅ جميع ملفات الإعدادات موجودة")
    
    # التحقق من محتوى ملف الخدمة
    try:
        with open("firebase-service-account.json", 'r') as f:
            service_account = json.load(f)
            
        required_keys = ["project_id", "private_key", "client_email"]
        missing_keys = [key for key in required_keys if key not in service_account]
        
        if missing_keys:
            print(f"❌ مفاتيح مفقودة في ملف الخدمة: {missing_keys}")
            return False
        
        print(f"✅ ملف خدمة Firebase صحيح - المشروع: {service_account['project_id']}")
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف خدمة Firebase: {e}")
        return False
    
    # التحقق من إعدادات Firebase
    try:
        with open("firebase_config.json", 'r') as f:
            firebase_config = json.load(f)
            
        if "storage_bucket" in firebase_config and "project_id" in firebase_config:
            print("✅ إعدادات Firebase محدثة بشكل صحيح")
            return True
        else:
            print("❌ إعدادات Firebase غير مكتملة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قراءة إعدادات Firebase: {e}")
        return False

def test_supabase_removal():
    """اختبار إزالة Supabase"""
    print("\n🗑️ اختبار إزالة Supabase...")
    
    # التحقق من ملفات الإعدادات
    try:
        with open("api_keys.json", 'r') as f:
            api_keys = json.load(f)
        
        supabase_keys = [key for key in api_keys.keys() if "supabase" in key.lower()]
        
        if supabase_keys:
            print(f"⚠️ لا تزال هناك مفاتيح Supabase: {supabase_keys}")
            print("💡 هذا لا يؤثر على التطبيق لأنها لا تُستخدم")
        else:
            print("✅ تم إزالة جميع مفاتيح Supabase")
        
        with open("database_config.json", 'r') as f:
            db_config = json.load(f)
        
        if "supabase" not in str(db_config).lower():
            print("✅ تم إزالة إعدادات Supabase من قاعدة البيانات")
            return True
        else:
            print("⚠️ لا تزال هناك مراجع لـ Supabase في إعدادات قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص إزالة Supabase: {e}")
        return False

def test_gemini_availability():
    """اختبار توفر Gemini AI"""
    print("\n🤖 اختبار توفر Gemini AI...")
    
    try:
        with open("api_keys.json", 'r') as f:
            api_keys = json.load(f)
        
        gemini_keys = api_keys.get("gemini_api_keys", [])
        
        if not gemini_keys:
            print("❌ لا توجد مفاتيح Gemini")
            return False
        
        print(f"✅ متوفر {len(gemini_keys)} مفتاح Gemini")
        
        # اختبار بسيط لمفتاح واحد
        try:
            import google.generativeai as genai
            genai.configure(api_key=gemini_keys[0])
            model = genai.GenerativeModel('gemini-2.0-flash-exp')
            response = model.generate_content("test")
            print("✅ Gemini AI يعمل بشكل صحيح")
            return True
        except Exception as gemini_e:
            print(f"⚠️ خطأ في اختبار Gemini: {gemini_e}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص Gemini: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار نهائي شامل للتطبيق")
    print("=" * 60)
    
    tests = [
        ("إعدادات Firebase", test_firebase_configuration),
        ("إزالة Supabase", test_supabase_removal),
        ("توفر Gemini AI", test_gemini_availability),
        ("بدء تشغيل التطبيق", test_application_startup),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية:")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {test_name}: {'نجح' if status else 'فشل'}")
    
    print(f"\n📈 معدل النجاح: {passed_tests}/{total_tests} ({(passed_tests/total_tests)*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ التطبيق جاهز للاستخدام بدون Supabase")
        print("🔥 Firebase هو الآن قاعدة البيانات والتخزين الوحيدة")
    elif passed_tests >= total_tests * 0.75:  # 75% نجاح
        print("\n✅ معظم الاختبارات نجحت!")
        print("⚠️ قد تحتاج لحل بعض المشاكل الطفيفة")
    else:
        print("\n⚠️ بعض الاختبارات فشلت")
        print("💡 راجع الرسائل أعلاه لحل المشاكل")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
