#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إنشاء bucket في Supabase
"""

import json
import os
from supabase import create_client, Client

def test_bucket_creation():
    """اختبار إنشاء bucket في Supabase"""
    
    # تحميل الإعدادات
    try:
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ ملف api_keys.json غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الإعدادات: {e}")
        return False
    
    # الحصول على معلومات Supabase
    supabase_url = "https://ytqxxodyecdeosnqoure.supabase.co"
    supabase_key = config.get("SUPABASE_KEY", "")
    
    if not supabase_key:
        print("❌ مفتاح Supabase غير موجود في الإعدادات")
        return False
    
    try:
        # إنشاء عميل Supabase
        print("🔄 إنشاء عميل Supabase...")
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # اختبار الاتصال أولاً
        print("🔄 اختبار الاتصال...")
        try:
            buckets = supabase.storage.list_buckets()
            print(f"✅ نجح الاتصال. عدد buckets الموجودة: {len(buckets)}")
            
            # طباعة أسماء buckets الموجودة
            if buckets:
                bucket_names = [bucket['name'] for bucket in buckets]
                print(f"📋 Buckets الموجودة: {bucket_names}")
            
        except Exception as conn_e:
            print(f"❌ فشل الاتصال: {conn_e}")
            return False
        
        # اختبار إنشاء bucket جديد
        test_bucket_name = "mods"
        existing_bucket_names = [bucket['name'] for bucket in buckets]
        
        if test_bucket_name not in existing_bucket_names:
            print(f"🔄 محاولة إنشاء bucket: {test_bucket_name}")
            try:
                # الطريقة الصحيحة لإنشاء bucket حسب توثيق storage3
                result = supabase.storage.create_bucket(id=test_bucket_name, options={'public': True})
                print(f"✅ تم إنشاء bucket بنجاح: {test_bucket_name}")
                print(f"📋 نتيجة الإنشاء: {result}")

            except Exception as create_e:
                print(f"❌ فشل إنشاء bucket: {create_e}")
                return False
        else:
            print(f"✅ bucket موجود مسبقاً: {test_bucket_name}")
        
        # اختبار bucket للصور أيضاً
        image_bucket_name = "images"
        if image_bucket_name not in existing_bucket_names:
            print(f"🔄 محاولة إنشاء bucket للصور: {image_bucket_name}")
            try:
                result = supabase.storage.create_bucket(id=image_bucket_name, options={'public': True})
                print(f"✅ تم إنشاء bucket للصور بنجاح: {image_bucket_name}")

            except Exception as create_e:
                print(f"❌ فشل إنشاء bucket للصور: {create_e}")
        else:
            print(f"✅ bucket للصور موجود مسبقاً: {image_bucket_name}")
        
        # التحقق النهائي من buckets
        print("🔄 التحقق النهائي من buckets...")
        final_buckets = supabase.storage.list_buckets()
        final_bucket_names = [bucket['name'] for bucket in final_buckets]
        print(f"📋 Buckets النهائية: {final_bucket_names}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("=== اختبار إنشاء Bucket في Supabase ===")
    success = test_bucket_creation()
    if success:
        print("✅ اكتمل الاختبار بنجاح")
    else:
        print("❌ فشل الاختبار")
