# دليل إعداد Supabase Storage

## المشكلة
عند محاولة رفع الملفات، تظهر رسالة خطأ:
```
Bucket 'mods' not found. Creating it...
⚠️ Error checking/creating bucket: {'statusCode': 400, 'error': Error, 'message': body/name must be string}
```

## السبب
التطبيق يحاول إنشاء buckets في Supabase تلقائياً، لكن هذا يتطلب صلاحيات admin غير متوفرة للمفتاح العادي.

## الحل

### الخيار 1: إنشاء Buckets يدوياً (الأسهل)

1. **اذهب إلى لوحة تحكم Supabase:**
   ```
   https://ytqxxodyecdeosnqoure.supabase.co/project/default/storage/buckets
   ```

2. **أنشئ bucket للمودات:**
   - ان<PERSON>ر على "New bucket"
   - Name: `mods`
   - ✅ فعل "Public bucket"
   - انقر "Create bucket"

3. **أنشئ bucket للصور:**
   - ان<PERSON>ر على "New bucket" مرة أخرى
   - Name: `images`
   - ✅ فعل "Public bucket"
   - انقر "Create bucket"

4. **تأكد من النتيجة:**
   - يجب أن ترى bucket اسمه `mods`
   - يجب أن ترى bucket اسمه `images`
   - كلاهما يجب أن يكون "Public"

### الخيار 2: استخدام Service Key (للمطورين المتقدمين)

1. **احصل على Service Key:**
   - اذهب إلى: `https://supabase.com/dashboard/project/[project-id]/settings/api`
   - انسخ "service_role" key (ليس anon key)

2. **أضف المفتاح إلى api_keys.json:**
   ```json
   {
     "SUPABASE_KEY": "your_anon_key_here",
     "SUPABASE_SERVICE_KEY": "your_service_key_here"
   }
   ```

3. **شغل أداة إنشاء Buckets:**
   ```bash
   python create_supabase_buckets.py
   ```

### الخيار 3: استخدام Firebase Storage (البديل)

إذا كنت لا تريد استخدام Supabase، يمكنك الاعتماد على Firebase Storage فقط:

1. **تأكد من تهيئة Firebase:**
   - ملف `firebase-service-account.json` موجود
   - Firebase Storage مُهيأ بشكل صحيح

2. **التطبيق سيستخدم Firebase تلقائياً** عند فشل Supabase

## التحقق من النجاح

بعد إنشاء buckets، شغل التطبيق مرة أخرى. يجب أن ترى:
```
✅ Bucket 'mods' found.
✅ تم رفع الملف بنجاح إلى Firebase Storage
```

## ملاحظات مهمة

- **Buckets يجب أن تكون Public** لتعمل روابط التحميل
- **أسماء Buckets حساسة للأحرف** - استخدم `mods` و `images` بالضبط
- **Firebase Storage يعمل كبديل** إذا فشل Supabase
- **لا تشارك Service Key** - استخدمه فقط للإعداد الأولي

## استكشاف الأخطاء

### إذا ظهرت رسالة "403 Unauthorized"
- تأكد من أن buckets موجودة
- تأكد من أن buckets مُعينة كـ Public
- تحقق من صحة SUPABASE_KEY في api_keys.json

### إذا ظهرت رسالة "Bucket not found"
- تأكد من إنشاء buckets بالأسماء الصحيحة: `mods` و `images`
- تحقق من أن buckets مرئية في لوحة التحكم

### إذا فشل كل شيء
- التطبيق سيستخدم Firebase Storage تلقائياً
- تأكد من تهيئة Firebase بشكل صحيح
